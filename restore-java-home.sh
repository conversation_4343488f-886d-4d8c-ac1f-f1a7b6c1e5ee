#!/bin/bash

# This script restores the original JAVA_HOME
# Run this script with 'source restore-java-home.sh' after you're done with gradle commands

# Restore original JAVA_HOME if it was saved
if [ -n "$OLD_JAVA_HOME" ]; then
  export JAVA_HOME=$OLD_JAVA_HOME
  export PATH=$JAVA_HOME/bin:$PATH
  unset OLD_JAVA_HOME
  echo "Restored original Java version:"
  java -version
else
  echo "No previous JAVA_HOME was saved. Nothing to restore."
fi
