09-23 12:09:15.414  1008  1724 D EGL_emulation: app_time_stats: avg=34924.52ms min=34924.52ms max=34924.52ms count=1
--------- beginning of system
09-23 12:09:15.498   492   774 I ActivityTaskManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.abinbev.oasis/.activities.StartUpActivity bnds=[832,1873][1005,2068]} from uid 10149
09-23 12:09:15.568   492   510 D CompatibilityChangeReporter: Compat change id reported: 135634846; UID 10175; state: DISABLED
09-23 12:09:15.568   492   510 D CompatibilityChangeReporter: Compat change id reported: 177438394; UID 10175; state: DISABLED
09-23 12:09:15.568   492   510 D CompatibilityChangeReporter: Compat change id reported: 135772972; UID 10175; state: DISABLED
09-23 12:09:15.568   492   510 D CompatibilityChangeReporter: Compat change id reported: 135754954; UID 10175; state: ENABLED
09-23 12:09:15.568   492   519 D CompatibilityChangeReporter: Compat change id reported: 143937733; UID 10175; state: ENABLED
09-23 12:09:15.574  1008  1060 D QuickstepModelDelegate: notifyAppTargetEvent action=1 launchLocation=predictions/hotseat
09-23 12:09:15.575   301   301 D Zygote  : Forked child process 14054
09-23 12:09:15.576   492   519 I ActivityManager: Start proc 14054:com.abinbev.oasis/u0a175 for next-top-activity {com.abinbev.oasis/com.abinbev.oasis.activities.StartUpActivity}
09-23 12:09:15.584 14054 14054 I m.abinbev.oasis: Using CollectorTypeCC GC.
09-23 12:09:15.589   780   848 W Parcel  : Expecting binder but got null!
09-23 12:09:15.590   492  2308 D CoreBackPreview: Window{b5c7fc u0 Splash Screen com.abinbev.oasis}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@e30b4da, mPriority=0}
09-23 12:09:15.590 14054 14054 E m.abinbev.oasis: Not starting debugger since process cannot load the jdwp agent.
09-23 12:09:15.600 14054 14054 D CompatibilityChangeReporter: Compat change id reported: 171979766; UID 10175; state: ENABLED
09-23 12:09:15.605   492  2308 W BatteryExternalStatsWorker: error reading Bluetooth stats: 11
09-23 12:09:15.606   780   848 E OpenGLRenderer: Unable to match the desired swap behavior.
09-23 12:09:15.612   492  2307 D TelephonyManager: requestModemActivityInfo: Sending result to app: ModemActivityInfo{ mTimestamp=960241 mSleepTimeMs=0 mIdleTimeMs=955012 mActivityStatsTechSpecificInfo=[{mRat=UNKNOWN,mFrequencyRange=UNKNOWN,mTxTimeMs[]=[0, 0, 0, 0, 0],mRxTimeMs=0}]}
09-23 12:09:15.614   492  1218 W Binder  : Caught a RuntimeException from the binder stub implementation.
09-23 12:09:15.614   492  1218 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
09-23 12:09:15.614   492  1218 W Binder  :      at android.util.ArraySet.valueAt(ArraySet.java:422)
09-23 12:09:15.614   492  1218 W Binder  :      at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
09-23 12:09:15.614   492  1218 W Binder  :      at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
09-23 12:09:15.614   492  1218 W Binder  :      at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
09-23 12:09:15.614   492  1218 W Binder  :      at android.os.Binder.execTransactInternal(Binder.java:1285)
09-23 12:09:15.614   492  1218 W Binder  :      at android.os.Binder.execTransact(Binder.java:1244)
09-23 12:09:15.623 14054 14054 D nativeloader: Configuring classloader-namespace for other apk /data/app/~~qBkRvP1Kb3Mpu0Rt3Qx2xg==/com.abinbev.oasis-_JBvfAayQREjO6rcnH0c-w==/base.apk. target_sdk_version=33, uses_libraries=, library_path=/data/app/~~qBkRvP1Kb3Mpu0Rt3Qx2xg==/com.abinbev.oasis-_JBvfAayQREjO6rcnH0c-w==/lib/arm64:/data/app/~~qBkRvP1Kb3Mpu0Rt3Qx2xg==/com.abinbev.oasis-_JBvfAayQREjO6rcnH0c-w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.abinbev.oasis
09-23 12:09:15.627 14054 14054 V GraphicsEnvironment: ANGLE Developer option for 'com.abinbev.oasis' set to: 'default'
09-23 12:09:15.627 14054 14054 V GraphicsEnvironment: ANGLE GameManagerService for com.abinbev.oasis: false
09-23 12:09:15.628 14054 14054 V GraphicsEnvironment: Neither updatable production driver nor prerelease driver is supported.
09-23 12:09:15.630 14054 14054 D NetworkSecurityConfig: Using Network Security Config from resource network_security_config debugBuild: false
09-23 12:09:15.633 14054 14054 D NetworkSecurityConfig: Using Network Security Config from resource network_security_config debugBuild: false
09-23 12:09:15.636 14054 14054 W ComponentDiscovery: Class com.google.firebase.dynamicloading.DynamicLoadingRegistrar is not an found.
09-23 12:09:15.637 14054 14054 D CompatibilityChangeReporter: Compat change id reported: 183155436; UID 10175; state: ENABLED
09-23 12:09:15.638 14054 14054 I FirebaseApp: Device unlocked: initializing all Firebase APIs for app [DEFAULT]
09-23 12:09:15.640 14054 14054 I FirebaseCrashlytics: Initializing Firebase Crashlytics 17.3.1
09-23 12:09:15.643 14054 14074 W m.abinbev.oasis: ClassLoaderContext classpath size mismatch. expected=15, found=0 (DLC[];PCL[base.apk***********:base.apk!classes2.dex***********:base.apk!classes3.dex***********:base.apk!classes4.dex***********:base.apk!classes5.dex***********:base.apk!classes6.dex***********:base.apk!classes7.dex*166583448:base.apk!classes8.dex***********:base.apk!classes9.dex***********:base.apk!classes10.dex***********:base.apk!classes11.dex***********:base.apk!classes12.dex***********:base.apk!classes13.dex*393638121:base.apk!classes14.dex***********:base.apk!classes15.dex*523121220]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*373201995]#PCL[/system/framework/com.android.location.provider.jar*989331188]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
09-23 12:09:15.644 14054 14074 I DynamiteModule: Considering local module com.google.android.gms.measurement.dynamite:46 and remote module com.google.android.gms.measurement.dynamite:158
09-23 12:09:15.645 14054 14074 I DynamiteModule: Selected remote version of com.google.android.gms.measurement.dynamite, version >= 158
09-23 12:09:15.645 14054 14074 V DynamiteModule: Dynamite loader version >= 2, using loadModule2NoCrashUtils
61   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.667 14054 14074 W System  : ClassLoader referenced unknown path: 
09-23 12:09:15.668 14054 14054 I FirebaseInitProvider: FirebaseApp initialization successful
09-23 12:09:15.671 14054 14074 D nativeloader: Configuring classloader-namespace for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~pfSybMr4WxLI7RRUa8a1lA==/com.google.android.gms--2uFQZS_czti2xLWttDC6Q==/lib/arm64:/data/app/~~pfSybMr4WxLI7RRUa8a1lA==/com.google.android.gms--2uFQZS_czti2xLWttDC6Q==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
09-23 12:09:15.677 14054 14074 W m.abinbev.oasis: ClassLoaderContext classpath size mismatch. expected=15, found=2 (DLC[];PCL[base.apk***********:base.apk!classes2.dex***********:base.apk!classes3.dex***********:base.apk!classes4.dex***********:base.apk!classes5.dex***********:base.apk!classes6.dex***********:base.apk!classes7.dex*166583448:base.apk!classes8.dex***********:base.apk!classes9.dex***********:base.apk!classes10.dex***********:base.apk!classes11.dex***********:base.apk!classes12.dex***********:base.apk!classes13.dex*393638121:base.apk!classes14.dex***********:base.apk!classes15.dex*523121220]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*373201995]#PCL[/system/framework/com.android.location.provider.jar*989331188]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[/data/app/~~qBkRvP1Kb3Mpu0Rt3Qx2xg==/com.abinbev.oasis-_JBvfAayQREjO6rcnH0c-w==/base.apk*771577899:/data/app/~~qBkRvP1Kb3Mpu0Rt3Qx2xg==/com.abinbev.oasis-_JBvfAayQREjO6rcnH0c-w==/base.apk!classes2.dex***********])
09-23 12:09:15.678   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.684 14054 14054 D StartUpActivity: onCreate started
09-23 12:09:15.693  1566  1566 I GsaVoiceInteractionSrv: Handling ACTION_STOP_HOTWORD
09-23 12:09:15.695   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.695 14054 14093 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
09-23 12:09:15.696   492  6847 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_0 
09-23 12:09:15.696 14054 14093 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
09-23 12:09:15.700   492  6847 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
09-23 12:09:15.700   492  6847 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
09-23 12:09:15.701 14054 14093 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
09-23 12:09:15.701  1566  1991 I HwDetectorWithState: (REDACTED) a: %d
09-23 12:09:15.702   492  6847 D EGL_emulation: eglCreateContext: 0xb400007ba74715d0: maj 3 min 0 rcv 3
09-23 12:09:15.702   492  6847 D EGL_emulation: eglMakeCurrent: 0xb400007ba74715d0: ver 3 0 (tinfo 0x7dc0d7f180) (first time)
09-23 12:09:15.705   492  1218 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_0 
09-23 12:09:15.706 14054 14054 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10175; state: ENABLED
09-23 12:09:15.708 14054 14054 D StartUpActivity: Layout set successfully
09-23 12:09:15.709 14054 14054 D StartUpActivity: onCreate completed successfully
09-23 12:09:15.711 14054 14054 D StartUpActivity: onResume started
09-23 12:09:15.711   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.713   492   774 I ActivityTaskManager: START u0 {act=android.content.pm.action.REQUEST_PERMISSIONS pkg=com.google.android.permissioncontroller cmp=com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity (has extras)} from uid 10175
09-23 12:09:15.713   492   774 D CompatibilityChangeReporter: Compat change id reported: 174042980; UID 10175; state: DISABLED
09-23 12:09:15.716 14054 14054 D StartUpActivity: onResume completed
09-23 12:09:15.718 14054 14054 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10175; state: DISABLED
09-23 12:09:15.721 14054 14091 W Parcel  : Expecting binder but got null!
09-23 12:09:15.721   492   774 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10175; state: DISABLED
09-23 12:09:15.721   492   774 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10175; state: DISABLED
09-23 12:09:15.722   492   774 D CoreBackPreview: Window{d30766 u0 com.abinbev.oasis/com.abinbev.oasis.activities.StartUpActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@7697b54, mPriority=0}
09-23 12:09:15.723 14054 14096 E m.abinbev.oasis: No package ID 6a found for ID 0x6a0b000f.
09-23 12:09:15.723   492  1218 W ActivityTaskManager: Tried to set launchTime (0) < mLastActivityLaunchTime (991591)
09-23 12:09:15.723 14054 14096 I FA      : App measurement initialized, version: 136005
09-23 12:09:15.723 14054 14096 I FA      : To enable debug logging run: adb shell setprop log.tag.FA VERBOSE
09-23 12:09:15.723 14054 14096 I FA      : To enable faster debug mode event logging run:
09-23 12:09:15.723 14054 14096 I FA      :   adb shell setprop debug.firebase.analytics.app com.abinbev.oasis
09-23 12:09:15.728   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.729   492  1218 D CompatibilityChangeReporter: Compat change id reported: 181136395; UID 10175; state: DISABLED
09-23 12:09:15.729   492  1218 D CompatibilityChangeReporter: Compat change id reported: 174042936; UID 10175; state: DISABLED
09-23 12:09:15.732 11750 11832 I PhenotypeResourceReader: unable to find any Phenotype resource metadata for com.abinbev.oasis
09-23 12:09:15.734 10685 10685 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.measurement.START pkg=com.google.android.gms }
09-23 12:09:15.734 14054 14096 I FA      : Tag Manager is not found and thus will not be used
09-23 12:09:15.734 10685 10685 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.measurement.START pkg=com.google.android.gms }
09-23 12:09:15.745   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.745 14054 14091 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_0 
09-23 12:09:15.747 14054 14091 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
09-23 12:09:15.747 14054 14091 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
09-23 12:09:15.748 14054 14091 D EGL_emulation: eglCreateContext: 0xb400007ba7439950: maj 3 min 0 rcv 3
09-23 12:09:15.749 14054 14091 D EGL_emulation: eglMakeCurrent: 0xb400007ba7439950: ver 3 0 (tinfo 0x7dc0c02080) (first time)
09-23 12:09:15.759   492  2307 D CoreBackPreview: Window{8a21616 u0 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@58c6184, mPriority=0}
09-23 12:09:15.762   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.767  5056  5056 E GrantPermissionsViewModel: None of [android.permission.READ_EXTERNAL_STORAGE, android.permission.WRITE_EXTERNAL_STORAGE] in {android.permission-group.READ_MEDIA_VISUAL=[android.permission.READ_MEDIA_IMAGES, android.permission.READ_MEDIA_VIDEO], android.permission-group.READ_MEDIA_AURAL=[android.permission.READ_MEDIA_AUDIO], android.permission-group.NOTIFICATIONS=[android.permission.POST_NOTIFICATIONS], android.permission-group.CAMERA=[android.permission.CAMERA], android.permission-group.PHONE=[android.permission.READ_PHONE_STATE], android.permission-group.LOCATION=[android.permission.ACCESS_FINE_LOCATION, android.permission.ACCESS_COARSE_LOCATION]}
09-23 12:09:15.768   148   148 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
09-23 12:09:15.770   492   507 I ActivityTaskManager: Displayed com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity: +269ms
09-23 12:09:15.771 14054 14091 I Gralloc4: mapper 4.x is not supported
09-23 12:09:15.774   148   148 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
09-23 12:09:15.775   147   147 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
09-23 12:09:15.775 14054 14091 W Gralloc4: allocator 4.x is not supported
09-23 12:09:15.777 14054 14091 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_0 
09-23 12:09:15.777 14054 14091 E OpenGLRenderer: Unable to match the desired swap behavior.
09-23 12:09:15.778   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.795   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.813   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.814 14054 14054 D StartUpActivity: FCM Token: FCM Registration Token: d4STo7R7TpOjrIQU7kneRU:APA91bH_JdZAjO-n05A-4nC-6RxtGS96XJaLIW-T8I-sMVfEXGnwGNDgmkj_Ayl_wZclFoViQkmsCAHKqB9TDcYvNATxDW_jj1lNgs4lBMTDm-VhnA-YwsE
09-23 12:09:15.814 14054 14054 D CompatibilityChangeReporter: Compat change id reported: 78294732; UID 10175; state: ENABLED
09-23 12:09:15.827  1541  1945 I AiAiEcho: Predicting[0]: 
09-23 12:09:15.827  1566  1566 I GsaVoiceInteractionSrv: Handling ACTION_START_HOTWORD
09-23 12:09:15.827  1541  1945 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata: 
09-23 12:09:15.827  1566  1993 I GsaVoiceInteractionSrv: (REDACTED) disregardVoiceMatch: %b
09-23 12:09:15.827  1566  1993 I GsaVoiceInteractionSrv: Cannot start hotword, hotword has been explicitly disabled.
09-23 12:09:15.832  1008  1060 D QuickstepModelDelegate: notifyAppTargetEvent action=1 launchLocation=workspace/0/[-1,-1]/[1,1]
09-23 12:09:15.834   492   774 D WindowManager: relayoutVisibleWindow: Window{b5c7fc u0 Splash Screen com.abinbev.oasis EXITING} mAnimatingExit=true, mRemoveOnExit=true, mDestroying=false
09-23 12:09:15.837   492  1568 W AppSearchIcing: icing-search-engine.cc:217: Error: 5, Message: Document (com.google.android.googlequicksearchbox$OneSearchZeroStateGoogleSuggestions/default, zp) not found.
09-23 12:09:15.841   342   430 E SurfaceFlinger: Only WindowManager is allowed to use eEarlyWakeup[Start|End] flags
09-23 12:09:15.844 10685 14103 D AdvertisingIdClient: AdvertisingIdClient already created.
09-23 12:09:15.844 10685 14103 D AdvertisingIdClient: AdvertisingIdClient is not bounded. Starting to bind it...
09-23 12:09:15.845   319  6842 I RanchuHwc: validateDisplay: layer 123 CompositionType 1, fallback to client
09-23 12:09:15.845   319  6842 I RanchuHwc: validateDisplay: layer 122 CompositionType 1, fallback to client
09-23 12:09:15.845   319  6842 I RanchuHwc: validateDisplay: layer 121 CompositionType 1, fallback to client
09-23 12:09:15.845   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.855 10685 14103 D AdvertisingIdClient: AdvertisingIdClient is bounded
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider: Failed to fetch suggestions from AppSearch, fallback to AGA
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider: java.util.concurrent.CompletionException: java.util.NoSuchElementException: No value present
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:307)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:322)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.concurrent.CompletableFuture$UniAccept.tryFire(CompletableFuture.java:680)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:482)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2064)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at com.google.android.apps.nexuslauncher.allapps.J.onResult(Unknown Source:59)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.app.appsearch.SearchSessionUtil$1.lambda$onResult$0(SearchSessionUtil.java:128)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.app.appsearch.SearchSessionUtil$1.$r8$lambda$_nlk9WiPjSfaAsqKYmp0aPvSRC0(Unknown Source:0)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.app.appsearch.AppSearchSession$4$$ExternalSyntheticLambda1.run(Unknown Source:26)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.os.Handler.handleCallback(Handler.java:942)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.os.Handler.dispatchMessage(Handler.java:99)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.os.Looper.loopOnce(Looper.java:201)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.os.Looper.loop(Looper.java:288)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at android.os.HandlerThread.run(HandlerThread.java:67)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider: Caused by: java.util.NoSuchElementException: No value present
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at java.util.Optional.get(Optional.java:144)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      at com.google.android.apps.nexuslauncher.allapps.J.onResult(Unknown Source:16)
09-23 12:09:15.856  1008  1888 E OneSearchSuggestProvider:      ... 8 more
09-23 12:09:15.856  1008  1888 D OneSearchSuggestProvider: Established or reuse existing binder channel authority=com.google.android.googlequicksearchbox
09-23 12:09:15.856 10685 14103 I AdvertisingIdClient: shouldSendLog 909878
09-23 12:09:15.856 10685 14103 I AdvertisingIdClient: GetInfoInternal elapse 12ms
09-23 12:09:15.857 11674 11674 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227 
09-23 12:09:15.860 11674 11674 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
09-23 12:09:15.860 11674 11674 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.google.android.apps.nexuslauncher fieldId=-1 fieldName=null extras=null}, false)
09-23 12:09:15.860 11674 11674 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
09-23 12:09:15.861 11674 11674 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
09-23 12:09:15.862   319  6842 I RanchuHwc: validateDisplay: layer 123 CompositionType 1, fallback to client
09-23 12:09:15.862   319  6842 I RanchuHwc: validateDisplay: layer 122 CompositionType 1, fallback to client
09-23 12:09:15.862   319  6842 I RanchuHwc: validateDisplay: layer 121 CompositionType 1, fallback to client
09-23 12:09:15.862   319  6842 I RanchuHwc: validateDisplay: layer 119 CompositionType 1, fallback to client
09-23 12:09:15.870  1541  1945 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
09-23 12:09:15.870  1541  1945 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
09-23 12:09:15.874 11750 11750 D BoundBrokerSvc: onRebind: Intent { act=com.google.android.gms.presencemanager.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
09-23 12:09:15.875 11750 11750 D BoundBrokerSvc: onRebind: Intent { act=com.google.android.gms.presencemanager.service.INTERNAL_IDENTITY dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
09-23 12:09:15.878   319  6842 I RanchuHwc: validateDisplay: layer 123 CompositionType 1, fallback to client
09-23 12:09:15.878   319  6842 I RanchuHwc: validateDisplay: layer 122 CompositionType 1, fallback to client
09-23 12:09:16.113   342   430 E SurfaceFlinger: Only WindowManager is allowed to use eEarlyWakeup[Start|End] flags
09-23 12:09:16.182   492  2307 D CoreBackPreview: Window{b5c7fc u0 Splash Screen com.abinbev.oasis EXITING}: Setting back callback null
09-23 12:09:16.183   492   774 W InputManager-JNI: Input channel object 'b5c7fc Splash Screen com.abinbev.oasis (client)' was disposed without first being removed with the input manager!
09-23 12:09:16.405 10685 10685 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.notifications.registration.service.NotificationsRegistrationTaskBoundService.ACTION_TASK_READY dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsInternalBoundBrokerService }
09-23 12:09:16.405 10685 10685 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.notifications.registration.service.NotificationsRegistrationTaskBoundService.ACTION_TASK_READY dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsInternalBoundBrokerService }
09-23 12:09:16.408 10685 10685 I GmsCoreXrpcWrapper: (REDACTED) Returning a channel provider with trafficStatsTag=%d trafficStatsUid=%d
09-23 12:09:16.414  1008  1724 D EGL_emulation: app_time_stats: avg=14.85ms min=0.79ms max=23.12ms count=57
09-23 12:09:16.419 11750 13918 I NetworkScheduler.Stats: (REDACTED) Task %s/%s started execution. cause:%s exec_start_elapsed_seconds: %s
09-23 12:09:16.427 10685 11230 D TrafficStats: tagSocket(96) with statsTag=0x4003300, statsUid=10175
09-23 12:09:16.430 10685 11230 D TrafficStats: tagSocket(96) with statsTag=0x4003300, statsUid=10175
09-23 12:09:16.433 10685 14112 W FA-SVC  : Gms url request failed, app: com.abinbev.oasis, GOOGLE_ANALYTICS, m1.mh: Exception in CronetUrlRequest: net::ERR_CONNECTION_REFUSED, ErrorCode=7, InternalErrorCode=-102, Retryable=false
09-23 12:09:16.434 10685 14103 W FA-SVC  : Network upload failed. Will retry later. code, error: 0, m1.mh: Exception in CronetUrlRequest: net::ERR_CONNECTION_REFUSED, ErrorCode=7, InternalErrorCode=-102, Retryable=false
09-23 12:09:16.437 10685 10685 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.auth.service.START pkg=com.google.android.gms }
09-23 12:09:16.437 10685 10685 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.auth.service.START pkg=com.google.android.gms }
09-23 12:09:16.438   492  2308 W InputManager-JNI: Input channel object '8a21616 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity (client)' was disposed without first being removed with the input manager!
09-23 12:09:16.447   492  1218 W WindowManager: Failed looking up window session=Session{2e3e035 5056:u0a10165} callers=com.android.server.wm.WindowManagerService.windowForClientLocked:6050 com.android.server.wm.Session.setOnBackInvokedCallbackInfo:943 android.view.IWindowSession$Stub.onTransact:1180 
09-23 12:09:16.447   492  1218 E WindowManager: setOnBackInvokedCallback(): No window state for package:com.google.android.permissioncontroller
09-23 12:09:16.451   492  2307 D CoreBackPreview: Window{d30766 u0 com.abinbev.oasis/com.abinbev.oasis.activities.StartUpActivity}: Setting back callback null
09-23 12:09:16.451   492   774 W InputManager-JNI: Input channel object 'd30766 com.abinbev.oasis/com.abinbev.oasis.activities.StartUpActivity (client)' was disposed without first being removed with the input manager!
09-23 12:09:16.453 11750 12268 W GLSUser : [DeviceKeyStore] Cannot load key: Device key file not found.
09-23 12:09:16.455   492  4681 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_0 
09-23 12:09:16.455   492  4681 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
09-23 12:09:16.456   492  4681 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
09-23 12:09:16.456   492  4681 D EGL_emulation: eglCreateContext: 0xb400007ba7483810: maj 3 min 0 rcv 3
09-23 12:09:16.457   492  4681 D EGL_emulation: eglMakeCurrent: 0xb400007ba7483810: ver 3 0 (tinfo 0x7dc0d7f100) (first time)
09-23 12:09:16.480 13062 13074 W libc    : Access denied finding property "persist.adb.tls_server.enable"
09-23 12:09:16.477 13062 13062 W binder:13062_1: type=1400 audit(0.0:453): avc: denied { read } for name="u:object_r:system_adbd_prop:s0" dev="tmpfs" ino=259 scontext=u:r:gmscore_app:s0:c512,c768 tcontext=u:object_r:system_adbd_prop:s0 tclass=file permissive=0 app=com.google.android.gms
09-23 12:09:16.489 13062 13074 D TrafficStats: tagSocket(76) with statsTag=0x1809, statsUid=-1
09-23 12:09:16.510 11750 12268 W GmsUrlRequestImpl: [https://android.googleapis.com/auth/devicekey] Content-Type header is overwritten.
09-23 12:09:16.536 11750 11994 D TrafficStats: tagSocket(167) with statsTag=0x4000401, statsUid=10120
09-23 12:09:16.741 13062 13062 W ThreadPoolForeg: type=1400 audit(0.0:454): avc: denied { write } for name="traced_producer" dev="tmpfs" ino=647 scontext=u:r:gmscore_app:s0:c512,c768 tcontext=u:object_r:traced_producer_socket:s0 tclass=sock_file permissive=0 app=com.google.android.gms
09-23 12:09:16.930   780   848 D EGL_emulation: app_time_stats: avg=16.57ms min=11.00ms max=37.60ms count=60
09-23 12:09:16.952 11750 12268 W GLSUser : [AppCertManager] Exception while requesting key: 
09-23 12:09:16.952 11750 12268 W GLSUser : java.util.concurrent.ExecutionException: clhu: Parse Proto Exception with response code 400
09-23 12:09:16.952 11750 12268 W GLSUser :      at gmwx.j(:com.google.android.gms@*********@25.35.34 (190400-*********):21)
09-23 12:09:16.952 11750 12268 W GLSUser :      at gmxg.u(:com.google.android.gms@*********@25.35.34 (190400-*********):71)
09-23 12:09:16.952 11750 12268 W GLSUser :      at aiii.b(:com.google.android.gms@*********@25.35.34 (190400-*********):439)
09-23 12:09:16.952 11750 12268 W GLSUser :      at aiig.a(:com.google.android.gms@*********@25.35.34 (190400-*********):67)
09-23 12:09:16.952 11750 12268 W GLSUser :      at aiig.fj(:com.google.android.gms@*********@25.35.34 (190400-*********):16)
09-23 12:09:16.952 11750 12268 W GLSUser :      at wdk.onTransact(:com.google.android.gms@*********@25.35.34 (190400-*********):97)
09-23 12:09:16.952 11750 12268 W GLSUser :      at android.os.Binder.transact(Binder.java:1164)
09-23 12:09:16.952 11750 12268 W GLSUser :      at ccqt.onTransact(:com.google.android.gms@*********@25.35.34 (190400-*********):10)
09-23 12:09:16.952 11750 12268 W GLSUser :      at android.os.Binder.transact(Binder.java:1164)
09-23 12:09:16.952 11750 12268 W GLSUser :      at bsgz.onTransact(:com.google.android.gms@*********@25.35.34 (190400-*********):157)
09-23 12:09:16.952 11750 12268 W GLSUser :      at android.os.Binder.execTransactInternal(Binder.java:1280)
09-23 12:09:16.952 11750 12268 W GLSUser :      at android.os.Binder.execTransact(Binder.java:1244)
09-23 12:09:16.952 11750 12268 W GLSUser : Caused by: clhu: Parse Proto Exception with response code 400
09-23 12:09:16.952 11750 12268 W GLSUser :      at clix.b(:com.google.android.gms@*********@25.35.34 (190400-*********):71)
09-23 12:09:16.952 11750 12268 W GLSUser :      at clln.d(:com.google.android.gms@*********@25.35.34 (190400-*********):18)
09-23 12:09:16.952 11750 12268 W GLSUser :      at clme.onSucceeded(:com.google.android.gms@*********@25.35.34 (190400-*********):9)
09-23 12:09:16.952 11750 12268 W GLSUser :      at m1.my.onSucceeded(:com.google.android.gms.dynamite_cronetdynamite@*********@25.35.34 (190400-0):3)
09-23 12:09:16.952 11750 12268 W GLSUser :      at m1.lu.run(:com.google.android.gms.dynamite_cronetdynamite@*********@25.35.34 (190400-0):21)
09-23 12:09:16.952 11750 12268 W GLSUser :      at m1.lo.run(:com.google.android.gms.dynamite_cronetdynamite@*********@25.35.34 (190400-0):31)
09-23 12:09:16.952 11750 12268 W GLSUser :      at bbnt.c(:com.google.android.gms@*********@25.35.34 (190400-*********):50)
09-23 12:09:16.952 11750 12268 W GLSUser :      at bbnt.run(:com.google.android.gms@*********@25.35.34 (190400-*********):70)
09-23 12:09:16.952 11750 12268 W GLSUser :      at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
09-23 12:09:16.952 11750 12268 W GLSUser :      at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
09-23 12:09:16.952 11750 12268 W GLSUser :      at bbtm.run(:com.google.android.gms@*********@25.35.34 (190400-*********):8)
09-23 12:09:16.952 11750 12268 W GLSUser :      at java.lang.Thread.run(Thread.java:1012)
09-23 12:09:16.952 11750 12268 W GLSUser : Caused by: hgtl: Protocol message contained an invalid tag (zero).
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgqj.k(:com.google.android.gms@*********@25.35.34 (190400-*********):43)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hguj.c(:com.google.android.gms@*********@25.35.34 (190400-*********):399)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hguj.i(:com.google.android.gms@*********@25.35.34 (190400-*********):8)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgsp.y(:com.google.android.gms@*********@25.35.34 (190400-*********):23)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgsk.g(:com.google.android.gms@*********@25.35.34 (190400-*********):3)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgqf.e(:com.google.android.gms@*********@25.35.34 (190400-*********):1)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgqf.d(:com.google.android.gms@*********@25.35.34 (190400-*********):3)
09-23 12:09:16.952 11750 12268 W GLSUser :      at hgqf.p(:com.google.android.gms@*********@25.35.34 (190400-*********):1)
09-23 12:09:16.952 11750 12268 W GLSUser :      at clix.g(:com.google.android.gms@*********@25.35.34 (190400-*********):69)
09-23 12:09:16.952 11750 12268 W GLSUser :      at clix.b(:com.google.android.gms@*********@25.35.34 (190400-*********):11)
09-23 12:09:16.952 11750 12268 W GLSUser :      ... 11 more
09-23 12:09:16.953 11750 12268 W GLSUser : [DeviceKeyStore] Cannot load key: Device key file not found.
09-23 12:09:16.962 10685 12733 E icbf    : *~*~*~ Previous channel {0} was garbage collected without being shut down! ~*~*~*
09-23 12:09:16.962 10685 12733 E icbf    :     Make sure to call shutdown()/shutdownNow()
09-23 12:09:16.962 10685 12733 E icbf    : java.lang.RuntimeException: ManagedChannel allocation site
09-23 12:09:16.962 10685 12733 E icbf    :      at icbe.<init>(:com.google.android.gms@*********@25.35.34 (190400-*********):21)
09-23 12:09:16.962 10685 12733 E icbf    :      at icbf.<init>(:com.google.android.gms@*********@25.35.34 (190400-*********):10)
09-23 12:09:16.962 10685 12733 E icbf    :      at icbd.a(:com.google.android.gms@*********@25.35.34 (190400-*********):250)
09-23 12:09:16.962 10685 12733 E icbf    :      at iblp.a(:com.google.android.gms@*********@25.35.34 (190400-*********):5)
09-23 12:09:16.962 10685 12733 E icbf    :      at cmsb.h(:com.google.android.gms@*********@25.35.34 (190400-*********):121)
09-23 12:09:16.962 10685 12733 E icbf    :      at gqio.a(:com.google.android.gms@*********@25.35.34 (190400-*********):158)
09-23 12:09:16.962 10685 12733 E icbf    :      at geoj.a(:com.google.android.gms@*********@25.35.34 (190400-*********):13)
09-23 12:09:16.962 10685 12733 E icbf    :      at gnbh.a(:com.google.android.gms@*********@25.35.34 (190400-*********):3)
09-23 12:09:16.962 10685 12733 E icbf    :      at gnai.run(:com.google.android.gms@*********@25.35.34 (190400-*********):19)
09-23 12:09:16.962 10685 12733 E icbf    :      at gnbj.run(:com.google.android.gms@*********@25.35.34 (190400-*********):5)
09-23 12:09:16.962 10685 12733 E icbf    :      at bbnt.c(:com.google.android.gms@*********@25.35.34 (190400-*********):50)
09-23 12:09:16.962 10685 12733 E icbf    :      at bbnt.run(:com.google.android.gms@*********@25.35.34 (190400-*********):70)
09-23 12:09:16.962 10685 12733 E icbf    :      at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
09-23 12:09:16.962 10685 12733 E icbf    :      at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
09-23 12:09:16.962 10685 12733 E icbf    :      at bbtm.run(:com.google.android.gms@*********@25.35.34 (190400-*********):8)
09-23 12:09:16.962 10685 12733 E icbf    :      at java.lang.Thread.run(Thread.java:1012)
09-23 12:09:16.963 10685 12733 E icbf    : *~*~*~ Previous channel {0} was garbage collected without being shut down! ~*~*~*
09-23 12:09:16.963 10685 12733 E icbf    :     Make sure to call shutdown()/shutdownNow()
09-23 12:09:16.963 10685 12733 E icbf    : java.lang.RuntimeException: ManagedChannel allocation site
09-23 12:09:16.963 10685 12733 E icbf    :      at icbe.<init>(:com.google.android.gms@*********@25.35.34 (190400-*********):21)
09-23 12:09:16.963 10685 12733 E icbf    :      at icbf.<init>(:com.google.android.gms@*********@25.35.34 (190400-*********):10)
09-23 12:09:16.963 10685 12733 E icbf    :      at icbd.a(:com.google.android.gms@*********@25.35.34 (190400-*********):250)
09-23 12:09:16.963 10685 12733 E icbf    :      at iblp.a(:com.google.android.gms@*********@25.35.34 (190400-*********):5)
09-23 12:09:16.963 10685 12733 E icbf    :      at cmsb.h(:com.google.android.gms@*********@25.35.34 (190400-*********):121)
09-23 12:09:16.963 10685 12733 E icbf    :      at gqio.a(:com.google.android.gms@*********@25.35.34 (190400-*********):158)
09-23 12:09:16.963 10685 12733 E icbf    :      at geoj.a(:com.google.android.gms@*********@25.35.34 (190400-*********):13)
09-23 12:09:16.963 10685 12733 E icbf    :      at gnbh.a(:com.google.android.gms@*********@25.35.34 (190400-*********):3)
09-23 12:09:16.963 10685 12733 E icbf    :      at gnai.run(:com.google.android.gms@*********@25.35.34 (190400-*********):19)
09-23 12:09:16.963 10685 12733 E icbf    :      at gnbj.run(:com.google.android.gms@*********@25.35.34 (190400-*********):5)
09-23 12:09:16.963 10685 12733 E icbf    :      at bbnt.c(:com.google.android.gms@*********@25.35.34 (190400-*********):50)
09-23 12:09:16.963 10685 12733 E icbf    :      at bbnt.run(:com.google.android.gms@*********@25.35.34 (190400-*********):70)
09-23 12:09:16.963 10685 12733 E icbf    :      at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
09-23 12:09:16.963 10685 12733 E icbf    :      at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
09-23 12:09:16.963 10685 12733 E icbf    :      at bbtm.run(:com.google.android.gms@*********@25.35.34 (190400-*********):8)
09-23 12:09:16.963 10685 12733 E icbf    :      at java.lang.Thread.run(Thread.java:1012)
09-23 12:09:16.985 10685 11230 D TrafficStats: tagSocket(96) with statsTag=0xc010301, statsUid=-1
09-23 12:09:17.550 10685 11230 I cn_CronetUrlRequestContext: destroyNativeStreamLocked org.chromium.net.impl.CronetBidirectionalStream@423d806
09-23 12:09:17.557 10685 12544 I NotificationsModuleTask: (REDACTED) Registering accounts to Chime Failed & Rescheduled for later. registration reason = %s
09-23 12:09:17.560 10685 10685 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.notifications.registration.service.NotificationsRegistrationTaskBoundService.ACTION_TASK_READY dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsInternalBoundBrokerService }
09-23 12:09:17.560 11750 13918 I NetworkScheduler.Stats: (REDACTED) Task %s/%s finished executing. cause:%s result: %s elapsed_millis: %s uptime_millis: %s exec_start_elapsed_seconds: %s
09-23 12:09:17.741 14054 14096 I FA      : Application backgrounded at: timestamp_millis: *************
09-23 12:09:20.836 14054 14062 W m.abinbev.oasis: Cleared Reference was only reachable from finalizer (only reported once)
09-23 12:09:27.753 10685 10685 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.measurement.START pkg=com.google.android.gms }
09-23 12:09:29.051   780   848 D EGL_emulation: app_time_stats: avg=1641.35ms min=11.24ms max=13016.33ms count=8
09-23 12:09:29.877 10685 10685 W ThreadPoolForeg: type=1400 audit(0.0:455): avc: denied { write } for name="traced_producer" dev="tmpfs" ino=647 scontext=u:r:gmscore_app:s0:c512,c768 tcontext=u:object_r:traced_producer_socket:s0 tclass=sock_file permissive=0 app=com.google.android.gms