apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

repositories {
    // Required for build
    flatDir {
        dirs 'libs'
    }
}

android {
    compileSdkVersion 33
    //buildToolsVersion "28.0.3"
    compileOptions.encoding = 'windows-1251'
    defaultConfig {
        applicationId "com.abinbev.oasis"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 29
        versionName "1.4.16"
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        multiDexEnabled true
        ndkVersion "26.1.10909125"
        ndk.abiFilters 'armeabi-v7a','arm64-v8a','x86','x86_64'
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    signingConfigs {
        release {
            storeFile file("../keys/sablimited.keystore")
            storePassword "SAB123*"
            keyAlias "sablimited"
            keyPassword "SAB123*"
        }
    }
    dexOptions {
        jumboMode = true
        javaMaxHeapSize "4g"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
    }
    productFlavors {
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

// Required for UNVIRED build
fileTree(dir: 'libs', include: '**/*.aar')
        .each { File file ->
            dependencies.add("implementation", [name: file.name.lastIndexOf('.').with { it != -1 ? file.name[0..<it] : file.name }, ext: 'aar'])
        }

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    //noinspection GradleCompatible
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.2.1'

    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'com.google.android.gms:play-services-location:17.1.0'
    implementation 'com.google.firebase:firebase-messaging:17.3.4'

    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.google.android.gms:play-services-location:16.0.0'
    implementation 'com.google.firebase:firebase-messaging'


    implementation 'com.google.firebase:firebase-core:16.0.4'
    implementation platform('com.google.firebase:firebase-bom:26.5.0')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'net.zetetic:android-database-sqlcipher:4.5.4@aar'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'
    implementation 'com.google.android.gms:play-services-basement:17.5.0'
    implementation 'androidx.sqlite:sqlite:2.4.0'
    //Unvired Android Library
//    implementation 'com.unvired:android-sdk:3.0.205'
    testImplementation 'junit:junit:4.12'
    //pdf
    implementation 'com.joanzapata.pdfview:android-pdfview:1.0.4@aar'
    //itext
    implementation 'com.itextpdf:itextg:5.5.10'
    //implementation(name:'app_libs_Unvired_Kernel_Android-R-4.000.0068', ext:'aar')
//    implementation(name:'DO_AndroidSDK_v2.4.9', ext:'aar')
//    implementation(name:'unviredpdfprintsevice-release', ext:'aar')
    implementation 'com.google.zxing:core:3.3.0'

}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'