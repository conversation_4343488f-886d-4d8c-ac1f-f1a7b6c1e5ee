<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <gradient android:angle="270"
                android:endColor="@color/white" android:startColor="@color/delivery_summary_card_color" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    <item
        android:left="0dp"
        android:right="1.5dp"
        android:top="0dp"
        android:bottom="1.5dp">
        <shape android:shape="rectangle">
            <stroke android:color="@color/colorPrimary" android:width="2dp"/>
            <corners android:radius="7dp" />
        </shape>
    </item>


</layer-list>