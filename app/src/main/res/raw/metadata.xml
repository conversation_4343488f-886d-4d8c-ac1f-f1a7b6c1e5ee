<Application description="OASIS Direct Store Delivery" name="OASIS" namespace="OASIS" package="com.abinbev.oasis" version="2.5">
    <BusinessEntity attachments="false" description="Answer option" name="ANSWER_OPTION" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.ANSWER_OPTION_HEADER" description="" name="ANSWER_OPTION_HEADER">
            <Field description="Two digit number" isGid="true" length="2" mandatory="true" name="Q_ID" sqlType="INTEGER"/>
            <Field description="5 Character Numeric NUMC" isGid="true" length="5" mandatory="true" name="SEQ" sqlType="INTEGER"/>
            <Field description="Two digit number" isGid="true" length="2" mandatory="true" name="ANS_ID" sqlType="INTEGER"/>
            <Field description="Text for Q and Ans" isGid="false" length="132" mandatory="false" name="ANS_TEXT" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="IS_AUTH_REQD" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Authorization" name="AUTH" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.AUTH_HEADER" description="" name="AUTH_HEADER">
            <Field description="Plant" isGid="true" length="4" mandatory="true" name="DEPOT" sqlType="TEXT"/>
            <Field description="Text (100 characters)" isGid="false" length="100" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="Text (100 characters)" isGid="true" length="100" mandatory="true" name="USER_ID" sqlType="TEXT"/>
            <Field description="Text Field" isGid="false" length="50" mandatory="false" name="PSWD" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="true" length="20" mandatory="true" name="TYPE" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Customer" name="CUSTOMER" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.CUSTOMER_HEADER" description="" name="CUSTOMER_HEADER">
            <Field description="DSD Connector: Customer Number" isGid="true" length="10" mandatory="true" name="CUST_NO" sqlType="TEXT"/>
            <Field description="Text (100 characters)" isGid="false" length="100" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="Time" isGid="false" length="6" mandatory="false" name="TIME_WIN_FROM" sqlType="TEXT"/>
            <Field description="Time" isGid="false" length="6" mandatory="false" name="TIME_WIN_TO" sqlType="TEXT"/>
            <Field description="Text, 255 Characters" isGid="false" length="255" mandatory="false" name="ADDRESS" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="VAT_IND" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="BANK_REF_NO" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="VAT_NO" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="LIC_NAME" sqlType="TEXT"/>
            <Field description="Plant" isGid="false" length="4" mandatory="false" name="SOLDTO_HM_DEPOT" sqlType="TEXT"/>
            <Field description="DSD Connector: Customer Number" isGid="false" length="10" mandatory="false" name="PAYER_NO" sqlType="TEXT"/>
            <Field description="Text (100 characters)" isGid="false" length="100" mandatory="false" name="BILL_TO_NAME" sqlType="TEXT"/>
            <Field description="Text, 255 Characters" isGid="false" length="255" mandatory="false" name="BILL_TO_ADDRESS" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="PAY_TERM" sqlType="TEXT"/>
            <Field description="Geo location latitude" isGid="false" length="8" mandatory="false" name="GPS_LAT" sqlType="REAL"/>
            <Field description="Geo location longitude" isGid="false" length="8" mandatory="false" name="GPS_LONG" sqlType="REAL"/>
            <Field description="Rawstring data" isGid="false" length="0" mandatory="false" name="MAP_IMG" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="false" length="1" mandatory="false" name="UNPLANNED" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="false" length="1" mandatory="false" name="IS_NAT_CUST" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="DIV" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="ACC_MGR" sqlType="TEXT"/>
            <Field description="DSD Connector: Phone number" isGid="false" length="16" mandatory="false" name="ACC_MGR_TEL" sqlType="TEXT"/>
            <Field description="Accum Discount" isGid="false" length="2" mandatory="false" name="ACC_DISC_IND" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="LIC_NO" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="ATTRIBUTES" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Customizing" name="CUSTOMIZATION" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.CUSTOMIZATION_HEADER" description="" name="CUSTOMIZATION_HEADER">
            <Field description="Comment" isGid="true" length="50" mandatory="true" name="KEY_NAME" sqlType="TEXT"/>
            <Field description="Comment" isGid="false" length="50" mandatory="false" name="KEY_VALUE" sqlType="TEXT"/>
            <Field description="Text (100 characters)" isGid="false" length="100" mandatory="false" name="KEY_DESC" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Depot" name="DEPOT" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.DEPOT_HEADER" description="" name="DEPOT_HEADER">
            <Field description="Plant" isGid="true" length="4" mandatory="true" name="DEPOT" sqlType="TEXT"/>
            <Field description="Name" isGid="false" length="30" mandatory="false" name="NAME" sqlType="TEXT"/>
            <Field description="DSD Connector: Phone number" isGid="false" length="16" mandatory="false" name="FAX" sqlType="TEXT"/>
            <Field description="Text, 255 Characters" isGid="false" length="255" mandatory="false" name="ADDRESS" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="NLLA" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="VAT_PESC" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="INV_MSG1" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="INV_MSG2" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="false" length="1" mandatory="false" name="SELECTED" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Table type for driver" name="DRIVER" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.DRIVER_HEADER" description="" name="DRIVER_HEADER">
            <Field description="Driver Number" isGid="true" length="10" mandatory="true" name="DRV_ID" sqlType="TEXT"/>
            <Field description="DSD Connector: Driver Name" isGid="false" length="35" mandatory="false" name="DRV_NAME" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Images" name="IMAGE" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.IMAGE_HEADER" description="" name="IMAGE_HEADER">
            <Field description="Text field length 255: texts" isGid="true" length="255" mandatory="true" name="IMG_NAME" sqlType="TEXT"/>
            <Field description="Short description of contents" isGid="false" length="50" mandatory="false" name="IMG_DESC" sqlType="TEXT"/>
            <Field description="" isGid="false" length="0" mandatory="false" name="IMG" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Description" name="INPUT_GET_SHIPMENT" onConflict="SERVER_WINS" save="false">
        <header className="com.abinbev.oasis.be.INPUT_GET_SHIPMENT_HEADER" description="" name="INPUT_GET_SHIPMENT_HEADER">
            <Field description="Plant" isGid="false" length="4" mandatory="true" name="DEPOT_NO" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="DNLD_MASTERDATA" sqlType="TEXT"/>
            <Field description="Character 100" isGid="true" length="100" mandatory="true" name="MOBILE_USER" sqlType="TEXT"/>
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Description" name="INPUT_GET_SHIPMENTS" onConflict="SERVER_WINS" save="false">
        <header className="com.abinbev.oasis.be.INPUT_GET_SHIPMENTS_HEADER" description="" name="INPUT_GET_SHIPMENTS_HEADER">
            <Field description="Plant" isGid="true" length="4" mandatory="true" name="DEPOT" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Description" name="INPUT_RESET_SHIPMENT" onConflict="SERVER_WINS" save="false">
        <header className="com.abinbev.oasis.be.INPUT_RESET_SHIPMENT_HEADER" description="" name="INPUT_RESET_SHIPMENT_HEADER">
            <Field description="Text (100 characters)" isGid="true" length="100" mandatory="true" name="MOBILE_USER" sqlType="TEXT"/>
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Material" name="MATERIAL" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.MATERIAL_HEADER" description="" name="MATERIAL_HEADER">
            <Field description="DSD Connector: Material Number" isGid="true" length="18" mandatory="true" name="MAT_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Description 40" isGid="false" length="40" mandatory="false" name="MAT_DESC" sqlType="TEXT"/>
            <Field description="DSD Connector: Material Category" isGid="false" length="4" mandatory="false" name="TYPE" sqlType="TEXT"/>
            <Field description="DSD Connector: Unit of Measure" isGid="false" length="3" mandatory="false" name="BASE_UOM" sqlType="TEXT"/>
            <Field description="Cs_Per_Pal" isGid="false" length="4" mandatory="false" name="CS_PER_PAL" sqlType="INTEGER"/>
            <Field description="DSD Connector: International article number" isGid="false" length="18" mandatory="false" name="EAN" sqlType="TEXT"/>
            <Field description="General item category group" isGid="false" length="4" mandatory="false" name="ITM_CAT" sqlType="TEXT"/>
            <Field description="External Material Group" isGid="false" length="18" mandatory="false" name="PACK_CODE" sqlType="TEXT"/>
            <Field description="Description for external material group" isGid="false" length="20" mandatory="false" name="PACK_DESC" sqlType="TEXT"/>
            <Field description="Material group 5" isGid="false" length="3" mandatory="false" name="MAT_GRP5" sqlType="TEXT"/>
            <Field description="Description" isGid="false" length="40" mandatory="false" name="MAT_GRP5_DESC" sqlType="TEXT"/>
            <Field description="Product hierarchy" isGid="false" length="18" mandatory="false" name="PROD_HIER" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Price" name="PRICE" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.PRICE_HEADER" description="" name="PRICE_HEADER">
            <Field description="DSD Connector: Material Number" isGid="true" length="18" mandatory="true" name="MAT_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Currency" isGid="false" length="3" mandatory="false" name="CUR" sqlType="TEXT"/>
            <Field description="DSD Connector: Net Price" isGid="false" length="12" mandatory="false" name="PRICE" sqlType="REAL"/>
            <Field description="Pricing: UOM" isGid="false" length="3" mandatory="false" name="UOM" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Pre-trip inspection Question Table Type" name="QUESTION" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.QUESTION_HEADER" description="" name="QUESTION_HEADER">
            <Field description="Two digit number" isGid="true" length="2" mandatory="true" name="Q_ID" sqlType="INTEGER"/>
            <Field description="Question Type" isGid="true" length="10" mandatory="true" name="TYPE" sqlType="TEXT"/>
            <Field description="Question text" isGid="false" length="0" mandatory="false" name="QUES" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Reason Codes" name="REASON" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.REASON_HEADER" description="" name="REASON_HEADER">
            <Field description="Text (20 Characters)" isGid="true" length="20" mandatory="true" name="TYPE" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="true" length="5" mandatory="true" name="RSN_CODE" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="RSN_DESC" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="IS_DEFAULT" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Shipment" name="SHIPMENT" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.SHIPMENT_HEADER" description="" name="SHIPMENT_HEADER">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="Driver Number" isGid="false" length="10" mandatory="false" name="DRV1" sqlType="TEXT"/>
            <Field description="Driver Number" isGid="false" length="10" mandatory="false" name="DRV2" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="false" length="18" mandatory="false" name="TRUCK" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="false" length="18" mandatory="false" name="TRL1" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="false" length="18" mandatory="false" name="TRL2" sqlType="TEXT"/>
            <Field description="Crew Count" isGid="false" length="1" mandatory="false" name="CREW" sqlType="INTEGER"/>
            <Field description="DSD - RA:  Number of Transmission" isGid="false" length="6" mandatory="false" name="TRIP" sqlType="INTEGER"/>
            <Field description="DSD Connector: Driver Name" isGid="false" length="35" mandatory="false" name="DRV1_NAME" sqlType="TEXT"/>
            <Field description="DSD Connector: Driver Name" isGid="false" length="35" mandatory="false" name="DRV2_NAME" sqlType="TEXT"/>
            <Field description="DSD Connector: Begin Mile" isGid="false" length="5" mandatory="false" name="START_MILE" sqlType="REAL"/>
            <Field description="DSD Connector: End Mile" isGid="false" length="5" mandatory="false" name="END_MILE" sqlType="REAL"/>
            <Field description="Vehicle Type" isGid="false" length="10" mandatory="false" name="TRUCK_TYPE" sqlType="TEXT"/>
            <Field description="Byte Value" isGid="false" length="1" mandatory="false" name="STAT" sqlType="INTEGER"/>
            <Field description="2 byte integer (signed)" isGid="false" length="2" mandatory="false" name="RCR_LIMIT" sqlType="INTEGER"/>
            <Field description="Vehicle Number" isGid="false" length="18" mandatory="false" name="TRL1_P" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="false" length="18" mandatory="false" name="TRL2_P" sqlType="TEXT"/>
            <Field description="Plant" isGid="false" length="4" mandatory="false" name="DEPOT" sqlType="TEXT"/>
            <Field description="Shipment Reasons" isGid="false" length="2" mandatory="false" name="FULL_FBR_RSN_CODE" sqlType="TEXT"/>
        </header>
        <item className="com.abinbev.oasis.be.ATTACHMENT" description="" name="ATTACHMENT">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="Two digit number" isGid="true" length="2" mandatory="true" name="TYPE" sqlType="INTEGER"/>
            <Field description="Field of type TIMS" isGid="false" length="6" mandatory="false" name="TIME" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="true" length="20" mandatory="true" name="REF_NO" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="true" length="20" mandatory="true" name="SIGNED_BY" sqlType="TEXT"/>
            <Field description="Rawstring data" isGid="false" length="0" mandatory="false" name="DATA" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="DATA_TYPE" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.DELIVERY" description="" name="DELIVERY">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD connector : Delivery Number" isGid="true" length="10" mandatory="true" name="DELV_NO" sqlType="TEXT"/>
            <Field description="DSD connecter : Visit ID" isGid="true" length="6" mandatory="true" name="VISIT_NO" sqlType="INTEGER"/>
            <Field description="DSD Connector: Invoice Number" isGid="false" length="10" mandatory="false" name="INV_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Order Number" isGid="false" length="10" mandatory="false" name="ORD_NO" sqlType="TEXT"/>
            <Field description="Sales Document Type" isGid="false" length="4" mandatory="false" name="ORD_TYPE" sqlType="TEXT"/>
            <Field description="DSD Connector: Phone number" isGid="false" length="16" mandatory="false" name="ORD_TEL" sqlType="TEXT"/>
            <Field description="DSD Connector: Shipment Type (Doc Type)" isGid="false" length="4" mandatory="false" name="DELV_TYPE" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="CUST_REF" sqlType="TEXT"/>
            <Field description="DSD Connector: Payment Type" isGid="false" length="2" mandatory="false" name="PAY_METHOD" sqlType="TEXT"/>
            <Field description="Text Line" isGid="false" length="72" mandatory="false" name="PAY_METHOD_DESC" sqlType="TEXT"/>
            <Field description="Text Field" isGid="false" length="50" mandatory="false" name="PAY_MESS" sqlType="TEXT"/>
            <Field description="Text Field" isGid="false" length="50" mandatory="false" name="PAY_MSG1" sqlType="TEXT"/>
            <Field description="Text Field" isGid="false" length="50" mandatory="false" name="PAY_MSG2" sqlType="TEXT"/>
            <Field description="Text Line" isGid="false" length="72" mandatory="false" name="NAT_INV_MSG1" sqlType="TEXT"/>
            <Field description="Text Line" isGid="false" length="72" mandatory="false" name="NAT_INV_MSG2" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="BLANK_TYPE" sqlType="TEXT"/>
            <Field description="Plant" isGid="false" length="4" mandatory="false" name="PLANT_ID" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="false" length="1" mandatory="false" name="IS_FBR" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="FULL_FBR_RSN" sqlType="TEXT"/>
            <Field description="Text field, length 4" isGid="false" length="4" mandatory="false" name="PAY_TERM" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="false" length="1" mandatory="false" name="HH_CREATED" sqlType="TEXT"/>
            <Field description="Cash Discount Percentage 1" isGid="false" length="3" mandatory="false" name="PERC_DISC" sqlType="REAL"/>
            <Field description="Baseline Date for Due Date Calculation" isGid="false" length="8" mandatory="false" name="BASELN_DT" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.DELIVERY_ITEM" description="" name="DELIVERY_ITEM">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD connector : Delivery Number" isGid="true" length="10" mandatory="true" name="DELV_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Item number" isGid="true" length="6" mandatory="true" name="ITM_NO" sqlType="INTEGER"/>
            <Field description="Original Quantity of Delivery Item" isGid="false" length="7" mandatory="false" name="QTY" sqlType="REAL"/>
            <Field description="Sales unit" isGid="false" length="3" mandatory="false" name="SLS_UOM" sqlType="TEXT"/>
            <Field description="Material Number" isGid="false" length="18" mandatory="false" name="MAT_NO" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="PROMO_NO" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="PROMO_DESC" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="PROMO_TYPE" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="PROMO_TYPE_DESC" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="SLS_DEAL" sqlType="TEXT"/>
            <Field description="Text, 40 Characters Long" isGid="false" length="40" mandatory="false" name="SLS_DEAL_DESC" sqlType="TEXT"/>
            <Field description="Field of type DATS" isGid="false" length="8" mandatory="false" name="SLS_DEAL_DT" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="IS_EMPTY" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="IS_RETURN" sqlType="TEXT"/>
            <Field description="Actual quantity delivered (in sales units)" isGid="false" length="7" mandatory="false" name="RCR_QTY" sqlType="REAL"/>
            <Field description="Actual quantity delivered (in sales units)" isGid="false" length="7" mandatory="false" name="FBR_QTY" sqlType="REAL"/>
            <Field description="Actual quantity delivered (in sales units)" isGid="false" length="7" mandatory="false" name="BIT_QTY" sqlType="REAL"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="FBR_RSN" sqlType="TEXT"/>
            <Field description="Numc3, internal use" isGid="false" length="3" mandatory="false" name="FBR_RSN_POS" sqlType="INTEGER"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="BIT_RSN" sqlType="TEXT"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="HH_CREATED" sqlType="TEXT"/>
            <Field description="Actual quantity delivered (in sales units)" isGid="false" length="7" mandatory="false" name="UBR_QTY" sqlType="REAL"/>
            <Field description="DSD Connector: Description 40" isGid="false" length="40" mandatory="false" name="MAT_DESC" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.DELIVERY_ITM_COND" description="" name="DELIVERY_ITM_COND">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD connector : Delivery Number" isGid="true" length="10" mandatory="true" name="DELV_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Item number" isGid="true" length="6" mandatory="true" name="ITM_NO" sqlType="INTEGER"/>
            <Field description="Condition Type" isGid="true" length="4" mandatory="true" name="COND_TYPE_ID" sqlType="TEXT"/>
            <Field description="Currency amount in BAPI interfaces" isGid="false" length="12" mandatory="false" name="RATE" sqlType="REAL"/>
            <Field description="Currency amount in BAPI interfaces" isGid="false" length="12" mandatory="false" name="VAL" sqlType="REAL"/>
            <Field description="Base Unit of Measure" isGid="false" length="3" mandatory="false" name="COND_UNIT" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.INVOICE" description="" name="INVOICE">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Invoice Number" isGid="true" length="10" mandatory="true" name="INV_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Customer Number" isGid="false" length="10" mandatory="false" name="CUST_NO" sqlType="TEXT"/>
            <Field description="DSD connector : Delivery Number" isGid="false" length="10" mandatory="false" name="DELV_NO" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="PRN_TIMESTAMP" sqlType="TEXT"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="DECLN_RSN" sqlType="TEXT"/>
            <Field description="Ship-to party character" isGid="false" length="12" mandatory="false" name="GRN_NO" sqlType="TEXT"/>
            <Field description="Invoice date (date created)" isGid="false" length="8" mandatory="false" name="INV_DATE" sqlType="TEXT"/>
            <Field description="Custom Specific Additional Field Customer Master" isGid="false" length="3" mandatory="false" name="CUST_RATING" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.INVOICE_ITEM" description="" name="INVOICE_ITEM">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Invoice Number" isGid="true" length="10" mandatory="true" name="INV_NO" sqlType="TEXT"/>
            <Field description="DSD connector : Delivery Number" isGid="true" length="10" mandatory="true" name="DELV_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Item number" isGid="true" length="6" mandatory="true" name="DELV_ITM_NO" sqlType="INTEGER"/>
            <Field description="DSD Connector: Price" isGid="false" length="12" mandatory="false" name="NET_VALUE" sqlType="REAL"/>
        </item>
        <item className="com.abinbev.oasis.be.NUM_RANGE" description="" name="NUM_RANGE">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Prefix" isGid="true" length="8" mandatory="true" name="PREFIX" sqlType="TEXT"/>
            <Field description="DSD Connector: Document Type" isGid="true" length="1" mandatory="true" name="DOC_TYPE" sqlType="TEXT"/>
            <Field description="DSD Connector: Document Numbering 8 byte" isGid="false" length="8" mandatory="false" name="LAST_USED_NUM" sqlType="TEXT"/>
            <Field description="DSD Connector: Extension field(30Char)" isGid="false" length="30" mandatory="false" name="MOBILE_USER" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.STOCK" description="" name="STOCK">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="Single-Character Indicator" isGid="true" length="1" mandatory="true" name="TYPE" sqlType="TEXT"/>
            <Field description="DSD Connector: Material Number" isGid="true" length="18" mandatory="true" name="MAT_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Quantity" isGid="false" length="12" mandatory="false" name="ACT_QTY" sqlType="REAL"/>
            <Field description="DSD Connector: Unit of Measure" isGid="false" length="3" mandatory="false" name="UOM" sqlType="TEXT"/>
            <Field description="DSD Connector: Quantity" isGid="false" length="12" mandatory="false" name="PLN_QTY" sqlType="REAL"/>
            <Field description="DSD Connector: Description 40" isGid="false" length="40" mandatory="false" name="MAT_DESC" sqlType="TEXT"/>
            <Field description="DSD Connector: Quantity" isGid="false" length="12" mandatory="false" name="CHCK_IN_QTY" sqlType="REAL"/>
            <Field description="Text field with length of 5" isGid="false" length="5" mandatory="false" name="RSN" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.TIME" description="" name="TIME">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="Time Type" isGid="true" length="20" mandatory="true" name="TYPE" sqlType="TEXT"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="true" name="TIME" sqlType="TEXT"/>
            <Field description="Text (length 35)" isGid="true" length="35" mandatory="true" name="ID" sqlType="TEXT"/>
            <Field description="Geo location latitude" isGid="false" length="8" mandatory="false" name="GPS_LAT" sqlType="REAL"/>
            <Field description="Geo location longitude" isGid="false" length="8" mandatory="false" name="GPS_LONG" sqlType="REAL"/>
        </item>
        <item className="com.abinbev.oasis.be.TRIP_INSP" description="" name="TRIP_INSP">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="Two digit number" isGid="true" length="2" mandatory="true" name="Q_ID" sqlType="INTEGER"/>
            <Field description="5 Character Numeric NUMC" isGid="true" length="5" mandatory="true" name="SEQ" sqlType="INTEGER"/>
            <Field description="Two digit number" isGid="false" length="2" mandatory="false" name="PRIO" sqlType="INTEGER"/>
            <Field description="Two digit number" isGid="false" length="2" mandatory="false" name="ANS_ID" sqlType="INTEGER"/>
            <Field description="5 Character Numeric NUMC" isGid="false" length="5" mandatory="false" name="REASON" sqlType="INTEGER"/>
            <Field description="Text (20 Characters)" isGid="false" length="20" mandatory="false" name="AUTH_BY" sqlType="TEXT"/>
            <Field description="Text for Q and Ans" isGid="false" length="132" mandatory="false" name="ANS_TEXT" sqlType="TEXT"/>
            <Field description="answer type value range" isGid="false" length="2" mandatory="false" name="ANS_TYPE" sqlType="INTEGER"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="MULTI_SEL" sqlType="TEXT"/>
            <Field description="Question Type" isGid="true" length="10" mandatory="false" name="INSP_TYPE" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.TRL_PICK_DRP" description="" name="TRL_PICK_DRP">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD Connector: Customer Number" isGid="true" length="10" mandatory="true" name="CUST_NO" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="true" length="18" mandatory="true" name="TRL_NO" sqlType="TEXT"/>
            <Field description="Two digit number" isGid="false" length="2" mandatory="false" name="TRL_TYPE" sqlType="INTEGER"/>
            <Field description="General Flag" isGid="true" length="1" mandatory="true" name="IS_DRP" sqlType="TEXT"/>
        </item>
        <item className="com.abinbev.oasis.be.VISIT" description="" name="VISIT">
            <Field description="Shipment Number" isGid="true" length="10" mandatory="true" name="SHIP_NO" sqlType="TEXT"/>
            <Field description="DSD connecter : Visit ID" isGid="true" length="6" mandatory="true" name="VISIT_NO" sqlType="INTEGER"/>
            <Field description="DSD Connector: Actual visit sequence" isGid="false" length="6" mandatory="false" name="SEQ_NO" sqlType="INTEGER"/>
            <Field description="DSD Connector: Customer Number" isGid="false" length="10" mandatory="false" name="CUST_NO" sqlType="TEXT"/>
            <Field description="Byte Value" isGid="false" length="1" mandatory="false" name="STAT" sqlType="INTEGER"/>
            <Field description="General Flag" isGid="false" length="1" mandatory="false" name="HHT" sqlType="TEXT"/>
            <Field description="DSD Connector: End Mile" isGid="false" length="5" mandatory="false" name="MILE" sqlType="REAL"/>
        </item>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Available Trailers" name="TRAILER" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.TRAILER_HEADER" description="" name="TRAILER_HEADER">
            <Field description="Vehicle Number" isGid="true" length="18" mandatory="true" name="TRAILER_ID" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="Trailer" name="TRL_CUST_MAP" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.TRL_CUST_MAP_HEADER" description="" name="TRL_CUST_MAP_HEADER">
            <Field description="DSD Connector: Customer Number" isGid="true" length="10" mandatory="true" name="CUST_NO" sqlType="TEXT"/>
            <Field description="Vehicle Number" isGid="true" length="18" mandatory="true" name="TRAILER_ID" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
    <BusinessEntity attachments="false" description="table type for truck" name="TRUCK" onConflict="SERVER_WINS" save="true">
        <header className="com.abinbev.oasis.be.TRUCK_HEADER" description="" name="TRUCK_HEADER">
            <Field description="Vehicle Number" isGid="true" length="18" mandatory="true" name="VECH_NO" sqlType="TEXT"/>
        </header>
    </BusinessEntity>
</Application>
