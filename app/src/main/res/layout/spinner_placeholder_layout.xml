<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/xReasonDropDownRootLayout">
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/grey_200_client"
        android:id="@+id/xReasonDropDownDiv"/>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:id="@+id/xSpinnerPlaceholder"
        android:textColor="@color/black"
        android:layout_margin="1dp"
        android:layout_below="@id/xReasonDropDownDiv"
        android:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="12sp"
        android:autoSizeMaxTextSize="18sp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="2dp"
        android:gravity="start"
        android:layout_centerVertical="true"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"/>
</RelativeLayout>