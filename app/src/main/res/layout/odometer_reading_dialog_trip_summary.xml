<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardBackgroundColor="@color/dialog_bg_color"
    android:layout_margin="10dp">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_close"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:id="@+id/xOdometerReadingDialogClose"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:text="@string/sample_text_dialog_odometer_reading"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_centerHorizontal="true"
            android:layout_below="@id/xOdometerReadingDialogClose"
           android:layout_marginTop="8dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:id="@+id/xOdometerReadingDialogMessage"/>
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_below="@id/xOdometerReadingDialogMessage"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/white"
            app:hintAnimationEnabled="false"
            android:hint="@string/hint_odometer_reading"
            app:hintTextColor="@color/white"
            app:boxBackgroundMode="filled"
            android:id="@+id/xOdometerReadingDialogReading">
            <com.google.android.material.textfield.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:id="@+id/xOdometerReadingDialogReadingEt"
                android:inputType="number"
                android:maxLength="6"/>
        </com.google.android.material.textfield.TextInputLayout>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/xOdometerReadingDialogReading"
            android:layout_marginTop="15dp"
            android:layout_marginRight="25dp"
            android:layout_marginLeft="25dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/spinner_bg"
            android:text="@string/proceed"
            android:textSize="20sp"
            android:textStyle="bold"
            android:id="@+id/xOdometerReadingDialogProceed"/>
    </RelativeLayout>
</androidx.cardview.widget.CardView>