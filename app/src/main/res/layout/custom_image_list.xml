<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="end">
        <ImageView
            android:id="@+id/crossButton"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:visibility="gone"
            android:src="@drawable/cancel_button" />
        <ImageView
            android:id="@+id/deleteButton"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:visibility="gone"
            android:src="@drawable/ic_delete_24" />
        </LinearLayout>
        <ImageView
            android:id="@+id/customerClaimImage"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_gravity="center"
            android:layout_height="match_parent">

        </ImageView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>