<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootLayout"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/depot_list_item_background">

    <androidx.cardview.widget.CardView
        android:id="@+id/itemCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp"
        android:layout_marginBottom="1dp"
        android:background="@color/depot_list_item_background"
        app:cardBackgroundColor="@color/depot_list_item_background"
        app:cardCornerRadius="2dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="5dp"
            android:paddingTop="8dp"
            android:paddingRight="5dp"
            android:paddingBottom="8dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.abinbev.oasis.util.FontAwesomeView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    app:fontAwesomeStyle="SOLID"
                    android:textColor="@color/white"
                    android:textSize="30sp"
                    android:text="@string/font_awesome_truck_icon"
                    android:layout_alignParentStart="true"
                    />

                <TextView
                    android:id="@+id/shipmentNO"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0"
                    android:layout_centerInParent="true"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="20dp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:layout_marginTop="10dp"
                android:paddingBottom="5dp"
                android:weightSum="100">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="60"
                    android:layout_marginEnd="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/truckNoLabel"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/truck_no"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/truckNo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="40"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/driver"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/driver_name"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/driverName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="WILLSON-SEPACET"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:weightSum="100">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="60"
                    android:layout_marginEnd="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/trailer1Label"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/trailer_1"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/trailer1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="40"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/trailer2Label"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/trailer_2"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/trailer2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:weightSum="100">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="60"
                    android:layout_marginEnd="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/crewLabel"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/crew"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/crew"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="40"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tripLabel"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/trip"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/trip"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>



