<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="3dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="10dp">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/xDeliverySummarySingleItemRootLayout"
        android:background="@color/question_bg_color">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Castle 340ml NRB"
            android:textSize="20sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="20dp"
            android:id="@+id/xDeliverySummarySingleItemName"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/xDeliverySummarySinglePlannedLayout"
            android:layout_below="@id/xDeliverySummarySingleItemName">
            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@string/planned"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:id="@+id/xDeliverySummarySingleItemPlannedTxt"
                android:layout_marginLeft="20dp"
               />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="84"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="30dp"
                android:textStyle="bold"
                android:id="@+id/xDeliverySummarySingleItemPlannedQty"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/xDeliverySummarySingleDeliveredLayout"
            android:layout_below="@id/xDeliverySummarySinglePlannedLayout">
            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@string/delivered"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="20dp"
                android:id="@+id/xDeliverySummarySingleItemDeliveredTxt"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="84"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="30dp"
                android:textStyle="bold"
                android:id="@+id/xDeliverySummarySingleItemDeliveredQty"/>
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/white"
            android:layout_below="@id/xDeliverySummarySingleDeliveredLayout"
            android:layout_marginTop="5dp"
            android:id="@+id/xDeliverySummarySingleItemDivider"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/xDeliverySummarySingleFbrBitLayout"
            android:layout_marginBottom="10dp"
            android:layout_below="@id/xDeliverySummarySingleItemDivider">
            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="@string/title_fbr_bit"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="20dp"
                android:id="@+id/xDeliverySummarySingleItemFbrBitTxt"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_marginTop="5dp"
                android:layout_marginLeft="30dp"
                android:textStyle="bold"
                android:id="@+id/xDeliverySummarySingleItemFbrBitQty"/>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>