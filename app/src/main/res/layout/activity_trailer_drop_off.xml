<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.TrailerDropOffActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_back"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/trailer_drop_off"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/xTrailerDropOffProceed"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_margin="10dp"
        android:gravity="center">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="3"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/trailer_1"
                android:textSize="16sp"
                android:layout_gravity="center_vertical"
                tools:targetApi="o" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="5dp"
                android:background="@drawable/spinner_bg_primary_border">
                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toStartOf="@id/xTrailerDropOffTrailer1Check"
                    android:background="@drawable/spinner_bg"
                    android:paddingStart="5dp"
                    android:inputType="number"
                    android:layout_margin="3dp"
                    android:id="@+id/xTrailerDropOffTrailer1Entry"
                    android:gravity="center"
                    tools:ignore="RtlSymmetry" />
                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:src="@drawable/ic_check_green"
                    android:layout_toStartOf="@id/xTrailerDropOffTrailer1Clear"
                    android:layout_marginEnd="5dp"
                    android:onClick="trailer1_check_clicked"
                    android:id="@+id/xTrailerDropOffTrailer1Check"/>
                <ImageButton
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentEnd="true"
                    android:src="@drawable/ic_close"
                    android:onClick="clearTrailer1"
                    android:id="@+id/xTrailerDropOffTrailer1Clear"
                    android:background="#5b9ad5"/>
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="3"
            android:layout_marginTop="20sp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/trailer_2"
                android:textSize="16sp"
                android:layout_gravity="center_vertical"
                tools:targetApi="o" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="5dp"
                android:background="@drawable/spinner_bg_primary_border">
                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toStartOf="@id/xTrailerDropOffTrailer2Check"
                    android:background="@drawable/spinner_bg"
                    android:paddingStart="5dp"
                    android:inputType="number"
                    android:layout_margin="3dp"
                    android:id="@+id/xTrailerDropOffTrailer2Entry"
                    android:gravity="center"
                    tools:ignore="RtlSymmetry" />
                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:src="@drawable/ic_check_green"
                    android:layout_toStartOf="@id/xTrailerDropOffTrailer2Clear"
                    android:layout_marginEnd="5dp"
                    android:onClick="trailer2_check_clicked"
                    android:id="@+id/xTrailerDropOffTrailer2Check"/>
                <ImageButton
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentEnd="true"
                    android:src="@drawable/ic_close"
                    android:onClick="clearTrailer2"
                    android:id="@+id/xTrailerDropOffTrailer2Clear"
                    android:background="#5b9ad5"/>
            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/button_bg_proceed"
        android:drawableRight="@drawable/ic_navigate_next"
        android:text="@string/proceed"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:onClick="trailer_drop_proceed_click"
        android:id="@+id/xTrailerDropOffProceed"
        android:layout_marginBottom="10dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>