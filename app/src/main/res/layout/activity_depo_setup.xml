<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.DepoSetupActivity"
    xmlns:custom="http://schemas.android.com/apk/">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_depot_setup_activity"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:onClick="navigateToSettings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_activity_depot"
        android:textColor="@color/colorPrimary"
        android:textSize="20sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.384" />

    <Button
        android:id="@+id/button3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:onClick="setupDepot"
        android:paddingLeft="25sp"
        android:paddingRight="25sp"
        android:text="@string/button_activity_depot"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="25sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.485" />



    <!--    <Button-->
<!--        android:id="@+id/button2"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:onClick="navigateToDriverSignatureActivity"-->
<!--        android:text="@string/title_activity_driver_signature"-->
<!--        android:textColor="@color/white"-->
<!--        android:textSize="20sp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintVertical_bias="0.6" />-->

<!--    <Button-->
<!--        android:id="@+id/button"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="76dp"-->
<!--        android:onClick="navigateToOdometerActivity"-->
<!--        android:text="ODOMETER_ACTIVITY"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.501"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/button2" />-->

<!--    <Button-->
<!--        android:id="@+id/button4"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="48dp"-->
<!--        android:onClick="navigateToQuestionsActivity"-->
<!--        android:text="QUESTIONS_ACTIVITY"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/button" />-->


</androidx.constraintlayout.widget.ConstraintLayout>
