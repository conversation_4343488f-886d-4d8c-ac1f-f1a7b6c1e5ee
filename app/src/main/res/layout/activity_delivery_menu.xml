<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.DeliveryMenuActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_back"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/title_delivery_menu"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="10dp">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xDeliveryMenuPrintInputChecklist"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_1_print_input_checklist"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:id="@+id/xDeliveryMenuFBRBIT"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_2_capture_fbr_or_bit"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xDeliveryMenuRCR"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/rcr"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:id="@+id/xDeliveryMenuUBR"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_4_ubr"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/xDeliveryMenuFullFbr">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_5_full_fbr"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/xDeliveryMenuWoodenSupportPallets">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_6_wooden_support_pallets"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/xDeliveryMenuDropTrailers">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_7_drop_trailers"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:id="@+id/xDeliveryMenuPrintProForma"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_8_print_pro_forma"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:id="@+id/xDeliveryMenuPrintInvoice"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_9_print_invoice"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>