<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:id="@+id/rootLayout"
    android:layout_marginBottom="1dp"
    android:background="@color/depot_list_item_background"
    >
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_settings"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="15sp"
        android:layout_marginLeft="15dp"/>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/depot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="1504"
            android:textColor="@color/white"
            android:textSize="20dp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="20dp"
            android:textStyle="bold"
            android:text="  " />

        <TextView
            android:id="@+id/depotName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:ellipsize="end"
            android:textSize="20dp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:text="SAB Newlands Operations Warehous"
            android:maxLines="1" />
    </LinearLayout>
</LinearLayout>