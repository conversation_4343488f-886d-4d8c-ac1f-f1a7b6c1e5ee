<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.ShipmentListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appBarLayout"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toLeftOf="@+id/xRefresh">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_shipment_list_activity"
                        android:layout_centerInParent="true"
                        android:id="@+id/xShipmentListTitle"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:paddingLeft="48dp"/>
                </RelativeLayout>
                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@drawable/ic_refresh"
                    android:layout_centerVertical="true"
                    android:id="@+id/xRefresh"
                    android:layout_toLeftOf="@id/xSettings"
                    android:layout_marginRight="10dp"/>
                <ImageView
                    android:onClick="navigateToSettings"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:id="@+id/xSettings"
                    android:layout_marginRight="15sp"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

<!--    <Button-->
<!--        android:id="@+id/shipmentListButton"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginStart="16dp"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:layout_marginEnd="16dp"-->
<!--        android:background="@color/colorPrimary"-->
<!--        android:onClick="downloadShipmentList"-->
<!--        android:text="@string/shipment_list_button"-->
<!--        android:textAllCaps="false"-->
<!--        android:textColor="@color/white"-->
<!--        android:textSize="25sp"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/shipmentRecyclerView"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />-->


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/shipmentRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>