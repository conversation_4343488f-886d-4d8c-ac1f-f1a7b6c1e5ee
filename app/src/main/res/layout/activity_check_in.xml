<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.CheckInActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/check_in"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:onClick="navigateToSettings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:layout_marginTop="15dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/shipment"
                android:textSize="16sp"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="11312033"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginTop="10dp"
                android:id="@+id/xCheckInShipment"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:layout_marginTop="15dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/driver"
                android:textSize="16sp"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="VICTOR WIMPIE"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:id="@+id/xCheckInDriverName"
                android:layout_marginTop="10dp"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:layout_marginTop="15dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/depot"
                android:textSize="16sp"
                />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1501 - SAB Alrode depot"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginTop="10dp"
                android:id="@+id/xCheckInDepotName"
                android:onClick="changeDepot"
                style="@style/Widget.AppCompat.Spinner.Underlined"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="20dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xCheckInFBRBIT"
                android:layout_marginLeft="10dp"
                android:onClick="checkInFbrBit"
                android:layout_marginBottom="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:text="@string/check_in_bit_fbr"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:onClick="checkInEmpty"
                android:id="@+id/xCheckInPallets"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:text="@string/check_in_empties_pallets"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
        </LinearLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>