<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.PODClaimSummaryActivity">
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/appbar"
            app:layout_constraintTop_toTopOf="parent"
            android:theme="@style/AppTheme.AppBarOverlay">
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/AppTheme.PopupOverlay" >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_back"
                        android:layout_centerVertical="true"
                        android:onClick="closeActivity"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="@string/scan_podclaim"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_settings"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15sp"
                        android:onClick="navigateToSettings"/>
                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.AppBarLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:weightSum="2"
            android:id="@+id/xScanPodClaimTitleLayout"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="10dp">
        </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/xScanPodClaimRecyclerview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="5dp"
        app:layout_constraintBottom_toTopOf="@id/xScanPodClaimTitleLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.444"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintVertical_bias="0.4" />

</androidx.constraintlayout.widget.ConstraintLayout>