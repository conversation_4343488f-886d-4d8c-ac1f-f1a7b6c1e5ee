<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/listPreferredItemHeight"
    android:paddingTop="2dip"
    android:paddingBottom="2dip"
    android:orientation="vertical"
    android:paddingStart="2dp"
    android:mode="twoLine"
    >

    <TextView android:id="@+id/auth_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dip"
        android:textAppearance="?android:attr/textAppearanceListItem"
        android:textStyle="bold"
        android:textAlignment="viewStart"
        />

    <TextView android:id="@+id/auth_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/auth_name"
        android:layout_alignStart="@id/auth_id"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textAlignment="viewStart"
        />

</LinearLayout>
