<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="10dp"
            android:padding="5dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="1">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/material"
                        android:textSize="16sp" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginEnd="5dp"
                        android:background="@drawable/spinner_bg_primary_border">
                        <Spinner
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:spinnerMode="dialog"
                            android:id="@+id/xCheckInEmptiesMaterialSpinner"/>
                    </RelativeLayout>
                </LinearLayout>
                <RelativeLayout
                    android:layout_width="120dp"
                    android:layout_height="70dp"
                    android:layout_marginStart="15dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="7dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <EditText
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toLeftOf="@id/xCheckInEmptiesQtyClear"
                        android:background="@drawable/edittext_bg"
                        android:paddingStart="5dp"
                        android:inputType="number"
                        android:maxLines="1"
                        android:hint="@string/qty"
                        android:layout_margin="3dp"
                        android:id="@+id/xCheckInEmptiesQty"
                        android:gravity="center"/>
                    <ImageButton
                        android:layout_width="40dp"
                        android:layout_height="70dp"
                        android:layout_alignParentEnd="true"
                        android:src="@drawable/ic_close"
                        android:onClick="clearReturnQty"
                        android:id="@+id/xCheckInEmptiesQtyClear"
                        android:background="#5b9ad5"/>
                </RelativeLayout>

            </LinearLayout>



            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="15dp">
                <com.abinbev.oasis.util.FontAwesomeView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:text="@string/font_awesome_minus_icon"
                    android:layout_gravity="center_vertical"
                    app:fontAwesomeStyle="SOLID"
                    android:gravity="center_vertical"
                    android:onClick="minus_button_click"
                    android:id="@+id/xCheckInEmptiesMinusIcon"
                    android:textColor="@color/red"
                    android:textSize="30sp"/>
                <Button
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:background="@drawable/button_bg_proceed"
                    android:text="Reduce"
                    android:layout_gravity="center_vertical"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:onClick="reduceFromStockItems"
                    android:id="@+id/xCheckInEmptiesReduceBtn"
                    android:layout_marginStart="15dp"/>

                <Button
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:background="@drawable/button_bg_proceed"
                    android:text="Add(+)"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:onClick="add_stock_button_click"
                    android:id="@+id/xCheckInEmptiesAddBtn"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="15dp"/>

            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>
</androidx.cardview.widget.CardView>