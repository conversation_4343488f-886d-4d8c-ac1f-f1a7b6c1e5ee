<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="10dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery"
                    android:textSize="16sp"
                    />
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginEnd="5dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <Spinner
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:id="@+id/xUBRBottomSheetDeliverySpinner"/>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/prod"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="75dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <Spinner
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:spinnerMode="dialog"
                        android:id="@+id/xUBRBottomSheetProductSpinner"/>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp">
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="@string/return_qty"
                    android:autoSizeMaxTextSize="18sp"
                    android:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="12sp"
                    android:layout_gravity="center_vertical"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    tools:targetApi="o" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:layout_weight="1.5"
                    android:background="@drawable/spinner_bg_primary_border">
                    <EditText
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toLeftOf="@id/xUBRBottomSheetReturnQtyClear"
                        android:background="@drawable/edittext_bg"
                        android:paddingStart="5dp"
                        android:inputType="number"
                        android:layout_margin="3dp"
                        android:id="@+id/xUBRBottomSheetReturnQtyEntry"
                        android:gravity="center"/>
                    <ImageButton
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentEnd="true"
                        android:src="@drawable/ic_close"
                        android:onClick="clearReturnQty"
                        android:id="@+id/xUBRBottomSheetReturnQtyClear"
                        android:background="#5b9ad5"/>
                </RelativeLayout>
                <Button
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/button_bg_proceed"
                    android:text="@string/next"
                    android:layout_weight="2"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:onClick="ubr_form_next_click"
                    android:id="@+id/xUBRFormNextButton"
                    android:layout_marginStart="15dp"/>

            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>