<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="7dp"
    android:layout_marginBottom="0dp"
    android:layout_marginLeft="0dp"
    android:layout_marginRight="0dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="7dp"
        android:id="@+id/xQuestionBg"
        app:cardBackgroundColor="@color/red">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:background="@color/question_bg_color">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:gravity="start"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginStart="5dp"
                android:id="@+id/xQuestionText"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_below="@id/xQuestionText"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:background="@drawable/spinner_bg">
                <Spinner
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:id="@+id/xQuestionSpinner"/>
            </RelativeLayout>

        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>