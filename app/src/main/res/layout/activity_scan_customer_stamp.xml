<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.ScanCustomerPodActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_scan_customer_stamp"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintBottom_toTopOf="@id/buttonLayout"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center"
        android:id="@+id/pickImageLayout"
        app:layout_constraintRight_toRightOf="parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">
            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/customerStampImage">

            </ImageView>

        </LinearLayout>

    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pickImageLayout"
        android:id="@+id/buttonLayout"
        >
        <Button
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?attr/colorPrimary"
            android:text="@string/take_photo"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:id="@+id/selectImageButton"
            android:onClick="select_image"
            android:textAllCaps="false"
            android:visibility="visible">
        </Button>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/editLayout"
            android:visibility="gone">
            <Button
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/colorPrimary"
                android:text="@string/change_photo"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:onClick="select_image"
                android:layout_weight="1"
                android:textAllCaps="false"
                android:layout_marginHorizontal="3dp">
            </Button>
            <Button
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/colorPrimary"
                android:text="@string/submit"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:onClick="submit_pod_claim"
                android:layout_weight="1"
                android:textAllCaps="false"
                android:layout_marginHorizontal="3dp">
            </Button>
        </LinearLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>