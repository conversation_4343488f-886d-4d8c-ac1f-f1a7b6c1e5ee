<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.DriverSignatureActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/AppTheme.PopupOverlay" >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <ImageView
                        android:id="@+id/xBackButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_back"
                        android:layout_centerVertical="true"
                        android:onClick="closeActivity"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_driver_signature"
                        android:textSize="20sp"
                        android:textColor="@color/white"
                        android:layout_centerInParent="true"
                        android:textStyle="bold"/>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_settings"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15sp"
                        android:onClick="navigateToSettings"/>
                </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>


    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:layout_marginRight="14dp"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@id/xDriverSignatureDrawingView"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/xDriverSignatureReset"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="resetDrawingView"
            android:src="@drawable/ic_refresh" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/xDriverSignatureReset"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:onClick="validateDriverSignature"
            android:text="@string/proceed"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <TextView
        android:id="@+id/xDriverCodeAndName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20sp"
        android:text="801686 - VICTOR WIMPIE"
        android:textAllCaps="true"
        android:textSize="20sp"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar" />
    <TextView
        android:id="@+id/xDeliveryNo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20sp"
        android:text="801686 - VICTOR WIMPIE"
        android:textAllCaps="true"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar" />
    <TextView
        android:id="@+id/xOrderNo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="9sp"
        android:text="SAP Order :- 12345"
        android:textAllCaps="true"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/xDeliveryNo" />

    <TextView
        android:id="@+id/xCustomerDecline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10sp"
        android:text="@string/customer_declined"
        android:layout_marginRight="15sp"
        android:textSize="18sp"
        android:gravity="right"
        android:textStyle="bold"
        android:textColor="@color/colorPrimary"
        android:visibility="visible"
        android:onClick="customerDeclined"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/xOrderNo" />

    <RelativeLayout
        android:id="@+id/xDriverSignatureDrawingView"
        android:layout_width="0dp"
        android:layout_height="280dp"
        android:layout_marginBottom="50dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        android:background="@drawable/drawingview_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/xCustomerDecline"
        app:layout_constraintVertical_bias="0.338" />


</androidx.constraintlayout.widget.ConstraintLayout>