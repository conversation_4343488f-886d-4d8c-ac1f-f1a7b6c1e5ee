<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="7dp"
    android:id="@+id/xPODClaimSingleItemRootLayout"
    app:cardBackgroundColor="@color/red"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp"
    app:cardElevation="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:paddingBottom="10dp"
        android:id="@+id/xPODClaimSummarySingleItemBg"
        android:background="@color/question_bg_color">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:weightSum="3"
                android:id="@+id/xTripSummaryNameLayout">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="20sp"
                    android:layout_alignParentStart="true"
                    android:textColor="@color/white"
                    android:layout_centerVertical="true"
                    android:text="Delivery : "
                    android:textStyle="bold"
                    android:layout_marginStart="5dp"
                    android:id="@+id/xTripSummarySingleItemIndex"
                    android:padding="5dp"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="8087603882"
                    android:textSize="20sp"
                    android:layout_marginLeft="12dp"
                    android:id="@+id/xPODClaimSummaryDeliveryNo"
                    android:layout_toEndOf="@id/xTripSummarySingleItemIndex"
                    android:layout_toStartOf="@id/xPodClaimSingleItemGoImg"
                    android:textColor="@color/white"
                    android:padding="5dp"/>
                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_white_arrow"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"
                    android:layout_centerVertical="true"
                    android:id="@+id/xPodClaimSingleItemGoImg"
                    android:layout_gravity="center_vertical"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:weightSum="3">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:textStyle="bold"
                    android:text="@string/sap_order"
                    android:id="@+id/xPODClaimSummarySingleItemIndex"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="5dp"
                    android:padding="5dp"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="726112"
                    android:id="@+id/xPODClaimSummarySapNumber"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_marginLeft="12dp"
                    android:layout_toEndOf="@id/xPODClaimSummarySingleItemIndex"
                    android:padding="5dp"/>
            </RelativeLayout>






        </LinearLayout>

    </RelativeLayout>
</androidx.cardview.widget.CardView>