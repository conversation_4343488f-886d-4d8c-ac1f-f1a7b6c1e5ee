<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.TripSummaryActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/trip_summary"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        android:weightSum="2"
        android:id="@+id/xTripSummaryButtonLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="10dp">
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_bg_proceed"
            android:text="@string/unplanned_pick"
            android:textSize="20sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:onClick="navigateToUnplannedCustomers"/>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_bg_proceed"
            android:textSize="20sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:id="@+id/xTripSummaryEndTrip"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:text="@string/end_trip"
            android:onClick="endTrip"
            android:layout_weight="1"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/xTripSummaryButtonLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_margin="5dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        android:id="@+id/xTripSummaryRecyclerview"/>
</androidx.constraintlayout.widget.ConstraintLayout>