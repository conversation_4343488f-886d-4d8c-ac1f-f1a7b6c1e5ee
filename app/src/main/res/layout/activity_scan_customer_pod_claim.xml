<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.ScanCustomerPodClaimActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/title_scan_customer_pod_claim"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintBottom_toTopOf="@id/relativeLayout"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center"
        android:id="@+id/customerStampClaimBody"
        app:layout_constraintRight_toRightOf="parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:onClick="navigate_to_scan_customer_pod"
                android:id="@+id/xEndOfVisitMenuCustomerPod"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="@string/_3_end_visit"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:onClick="navigate_to_scan_customer_claim"
                android:id="@+id/xEndOfVisitMenuCustomerClaim"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_4_end_visit"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@id/customerStampClaimBody"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:onClick="navigateToEndOfVisitMenu"
            android:text="@string/proceed"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>