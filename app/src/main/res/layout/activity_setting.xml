<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.SettingActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/setting_toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_arrow_back"
                            android:layout_centerVertical="true"
                            android:onClick="closeActivity"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Settings"
                            android:textSize="24sp"
                            android:textColor="@color/white"
                            android:layout_centerInParent="true"
                            android:textStyle="bold"/>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/setting_toolbar1"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/setting_toolbar">

                    <TextView
                        android:id="@+id/versionNo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Version No: 2.0.2.4"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="16sp" />

                </LinearLayout>
                <!--<LinearLayout
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/setting_toolbar1">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Settings"
                        android:textColor="@color/white"
                        android:textSize="22sp"
                        android:textStyle="bold" />

                </LinearLayout>-->
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <LinearLayout
            android:paddingBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:id="@+id/setting_send_log"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="1. Send Logs to Server"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:id="@+id/setting_setup_depot"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="2. Depot Setup"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/setting_reset_shipment"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="3. Reset Shipment"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:id="@+id/setting_reset_app"
                android:layout_marginRight="10dp">

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="4. Reset Application"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/setting_change_printer">

                <TextView

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="5.Printer Configuration"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/setting_send_app_db"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="6. Send App DB to Server"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/setting_exit_app">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="6. Exit Application"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/setting_simulate_crash">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="7.Simulate Crash!"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:id="@+id/setting_push_DB">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="8. Push DB"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>


        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>