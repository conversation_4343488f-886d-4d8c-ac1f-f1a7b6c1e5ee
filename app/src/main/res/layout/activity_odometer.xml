<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.OdometerActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_odometer"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/button_bg_proceed"
        android:layout_margin="5dp"
        android:text="@string/next"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:drawableEnd="@drawable/ic_navigate_next"
        android:id="@+id/xOdometerNext"
        android:onClick="validateReading"/>
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/xOdometerNext"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_margin="5dp"
            app:layout_constraintTop_toBottomOf="@id/appbar">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="30dp"
                    android:id="@+id/xOdometerReadingLayout"
                    android:hint="@string/hint_odometer_reading">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/xOdometerReading"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberPassword"
                        android:maxLength="6"/>
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/hint_confirm_odometer_reading"
                    android:layout_marginTop="20dp"
                    android:layout_below="@id/xOdometerReadingLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:id="@+id/xOdometerConfirmReading"
                        android:maxLength="6"/>
                </com.google.android.material.textfield.TextInputLayout>
            </RelativeLayout>
        </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>