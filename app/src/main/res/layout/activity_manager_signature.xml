<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.ManagerSignatureActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_manager_signature"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:onClick="navigateToSettings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>


    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:layout_marginRight="14dp"
        android:layout_marginLeft="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/xManagerSignatureReset"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="resetDrawingView"
            android:src="@drawable/ic_refresh" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/xManagerSignatureReset"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:text="@string/proceed"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:onClick="validateManagerSignature"/>
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="801686 - VICTOR WIMPIE"
        android:textAllCaps="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="20sp"
        android:textSize="20sp"
        android:id="@+id/xManagerCodeAndName"/>

    <RelativeLayout
        android:id="@+id/xManagerSignatureDrawingView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="15sp"
        android:background="@drawable/drawingview_bg"
        app:layout_constraintBottom_toTopOf="@+id/relativeLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/xManagerCodeAndName"
        app:layout_constraintVertical_bias="0.338" />

</androidx.constraintlayout.widget.ConstraintLayout>