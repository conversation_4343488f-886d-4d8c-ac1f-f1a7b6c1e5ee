<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="25dp"
        android:background="@drawable/a_corner">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/a_corner"
            android:elevation="4dp"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp"
           >

            <TextView
                android:id="@+id/tv1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:text="Alert Dialog"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="5dp"
                android:textColor="@color/white"
                android:text="This is the alert dialog to showing alert to user"

                android:textSize="18dp"
                android:textStyle="bold" />




            <LinearLayout
                android:id="@+id/btnDialogNeutral"
                android:layout_width="200dp"
                android:layout_height="37dp"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="30dp"
                android:background="@drawable/btn_selector"
                android:elevation="4dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvNeutral"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Later"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnDialogOk"
                android:layout_width="200dp"
                android:layout_height="37dp"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="30dp"
                android:background="@drawable/btn_selector"
                android:elevation="4dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvok"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="OK"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnDialogCancel"
                android:layout_width="200dp"
                android:layout_height="37dp"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="30dp"
                android:background="@drawable/btn_selector"
                android:elevation="4dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvCan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cancel"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>


    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="2dp"
            android:elevation="4dp"
            android:gravity="top|center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/textView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="1dp"
                android:gravity="center_horizontal"
                android:src="@drawable/icon2"
                android:visibility="visible" />
        </LinearLayout>
    </LinearLayout>


</merge>