<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.CustomerRatingActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/title_customer_rating"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <ImageView
                    android:onClick="navigateToSettings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"
                    android:src="@drawable/ic_settings" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <Button
        android:id="@+id/continueAuth"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/button_bg_proceed"
        android:drawableEnd="@drawable/ic_navigate_next"
        android:text="@string/continue_label"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        android:onClick="validateCustomerRating"/>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/continueAuth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_margin="5dp"
        app:layout_constraintTop_toBottomOf="@id/appbar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/customerRatingLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:scrollHorizontally="false"
                android:padding="10sp"
                android:textSize="20dp"
                android:text="@string/customer_rating_title" />

            <RelativeLayout
                android:id="@+id/activity_main"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_below="@id/customerRatingLabel"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/customerRatingLabel">

                <RadioGroup
                    android:id="@+id/radio_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:paddingTop="20dp"></RadioGroup>
            </RelativeLayout>

        </RelativeLayout>
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>