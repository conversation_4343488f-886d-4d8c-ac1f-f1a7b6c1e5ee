<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardBackgroundColor="@color/dialog_bg_color"
    android:layout_margin="10dp">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_close"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:id="@+id/xPalletCheckerDialogClose"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_below="@id/xPalletCheckerDialogClose"
            android:id="@+id/xPalletCheckerDialogDropDownLayout">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/checker_name"
                android:textSize="16sp"
                android:textColor="@color/white"
                android:id="@+id/xPalletCheckerSpinnerLabel"
                />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:layout_marginEnd="5dp"
                android:background="@drawable/spinner_bg_primary_border">
                <Spinner
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:id="@+id/xPalletCheckerDialogSpinner"
                    android:spinnerMode="dialog"/>
            </RelativeLayout>
        </LinearLayout>
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_below="@id/xPalletCheckerDialogDropDownLayout"
            android:layout_marginTop="10dp"
            app:boxStrokeColor="@color/white"
            app:hintAnimationEnabled="false"
            android:hint="@string/password"
            app:hintTextColor="@color/white"
            app:boxBackgroundMode="filled"
            android:id="@+id/xPalletCheckerDialogPasswordLayout">
            <com.google.android.material.textfield.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:id="@+id/xPalletCheckerDialogPassword" />
        </com.google.android.material.textfield.TextInputLayout>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/xPalletCheckerDialogPasswordLayout"
            android:layout_marginTop="15dp"
            android:layout_marginRight="25dp"
            android:layout_marginLeft="25dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/spinner_bg"
            android:text="@string/proceed"
            android:textSize="20sp"
            android:textStyle="bold"
            android:id="@+id/xPalletCheckerDialogProceed"/>
    </RelativeLayout>
</androidx.cardview.widget.CardView>