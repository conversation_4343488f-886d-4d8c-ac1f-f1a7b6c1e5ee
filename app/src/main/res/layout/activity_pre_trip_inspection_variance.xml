<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.PreTripInspectionVarianceActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/titleTextVariance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/title_pre_trip_inspection_variance"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <ImageView
                    android:onClick="navigateToSettings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"
                    android:src="@drawable/ic_settings" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>




    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar">
        <ScrollView android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:orientation="vertical">


            <RelativeLayout
                android:id="@+id/activity_main"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="10dp"
                app:layout_constraintEnd_toStartOf="@id/username_spinner"
                app:layout_constraintStart_toStartOf="parent">

                <RadioGroup
                android:id="@+id/radio_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:paddingTop="20dp">
            </RadioGroup>
            </RelativeLayout>

            <Spinner
                style="@style/SpinnerTheme"
                android:id="@+id/username_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/activity_main"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="15dp"
                app:layout_constraintEnd_toStartOf="@id/authPasswordBox"
                app:layout_constraintStart_toStartOf="@id/activity_main"
                tools:layout_editor_absoluteX="23dp"
                tools:layout_editor_absoluteY="561dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/authPasswordBox"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/username_spinner"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="15dp"
                android:hint="@string/password_label"
                app:layout_constraintEnd_toStartOf="parent"
                app:layout_constraintStart_toStartOf="@id/username_spinner">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/authPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberPassword" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/continueAuth"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_below="@+id/authPasswordBox"
                android:background="@drawable/button_bg_proceed"
                android:drawableEnd="@drawable/ic_navigate_next"
                android:text="@string/continue_label"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                android:onClick="continueAuth"/>
        </RelativeLayout>
        </ScrollView>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>