<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.DeliverySummaryForm">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_back"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_delivery_summary"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerHorizontal="true"
                    android:textStyle="bold"
                    android:layout_marginTop="5dp"
                    android:id="@+id/xDeliverySummaryTitle"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/xDeliverySummaryTitle"
                    android:layout_centerHorizontal="true"
                    android:text="Delivery: 35435435435"
                    android:textSize="18sp"
                    android:id="@+id/xDeliverySummaryDeliveryNo"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="2dp"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/xDeliverySummaryDeliveryNo"
                    android:layout_centerHorizontal="true"
                    android:text="SAP Order: 12345"
                    android:textSize="18sp"
                    android:id="@+id/xSapOrderNo"
                    android:layout_marginTop="1dp"
                    android:layout_marginBottom="2dp"/>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@id/xDeliverySummaryProceed"
        android:id="@+id/xDeliverySummaryRecyclerView"/>
    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/button_bg_proceed"
        android:layout_margin="5dp"
        android:text="@string/proceed"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:id="@+id/xDeliverySummaryProceed"
        android:onClick="proceed_button_click"/>
</androidx.constraintlayout.widget.ConstraintLayout>