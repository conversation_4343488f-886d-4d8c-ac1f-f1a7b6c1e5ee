<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.PreTripInspectionSecurityActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_back"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"/>

                <TextView
                    android:id="@+id/pretrip_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/title_pre_trip_security"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@id/xPreTripSecuritySignatureDrawingView"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/xPreTripSecuritySignatureReset"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="resetDrawingView"
            android:src="@drawable/ic_refresh" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/xPreTripSecuritySignatureReset"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:text="@string/confirm_and_proceed"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:onClick="validate"
            android:textStyle="bold" />
    </RelativeLayout>



        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appbar"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="20sp"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="14dp"
            android:hint="@string/card_number_hint"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:id="@+id/xPreTripSecurityCardNumber">
            <com.google.android.material.textfield.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:id="@+id/xPreTripInspectionSecurityCardNumberET"/>
        </com.google.android.material.textfield.TextInputLayout>


    <RelativeLayout
        android:id="@+id/xPreTripSecuritySignatureDrawingView"
        android:layout_width="0dp"
        android:layout_height="280dp"
        android:layout_margin="15sp"
        android:background="@drawable/drawingview_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:elevation="10dp"
        app:layout_constraintTop_toBottomOf="@+id/xPreTripSecurityCardNumber"
        app:layout_constraintVertical_bias="0.338" />
</androidx.constraintlayout.widget.ConstraintLayout>