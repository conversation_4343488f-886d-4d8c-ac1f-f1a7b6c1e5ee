<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.CustomerSignatureActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_customer_signature"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>


    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:layout_marginRight="14dp"
        android:layout_marginLeft="14dp"
        app:layout_constraintTop_toBottomOf="@id/xCustomerSignatureDrawingView"
         app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="15dp">

        <ImageView
            android:id="@+id/xCustomerSignatureReset"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="resetDrawingView"
            android:src="@drawable/ic_refresh" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/xCustomerSignatureReset"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:text="@string/proceed"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:onClick="validateCustomerSignature"/>
    </RelativeLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Delivery - 8069973076"
        android:textAllCaps="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="20sp"
        android:textSize="20sp"
        android:id="@+id/xCustomerCode"/>

    <TextView
        android:id="@+id/xSapOrderCode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:text="Delivery - 8069973076"
        android:textAllCaps="true"
        android:textSize="20sp"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/xCustomerCode" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/rating_label"
        android:textAllCaps="false"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/xSapOrderCode"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="20sp"
        android:textSize="15sp"
        android:id="@+id/xCustomerRatingLabel"/>

    <RelativeLayout
        android:id="@+id/ratingLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/xCustomerRatingLabel">
        <RatingBar
            android:id="@+id/ratingBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:max="5"
            android:numStars="5"
            android:stepSize="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/xCustomerSignatureDrawingView"
        android:layout_width="0dp"
        android:layout_height="280dp"
        android:layout_margin="15sp"
        android:background="@drawable/drawingview_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.551"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ratingLayout"
        app:layout_constraintVertical_bias="0">

        <TextView
            android:id="@+id/tandc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/tandc"
            android:textSize="16dp" />
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>