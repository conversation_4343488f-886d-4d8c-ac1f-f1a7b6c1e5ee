<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.PalletsPickUpActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_back"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/wooden_pallets_pickup"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:layout_margin="10dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@id/xPalletsPickUpBottomLayout">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/delivery"
                android:textSize="16sp"
                />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:layout_marginEnd="5dp"
                android:background="@drawable/spinner_bg_primary_border">
                <Spinner
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:id="@+id/xPalletsPickUpDeliverySpinner"/>
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/product"
                android:textSize="16sp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="75dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:layout_marginRight="5dp"
                android:background="@drawable/spinner_bg_primary_border">
                <Spinner
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:id="@+id/xPalletsPickUpProductSpinner"/>
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="4"
            android:layout_marginTop="10dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pallets_returned"
                android:textSize="16sp"
                android:layout_gravity="center_vertical"
                tools:targetApi="o" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="5dp"
                android:padding="3dp"
                android:background="@drawable/spinner_bg_primary_border">
                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_toStartOf="@id/xPalletsPickUpClearReturn"
                    android:background="@drawable/edittext_bg"
                    android:paddingStart="5dp"
                    android:padding="3dp"
                    android:inputType="number"
                    android:layout_centerVertical="true"
                    android:maxLength="4"
                    android:id="@+id/xPalletsPickupReturnEntry"
                    android:gravity="center_vertical" />
                <ImageButton
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentEnd="true"
                    android:src="@drawable/ic_close"
                    android:id="@+id/xPalletsPickUpClearReturn"
                    android:onClick="clearEditText"
                    android:background="#5b9ad5"/>
            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:orientation="horizontal"
        android:id="@+id/xPalletsPickUpBottomLayout"
        android:layout_margin="10dp"
        android:weightSum="2">
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/next"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_weight="1"
            android:layout_marginEnd="10dp"
            android:textColor="@color/white"
            android:onClick="nextButtonClick"
            android:background="@drawable/button_bg_proceed"/>
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/proceed"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:onClick="proceedButtonClick"
            android:background="@drawable/button_bg_proceed"
            android:layout_marginStart="10dp"
            android:drawableEnd="@drawable/ic_navigate_next"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>