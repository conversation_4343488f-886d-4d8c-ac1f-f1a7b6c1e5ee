<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.RcrFormActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_rcr"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:id="@+id/xRcrFormLayout">

                <include layout="@layout/rcr_bottom_sheet_layout"></include>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/xRcrFormLayout"
                android:id="@+id/xRcrRecyclerViewLayout"
                android:layout_marginTop="10dp">
                <TableLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:stretchColumns="*"
                    android:id="@+id/xRcrTableLayout">
                    <TableRow android:gravity="center">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_margin="2dp"
                                android:background="@color/table_bg_color"
                                android:padding="5dp"
                                android:layout_weight="1"
                                android:text="@string/material"
                                android:textColor="@color/white"
                                android:textSize="18sp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/total_cases"
                                android:textSize="18sp"
                                android:layout_margin="2dp"
                                android:layout_weight="2"
                                android:background="@color/table_bg_color"
                                android:textColor="@color/white"
                                android:padding="5dp"/>
                        </LinearLayout>

                    </TableRow>
                </TableLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="180dp"
                    android:layout_below="@id/xRcrTableLayout">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/no_data_available"
                        android:layout_marginTop="20sp"
                        android:layout_centerHorizontal="true"
                        android:textSize="18sp"
                        android:id="@+id/xRcrNoDataAvailable"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:id="@+id/xRcrRecyclerView"
                        android:visibility="gone"/>
                </RelativeLayout>

            </RelativeLayout>
            <Button
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
               android:layout_below="@id/xRcrRecyclerViewLayout"
                android:background="@drawable/button_bg_proceed"
                android:drawableRight="@drawable/ic_navigate_next"
                android:text="@string/proceed"
                android:gravity="center"
                android:layout_marginTop="10dp"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:onClick="rcr_proceed_click"
                android:id="@+id/xRcrProceed"
                android:layout_marginBottom="10dp"/>
        </RelativeLayout>
    </ScrollView>





</androidx.constraintlayout.widget.ConstraintLayout>