<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:id="@+id/xProductListDropDownRoot">
    <ImageView
        android:layout_width="78dp"
        android:layout_height="78dp"
        android:layout_alignParentStart="true"
        android:layout_below="@id/xProductDropDownDivider"
        android:id="@+id/xProductDropDownImg"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/xProductDropDownImg"
        android:layout_alignParentTop="true"
        android:layout_marginStart="10dp"
        android:text="000000"
        android:textColor="@color/black"
        android:layout_marginTop="10dp"
        android:textSize="18sp"
        android:id="@+id/xProductDropDownMatNo"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/xProductDropDownImg"
        android:layout_marginStart="10dp"
        android:textColor="@color/black"
        android:text="Pallet : SAB Wood"
        android:layout_below="@id/xProductDropDownMatNo"
        android:layout_marginTop="10dp"
        android:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="12sp"
        android:autoSizeMaxTextSize="18sp"
        android:id="@+id/xProductDropDownMatDesc"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/grey_200_client"
        android:id="@+id/xProductDropDownDivider"
        android:layout_alignParentTop="true" />
</RelativeLayout>