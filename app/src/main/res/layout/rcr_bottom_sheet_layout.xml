<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="10dp">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery"
                    android:textSize="16sp"
                   />
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginEnd="5dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <Spinner
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:id="@+id/xRcrBottomSheetDeliverySpinner"/>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/prod"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="75dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <Spinner
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:spinnerMode="dialog"
                        android:id="@+id/xRcrBottomSheetProductSpinner"/>
                </RelativeLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/bottlesLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp">
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="@string/bottles"

                    android:autoSizeMaxTextSize="18sp"
                    android:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="12sp"
                    android:layout_gravity="center_vertical"
                    android:textStyle="bold"

                    android:textColor="@color/black"
                    tools:targetApi="o" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center"
                    android:text="0"
                    android:layout_weight="2"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:id="@+id/xRcrBottomSheetTotalBottlesCount"
                    android:background="@drawable/edittext_disabled_bg"/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp">
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="@string/split_cases"

                    android:autoSizeMaxTextSize="18sp"
                    android:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="12sp"
                    android:layout_gravity="center_vertical"
                    android:textStyle="bold"

                    android:textColor="@color/black"
                    tools:targetApi="o" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center"
                    android:text="0"
                    android:layout_weight="2"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:id="@+id/xRcrBottomSheetSplitCasesResult"
                    android:background="@drawable/edittext_disabled_bg"/>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_weight="1.2"
                    android:layout_marginStart="15dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <EditText
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toLeftOf="@id/xRcrBottomSheetSplitCasesAdd"
                        android:background="@drawable/edittext_bg"
                        android:paddingStart="5dp"
                        android:inputType="number"
                        android:layout_margin="3dp"
                        android:id="@+id/xRcrBottomSheetSplitCasesEntry"
                        android:gravity="center"/>
                    <ImageButton
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentEnd="true"
                        android:src="@drawable/ic_add"
                        android:onClick="split_cases_add_click"
                        android:id="@+id/xRcrBottomSheetSplitCasesAdd"
                        android:background="#5b9ad5"/>
                </RelativeLayout>
                <ImageButton
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_remove"
                    android:background="#5b9ad5"
                    android:layout_weight="2"
                    android:onClick="split_cases_minus_click"
                    android:id="@+id/xRcrBottomSheetSplitCasesReduce"
                    android:layout_marginLeft="15dp"/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp">
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="@string/full_pallets"
                    android:autoSizeMaxTextSize="18sp"
                    android:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="12sp"
                    android:layout_gravity="center_vertical"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    tools:targetApi="o" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center"
                    android:text="0"
                    android:layout_weight="2"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:id="@+id/xRcrBottomSheetFullPalletsResult"
                    android:background="@drawable/edittext_disabled_bg"/>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:layout_weight="1.2"
                    android:background="@drawable/spinner_bg_primary_border">
                    <EditText
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toLeftOf="@id/xRcrBottomSheetFullPalletsAdd"
                        android:background="@drawable/edittext_bg"
                        android:paddingStart="5dp"
                        android:inputType="number"
                        android:id="@+id/xRcrBottomSheetFullPalletsEntry"
                        android:layout_margin="3dp"
                        android:gravity="center"/>
                    <ImageButton
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentEnd="true"
                        android:src="@drawable/ic_add"
                        android:onClick="full_pallets_add_click"
                        android:id="@+id/xRcrBottomSheetFullPalletsAdd"
                        android:background="#5b9ad5"/>
                </RelativeLayout>
                <ImageButton
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_remove"
                    android:background="#5b9ad5"
                    android:layout_weight="2"
                    android:onClick="full_pallets_minus_click"
                    android:id="@+id/xRcrBottomSheetFullPalletsReduce"
                    android:layout_marginStart="15dp"/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"

                android:layout_marginTop="10dp">
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="@string/total_cases"
                    android:autoSizeMaxTextSize="18sp"
                    android:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="12sp"
                    android:layout_gravity="center_vertical"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    tools:targetApi="o" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center"
                    android:text="0"
                    android:layout_weight="2"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:id="@+id/xRcrBottomSheetTotalCasesResult"
                    android:background="@drawable/edittext_disabled_bg"/>
                <Button
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/button_bg_proceed"
                        android:text="@string/next"
                        android:layout_weight="1.6"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:onClick="rcr_next_button_click"
                        android:id="@+id/xRcrBottomSheetNextButton"
                        android:layout_marginStart="15dp"/>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/clear"
                        android:textSize="20sp"
                        android:layout_weight="1.6"
                        android:layout_marginStart="15dp"
                        android:textColor="@color/red"
                        android:onClick="rcr_clear_onClick"
                        android:id="@+id/xRcrBottomSheetClearAll"
                        android:layout_gravity="center_vertical"/>

            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>