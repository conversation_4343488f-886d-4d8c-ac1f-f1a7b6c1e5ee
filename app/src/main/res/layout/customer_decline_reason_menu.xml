<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/linearLayoutCompat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <RelativeLayout
            android:id="@+id/xCustomerDeclineHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="MissingConstraints">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:text="@string/reason_for_decline"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/xCustomerDeclineMenuClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:src="@drawable/ic_close_black" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_below="@+id/xCustomerDeclineHeader"
            app:layout_constraintTop_toBottomOf="@+id/xCustomerDeclineHeader">
            <ScrollView android:id="@+id/scroll_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/activity_main"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="10dp"
                    app:layout_constraintStart_toStartOf="parent">

                    <RadioGroup
                        android:id="@+id/radio_group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:paddingTop="20dp">
                    </RadioGroup>
                </RelativeLayout>
                <Button
                    android:id="@+id/continueSignature"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="40dp"
                    android:layout_below="@+id/activity_main"
                    android:background="@drawable/button_bg_proceed"
                    android:drawableEnd="@drawable/ic_navigate_next"
                    android:onClick="validateCustomerRating"
                    android:text="@string/continue_label"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toStartOf="parent" />

            </RelativeLayout>
            </ScrollView>
        </RelativeLayout>

    </RelativeLayout>



</androidx.constraintlayout.widget.ConstraintLayout>