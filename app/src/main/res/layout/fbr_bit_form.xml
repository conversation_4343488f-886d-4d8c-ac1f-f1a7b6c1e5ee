<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="10dp"
            android:padding="2dp"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/delivery"
                    android:textSize="16sp"
                    />
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginEnd="5dp"
                    android:background="@drawable/spinner_bg_primary_border">

                    <Spinner
                        android:id="@+id/xFbrBitDeliverySpinner"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/prod"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="75dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/spinner_bg_primary_border">
                    <Spinner
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:spinnerMode="dialog"
                        android:id="@+id/xFbrBitProductSpinner"/>
                </RelativeLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="@string/fbr_qty"
                    android:layout_weight="2"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="5dp">
                    <com.google.android.material.textfield.TextInputEditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:id="@+id/xFbrBitFBRQty"/>
                </com.google.android.material.textfield.TextInputLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/reason"
                        android:textSize="16sp" />

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginRight="5dp"
                        android:background="@drawable/spinner_bg_primary_border">
                        <Spinner
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:spinnerMode="dialog"
                            android:id="@+id/xFbrBitReasonSpinner"/>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="10dp">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:hint="@string/bit_qty"
                android:layout_weight="2"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="5dp">
                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/xFbrBitBITQty"
                    android:inputType="number"/>
            </com.google.android.material.textfield.TextInputLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="5dp"
                android:layout_gravity="center_vertical">
                <Button
                    android:layout_width="100dp"
                    android:layout_height="40dp"
                    android:background="@drawable/button_bg_proceed"
                    android:text="@string/next"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:layout_centerVertical="true"
                    android:onClick="fbr_bit_next_click"
                    android:id="@+id/xFbrBitBottomSheetNextButton"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/clear"
                    android:textSize="20sp"
                    android:layout_weight="1"
                    android:layout_marginStart="15dp"
                    android:textColor="@color/red"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/xFbrBitBottomSheetNextButton"
                    android:onClick="fbr_bit_clear_click"
                    android:id="@+id/xFbrBitBottomSheetClearAll"
                   />
            </RelativeLayout>

        </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>