<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.PrinterSetupActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/printer_setup_activity_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary"
        android:minHeight="?attr/actionBarSize"
        android:theme="?attr/actionBarTheme"
        app:layout_constraintTop_toTopOf="parent">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/printer_setup"
            android:textSize="20sp"
            android:gravity="center_horizontal"
            android:textColor="@color/white"
            android:layout_centerInParent="true"
            android:textStyle="bold"/>
    </androidx.appcompat.widget.Toolbar>

    <RelativeLayout
        android:id="@+id/relativeLayout2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/printer_setup_activity_tool_bar"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/scanForMacMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="6dp"
            android:layout_marginTop="48dp"
            android:layout_marginRight="6dp"
            android:paddingTop="10dp"
            android:text="@string/printerSetupmsg"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/printerName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/scanForMacMsg"
            android:layout_marginTop="53dp"
            android:enabled="false"
            android:gravity="center_horizontal"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/grey" />


        <TextView
            android:id="@+id/printerMacAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/printerName"
            android:enabled="false"
            android:gravity="center_horizontal"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/grey" />


        <Button
            android:id="@+id/scanForMacAddr"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/printerMacAddress"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="15dp"
            android:background="@drawable/button_bg_proceed"
            android:text="Scan"
            android:textColor="@color/white" />

        <LinearLayout
            android:id="@+id/buttonsLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="center_vertical|center_horizontal"
            android:gravity="center_vertical|center_horizontal"
            android:orientation="horizontal"
            android:weightSum="2">

            <TableLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:stretchColumns="1">

                <TableRow android:id="@+id/button_row">

                    <Button
                        android:textColor="@android:color/white"
                        android:background="@drawable/button_bg_proceed"
                        android:id="@+id/printerSetupCancelBtn"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:layout_marginLeft="10dip"
                        android:layout_weight="1"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:text="@string/back_text"/>

                    <Button
                        android:textColor="@android:color/white"

                        android:background="@drawable/button_bg_proceed"
                        android:id="@+id/saveMacAddr"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:text="@string/save_text"/>

                    <!--
                             <ImageButton
                            android:id="@+id/sendToFileButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_margin="5dp"
                            android:layout_marginRight="10dip"
                            android:layout_weight="1"
                            android:paddingLeft="5dp"
                            android:paddingRight="5dp"
                            android:src="@drawable/to_file" />
                    -->
                </TableRow>
            </TableLayout>
        </LinearLayout>

    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>