<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="2dp">
    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        android:id="@+id/xRcrTableLayout">
        <TableRow android:gravity="center">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@color/table_data_bg_color"
                android:padding="5dp"
                android:id="@+id/xRcrSingleItemMaterialName"
                android:text="@string/material"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:layout_weight="1"
                android:gravity="start"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/total_cases"
                android:textSize="18sp"
                android:layout_margin="2dp"
                android:background="@color/table_data_bg_color"
                android:textColor="@color/black"
                android:padding="5dp"
                android:layout_weight="2"
                android:id="@+id/xRcrSingleItemTotalCases"
                android:gravity="start"/>
            </LinearLayout>
        </TableRow>
    </TableLayout>
</RelativeLayout>