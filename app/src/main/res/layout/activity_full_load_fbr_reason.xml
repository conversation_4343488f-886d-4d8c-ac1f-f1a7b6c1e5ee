<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.FullLoadFBRReason">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:onClick="closeActivity"
                    android:src="@drawable/ic_arrow_back" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_full_load_fbr_reason"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:layout_margin="10dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@id/xFullFbrBottomLayout">

        <RadioGroup
            android:id="@+id/FBRReason"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RadioButton
                android:id="@+id/warehouseFullFBR"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="false"
                android:layout_marginStart="10dp"
                android:text="@string/warehouse_full_fbr_reason"
                android:textSize="25sp"
                android:textStyle="bold" />

            <RadioButton
                android:id="@+id/logisticFullFBR"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="false"
                android:layout_marginStart="10dp"
                android:layout_marginTop="150px"
                android:layout_marginBottom="150px"
                android:text="@string/logistics_full_fbr_reason"
                android:textSize="25sp"
                android:textStyle="bold" />

        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/select_reason"
                android:textSize="16sp" />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:layout_marginEnd="5dp"
                android:paddingStart="3dp"
                android:background="@drawable/spinner_bg_primary_border">

                <Spinner
                    android:id="@+id/xFullLoadFbrReasonSpinner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:orientation="horizontal"
        android:id="@+id/xFullFbrBottomLayout"
        android:layout_margin="10dp"
        android:weightSum="2">

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10px"
            android:layout_weight="1"
            android:background="@drawable/button_bg_proceed"
            android:drawableEnd="@drawable/ic_navigate_next"
            android:onClick="validateAndClose"
            android:text="@string/proceed"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>