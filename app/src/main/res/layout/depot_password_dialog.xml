<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    app:cardBackgroundColor="@color/dialog_bg_color">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp">

        <ImageView
            android:id="@+id/depot_dialog_close_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_close" />

        <TextView
            android:id="@+id/dialog_depot_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/depot_dialog_close_icon"
            android:layout_marginStart="10dp"
            android:layout_marginTop="-32dp"
            android:layout_marginEnd="10dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:text="@string/depot_password"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />



        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_marginTop="15dp"
            android:id="@+id/linear_depot"
            android:layout_below="@+id/dialog_depot_label">
            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/dialog_depot_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="SAB Wadelle"
                android:textSize="16sp" />

            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/dialog_shipment_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/dialog_depot_name"
                android:layout_marginTop="5dp"
                android:text="Shipment: 123121212"
                android:textSize="16sp" />

            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/dialog_driver_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/dialog_shipment_name"
                android:layout_marginTop="5dp"
                android:text="Driver: 1212"
                android:textSize="16sp" />

            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/dialog_truck_no"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/dialog_driver_name"
                android:layout_marginTop="5dp"
                android:text="Truck: 1232"
                android:textSize="16sp" />
            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/dialog_user_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/dialog_truck_no"
                android:layout_marginTop="5dp"
                android:text="User: Charl"
                android:textSize="16sp" />
        </RelativeLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/xOdometerReadingDialogReading"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/linear_depot"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="25dp"
            app:boxBackgroundColor="@color/white"
            app:boxBackgroundMode="outline"
            app:boxStrokeColor="@color/white">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/depot_password"
                android:inputType="textPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/depot_pass_ok_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/xOdometerReadingDialogReading"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/spinner_bg"
            android:text="@string/ok"
            android:textSize="20sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/depot_cancel_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/depot_pass_ok_btn"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/spinner_bg"
            android:text="@string/cancel"
            android:textSize="20sp"
            android:textStyle="bold" />
        <TextView
            android:textColor="@android:color/white"
            android:id="@+id/dialog_depot_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ver: 2.0.3"
            android:layout_below="@id/depot_cancel_btn"
            android:layout_centerHorizontal="true"
            android:textSize="13sp" />
    </RelativeLayout>
</androidx.cardview.widget.CardView>