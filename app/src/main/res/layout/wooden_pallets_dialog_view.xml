<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="10dp"
    app:cardBackgroundColor="@color/white">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/wooden_pallets_menu"
            android:layout_centerHorizontal="true"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginTop="10dp"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_close_black"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:id="@+id/xWoodenPalletsDialogClose"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginTop="15dp"
            android:layout_below="@id/xWoodenPalletsDialogClose">
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/button_bg_proceed"
                android:text="@string/_1_pallets_pick_up"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="18sp"
                android:id="@+id/xWoodenPalletsDialogPickUp"/>
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/button_bg_proceed"
                android:text="@string/_2_pallets_drop_off"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:textSize="18sp"
                android:id="@+id/xWoodenPalletsDialogDropOff"/>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>