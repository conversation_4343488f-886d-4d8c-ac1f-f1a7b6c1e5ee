<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.abinbev.oasis.activities.EndOfVisitMenuActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/appbar"
        app:layout_constraintTop_toTopOf="parent"
        android:theme="@style/AppTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_end_of_visit_menu"
                    android:textSize="20sp"
                    android:textColor="@color/white"
                    android:layout_centerInParent="true"
                    android:textStyle="bold"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_settings"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15sp"
                    android:onClick="navigateToSettings"/>
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center"
        app:layout_constraintRight_toRightOf="parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xEndOfVisitMenuReprint"
                android:layout_marginLeft="10dp"
                android:onClick="reprint_invoice_click"
                android:layout_marginBottom="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="1. Reprint Invoice"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:onClick="print_vehicle_stock_click"
                android:id="@+id/xEndOfVisitMenuVehicleStock"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="@string/_2_print_vehicle_stock_report"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:layout_marginStart="10dp"/>
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:background="@drawable/button_bg_proceed"-->
<!--                android:layout_marginTop="15dp"-->
<!--                android:layout_marginLeft="10dp"-->
<!--                android:layout_marginBottom="10dp"-->
<!--                android:onClick="navigate_to_scan_customer_stamp"-->
<!--                android:id="@+id/xEndOfVisitMenuCustomerStamp"-->
<!--                android:layout_marginRight="10dp">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentStart="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginStart="10dp"-->
<!--                    android:text="@string/_3_end_visit"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="20sp"-->
<!--                    android:textStyle="bold" />-->
<!--                <ImageView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:src="@drawable/ic_navigate_next"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_alignParentEnd="true"-->
<!--                    android:layout_marginEnd="5dp"/>-->
<!--            </RelativeLayout>-->
<!--            <RelativeLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:background="@drawable/button_bg_proceed"-->
<!--                android:layout_marginTop="15dp"-->
<!--                android:layout_marginLeft="10dp"-->
<!--                android:layout_marginBottom="10dp"-->
<!--                android:onClick="navigate_to_scan_customer_claim"-->
<!--                android:id="@+id/xEndOfVisitMenuCustomerClaim"-->
<!--                android:layout_marginRight="10dp">-->
<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentStart="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:text="@string/_4_end_visit"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textStyle="bold"-->
<!--                    android:textSize="20sp"-->
<!--                    android:layout_marginStart="10dp"/>-->
<!--                <ImageView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:src="@drawable/ic_navigate_next"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_alignParentEnd="true"-->
<!--                    android:layout_marginEnd="5dp"/>-->
<!--            </RelativeLayout>-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xEndOfVisitMenuPodClaim"
                android:onClick="navigate_to_PODClaim"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="@string/_3_end_visit_label"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/button_bg_proceed"
                android:layout_marginTop="15dp"
                android:id="@+id/xEndOfVisitMenuEndVisit"
                android:onClick="end_visit_click"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="10dp"
                android:layout_marginRight="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:text="@string/_4_end_visit_label"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_navigate_next"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"/>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>