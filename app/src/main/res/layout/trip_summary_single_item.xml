<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="7dp"
    android:id="@+id/xTripSummarySingleItemRootLayout"
    app:cardBackgroundColor="@color/red"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="10dp"
    app:cardElevation="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:paddingBottom="10dp"
        android:id="@+id/xTripSummarySingleItemBg"
        android:background="@color/question_bg_color">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:weightSum="3"
                android:id="@+id/xTripSummaryNameLayout"
                android:background="@drawable/spinner_bg">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:layout_alignParentStart="true"
                    android:textColor="@color/question_bg_color"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="5dp"
                    android:id="@+id/xTripSummarySingleItemIndex"
                    android:padding="5dp"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="SHOPRITE MIMOSA"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_marginLeft="12dp"
                    android:id="@+id/xTripSummarySingleItemCustomerName"
                    android:layout_toEndOf="@id/xTripSummarySingleItemIndex"
                    android:layout_toStartOf="@id/xTripSummarySingleItemGoImg"
                    android:textColor="@color/black"
                    android:padding="5dp"/>
                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_arrow_right"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="5dp"
                    android:layout_centerVertical="true"
                    android:id="@+id/xTripSummarySingleItemGoImg"
                    android:layout_gravity="center_vertical"/>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/outlet_no"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="726112"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:id="@+id/xTripSummarySingleItemOutletNo"
                    android:textStyle="bold"
                    android:textColor="@color/white"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/address"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="SHOP L1 MIMOSA MALL BOUNDED BY,NELSON MANDELA DRIVE GENERAL DAN PIENNA"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:id="@+id/xTripSummarySingleItemAddress"
                    android:textStyle="bold"
                    android:textColor="@color/white"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/contact"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="JACO COETZEE AM"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:textStyle="bold"
                    android:id="@+id/xTripSummarySingleItemContact"
                    android:textColor="@color/white"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/tel_no"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:textStyle="bold"
                    android:id="@+id/xTripSummarySingleItemTelNo"
                    android:textColor="@color/white"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/deliveries"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="2"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:textStyle="bold"
                    android:id="@+id/xTripSummarySingleItemDeliveries"
                    android:textColor="@color/white"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <TextView
                    android:layout_width="140dp"
                    android:layout_height="wrap_content"
                    android:text="@string/visit_window"
                    android:textSize="20sp"
                    android:textColor="@color/white"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="08:00  15:00"
                    android:textSize="20sp"
                    android:gravity="start"
                    android:textStyle="bold"
                    android:id="@+id/xTripSummarySingleItemVisitWindow"
                    android:textColor="@color/red"/>
            </LinearLayout>


        </LinearLayout>

    </RelativeLayout>
</androidx.cardview.widget.CardView>