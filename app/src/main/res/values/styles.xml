<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>



    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.StartUp" parent="Theme.AppCompat.Light.NoActionBar"  />

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />



<!--    Loader class animated dots attributes-->
    <style name="SpotsDialogDefault" parent="android:Theme.DeviceDefault.Light.Dialog">
        <item name="DialogTitleAppearance">@android:style/TextAppearance.Medium</item>
        <item name="DialogTitleText">Loading…</item>
        <item name="DialogSpotCount">5</item>
    </style>
    <style name="dialogStyle" parent="@android:style/Theme.Holo.Light.Dialog">
        <item name="android:windowTitleStyle">@style/dialog_title_style</item>
    </style>
    <style name="dialog_title_style" parent="android:Widget.TextView">
        <item name="android:textColor">@android:color/holo_blue_light</item>
        <item name="android:textAppearance">@android:style/TextAppearance.Large</item>
    </style>


<!--    Spinner icon size   -->
    <style name="SpinnerTheme" parent="android:Widget.Spinner">
        <item name="android:background">@drawable/spinner_background</item>
    </style>
</resources>
