<resources>
    <string name="app_name">OASIS</string>

    <string name="ok">OK</string>
    <string name="continue_label">Continue</string>
    <string name="cancel">Cancel</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>

    <string name="title_activity_home">Oasis</string>
    <string name="title_activity_depot">Please Setup</string>
    <string name="title_activity_scrolling">ScrollingActivity</string>
    <string name="title_activity_driver_signature">DriverSignatureActivity</string>
    <string name="title_activity_pre_trip_inspection_security">PreTripInspectionSecurityActivity</string>
    <string name="title_activity_pre_trip_inspection_questions">PreTripInspectionQuestionsActivity</string>
    <string name="action_settings">Settings</string>
    <string name="title_activity_depot_list">ScrollingActivity</string>

    <string name="invalidResponse">No valid response from server</string>
    <string name="DepotListDownloadSuccess">Depot List Downloaded.</string>

    <string name="button_activity_depot">Setup Depot</string>
    <string name="title_driver_signature">Driver Signature</string>
    <string name="title_pre_trip_inspection">Pre Trip Inspection</string>
    <string name="title_pre_trip_inspection_variance">Pre Trip Inspection Variance</string>
    <string name="title_pre_trip_security">PreTrip : Security sign off</string>
    <string name="title_post_trip_security">PostTrip : Security sign off</string>
    <string name="title_customer_rating">Customer Rating</string>
    <string name="proceed">Proceed</string>
    <string name="confirm_and_proceed">Confirm &amp; Proceed</string>
    <string name="card_number">Card Number :</string>
    <string name="card_number_hint">Card Number</string>
    <string name="next">Next</string>
    <string name="sample_question_text">Braking System : Are the brake hoses(RED and YELLOW hoses) and electric suize between truck and trailers clean, intact and coupled correctly</string>
    <string name="title_odometer">Enter Initial Odometer</string>
    <string name="hint_odometer_reading">Odometer Reading</string>
    <string name="hint_grn_number">GRN/GRV number</string>
    <string name="hint_confirm_odometer_reading">Confirm Odometer Reading</string>
    <string name="readings_mismatch">Entered KM readings do not match.</string>
    <string name="readings_zero">Entered KM readings cannot be 0.</string>
    <string name="proceed_alert_message">Once you proceed you cannot visit this or previous screens. Are you sure?</string>
    <string name="trip_summary">Trip Summary</string>
    <string name="scan_podclaim">Scan POD/Claim</string>
    <string name="unplanned_pick">Unplanned Pick</string>
    <string name="end_trip">End Trip</string>
    <string name="outlet_no">Outlet No</string>
    <string name="address">Address</string>
    <string name="contact">Contact</string>
    <string name="tel_no">Tel No</string>
    <string name="deliveries">Deliveries</string>
    <string name="visit_window">Visit Window</string>
    <string name="unplanned_customer_list">Unplanned Customer List</string>
    <string name="search_customer">Search customer</string>
    <string name="time_window">Time Window</string>
    <string name="location">Location</string>

    <string name="downloading_image">Please Wait Downloading Images from SAP..</string>
    <string name="downloading_depot_list">Please Wait Downloading Depot List from SAP..</string>
    <string name="downloading_shipments_list">Please Wait Downloading Shipments List from SAP..</string>
    <string name="sample_text_dialog_odometer_reading">Odometer reading at SHOPRITE MIMOSA MALL 31429</string>
    <string name="sample_text_grn_grv_number">GRN/GRV Number</string>
    <string name="_1_print_input_checklist">1. Print Input Checklist</string>
    <string name="title_delivery_menu">Delivery Menu</string>
    <string name="_2_capture_fbr_or_bit">2. Capture FBR or BIT</string>
    <string name="rcr">3. RCR</string>
    <string name="_4_ubr">4. UBR</string>
    <string name="_5_full_fbr">5. Full FBR</string>
    <string name="_6_wooden_support_pallets">6. Wooden Support Pallets</string>
    <string name="_7_drop_trailers">7. Drop Trailers</string>
    <string name="_8_print_pro_forma">8. Print Pro forma</string>
    <string name="_9_print_invoice">9. Print Invoice</string>
    <string name="delivery">Delivery</string>
    <string name="prod">Prod.</string>
    <string name="split_cases">Split Cases</string>
    <string name="full_pallets">Full Pallets</string>
    <string name="clear">Clear All</string>
    <string name="title_rcr">RCR</string>
    <string name="_000">0000</string>
    <string name="add">ADD</string>
    <string name="material">Material</string>
    <string name="total_cases">Total Cases</string>
    <string name="no_data_available">No data available</string>

    <string name="title_depot_setup_activity">OASIS 2</string>
    <string name="title_depot_list_activity">Depot List</string>
    <string name="title_shipment_list_activity">Shipment List</string>

    <string name="truck_no">Truck No</string>
    <string name="driver_name">Driver</string>
    <string name="crew">Crew</string>
    <string name="trip">Trip</string>

    <string name="shipment_list_button">Shipment List</string>


    <string name="title_fbr_bit">FBR/BIT</string>
    <string name="fbr_qty">FBR Qty</string>
    <string name="bit_qty">BIT Qty</string>
    <string name="complete">Complete</string>
    <string name="reason">Reason</string>
    <string name="select_reason">Select a Reason</string>
    <string name="title_full_fbr">Full FBR</string>
    <string name="fbr_reason">FBR Reason</string>
    <string name="choose_correct_reason">Please make sure you select the right reason</string>
    <string name="title_ubr">UBR</string>
    <string name="return_qty">Return Qty</string>
    <string name="_1_trailer_pick_up">1 - Trailer PICK-UP</string>
    <string name="_2_trailer_drop_off">2 - Trailer DROP-OFF</string>
    <string name="wooden_pallets_menu">Wooden Pallets Menu</string>
    <string name="trailer_menu">Trailer Menu</string>
    <string name="_1_pallets_pick_up">1 - Pallets PICK-UP</string>
    <string name="_2_pallets_drop_off">2 - Pallets DROP-OFF</string>
    <string name="wooden_pallets_drop">Wooden Pallets Drop</string>
    <string name="wooden_pallets_pickup">Wooden Pallets Pickup</string>
    <string name="trailer_pick_up">Trailer Pick Up</string>
    <string name="trailer_drop_off">Trailer DROP OFF</string>
    <string name="product">Product</string>
    <string name="pallets_returned">Pallets Returned</string>
    <string name="pallets">Pallets</string>
    <string name="trailer_1">Trailer 1</string>
    <string name="trailer_2">Trailer 2</string>
    <string name="clear_">Clear</string>


    <string name="solid">SOLID</string>
    <string name="regular">REGULAR</string>


    //Font awesome icons
    <string name="font_awesome_truck_icon">&#xf4df;</string>
    <string name="image_placeholder">&#xf03e;</string>
    <string name="font_awesome_minus_icon">&#xf056;</string>
    <string name="please_sign">Please sign...</string>

    <string name="choose_delivery">Choose a delivery</string>
    <string name="choose_reason">Choose a reason</string>
    <string name="choose_delivery_validation">Please select a delivery</string>
    <string name="choose_reason_validation">Please select a reason</string>

    <string name="pre_trip_insp_error">Please fill in all the answers</string>
    <string name="password_label">Password</string>
    <string name="select_action_error">Select an Action</string>
    <string name="select_supervisor_error">Please select a Supervisor</string>
    <string name="password_error">Please enter the Passcode</string>
    <string name="incorrect_password_error">Incorrect Passcode. Please try again</string>
    <string name="continue_conformation">Once you proceed you cannot vist this or previous screens. Are you sure ?</string>
    <string name="title_manager_signature">Manager Signature</string>
    <string name="signature_error">Signature Mandatory</string>
    <string name="send_log_alert_message">Are you sure to send the logs to Server?\nYou need to be connected to the network via dock or wirelessly to send the logs to server.</string>
    <string name="depot_setup_alert_message">If there is shipment in the device, that would be cleared.\nAre you sure to Proceed ?</string>
    <string name="reset_ship_alert_message">Reset Shipment ?</string>
    <string name="reset_app_alert_message">Are you sure to Reset the App?\nReset will clear the application data fully and you have to start from the initial setup.</string>
    <string name="send_tosap__alert_message">Are you sure to send the App DB to Server?\nYou need to be connected to the network via dock or wirelessly to send the logs to server.</string>
    <string name="exit_app_alert_message">Are you sure you want to Exit?</string>
    <string name="successful_delete">Successfully Cleared</string>
    <string name="depot_password">Depot Password</string>
    <string name="title_scan_customer_pod_claim">Customer POD/Claim</string>
    <string name="title_end_of_visit_menu">End of Visit Menu</string>
    <string name="title_scan_customer_stamp">Scan Customer Stamp</string>
    <string name="title_scan_customer_claim">Scan Customer Claim</string>
    <string name="_2_print_vehicle_stock_report">2. Print Vehicle Stock Report</string>
    <string name="_4_end_visit_label">4. End Visit</string>
    <string name="_3_end_visit_label">3. Scan POD/Claim </string>
    <string name="_5_end_visit">5. End Visit</string>
    <string name="_3_end_visit">1. Customer POD</string>
    <string name="_4_end_visit">2. Customer Claim</string>
    <string name="select_device_alert_message">Select Bluetooth Device</string>
    <string name="title_end_of_trip_menu">End of Trip Menu</string>
    <string name="title_full_load_fbr_reason">Full Load FBR Reason</string>
    <string name="warehouse_full_fbr_reason">Warehouse Full FBR Reason</string>
    <string name="logistics_full_fbr_reason">Distribution Full FBR Reason</string>
    <string name="_1_print_final_reports">1. Print Final Reports</string>
    <string name="_2_final_odometer">2. Final Odometer</string>
    <string name="_3_Full_load_FBR_Reason">3. Full Load FBR Reason</string>
    <string name="_4_check_in">4. CHECK IN</string>
    <string name="checker_name">Checker Name</string>
    <string name="check_in">CHECK-IN</string>
    <string name="shipment">Shipment</string>
    <string name="driver">Driver</string>
    <string name="depot">Depot</string>
    <string name="check_in_bit_fbr">CHECK-IN BIT/FBR</string>
    <string name="check_in_empties_pallets">CHECK-IN Empties/pallets</string>
    <string name="password">password</string>
    <string name="change_depot">Change Depot</string>
    <string name="check_in_fulls">CHECK-IN FULLS</string>
    <string name="qty">Qty</string>
    <string name="bit">BIT</string>
    <string name="check_in_complete">Check In Complete</string>
    <string name="printerSetupmsg">Please tap Scan button to retrieve the Printer Mac Address.</string>
    <string name="confirmation">Confirmation</string>
    <string name="cancel_dialog">Do you want to continue?</string>
    <string name="print_status">Printing Status</string>
    <string name="printer_setup">Printer Setup</string>
    <string name="print_next_input_list">Click OK to Print the next Input Checklist</string>
    <string name="print_next_proforma_list">Click OK to Print the next Pro Forma</string>
    <string name="print_next_invoice_list">Click OK to Print the next Invoice</string>
    <string name="title_delivery_summary">Delivery Summary</string>
    <string name="customer_rating_title">Please rate the overall delivery experience received today</string>
    <string name="customer_rating_error">Please select a rating</string>
    <string name="planned">Planned</string>
    <string name="delivered">Delivered</string>
    <string name="take_photo">Take photo</string>
    <string name="change_photo">Edit</string>
    <string name="save">Save</string>
    <string name="add_more">Add more</string>
    <string name="submit">Submit</string>

    <string name="title_upload_menu">Upload Menu</string>

    <string name="title_customer_signature">Customer Signature</string>
    <string name="tandc">I accept and agree terms and conditions</string>
    <string name="customer_declined"><u>Customer Declined?</u></string>
    <string name="reason_for_decline">Reason For Declining</string>
    <string name="decline_reason">Please select a reason for declining</string>
    <string name="rating_label">How satisfied are you with the delivery?</string>

    <!--Play service messages-->
    <string name="playServiceError">Google Play Services Error</string>
    <string name="install">Install</string>
    <string name="update">Update</string>
    <string name="installPlayService">Please install Google Play Services</string>
    <string name="updatePlayService">Please update Google Play Services</string>
    <string name="errorPlayService">Google Play Services error</string>
    <string name="notConnected">Not connected</string>
    <string name="actionsNotEnabled">No action is enabled for you on this alert,please contact your administrator.</string>
    <string name="pre_trip_insp_title">Pre Trip Inspection</string>
    <string name="pre_trip_insp_fl_title">Forklift Inspection</string>
    <string name="post_trip_insp_title">Post Trip Inspection</string>
    <string name="post_trip_insp_fl_title">Forklift Inspection</string>
    <string name="back_text">Back</string>
    <string name="save_text">Save</string>
    <string name="ver_no">Ver. No: </string>
    <string name="msg_token_fmt">FCM Registration Token: %1$s</string>
    <string name="title_activity_full_load_fbrreason">FullLoadFBRReason</string>
    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="previous">Previous</string>

    <string name="hello_first_fragment">Hello first fragment</string>
    <string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string>
    <string name="delivery_no">Delivery: 35435435435</string>
    <string name="sap_order">SAP Order :</string>
    <string name="saving_image">Please Wait Saving Images..</string>
    <string name="bottles">Bottles</string>
</resources>
