<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorAccent">#FF4081</color>

    <color name="black">#000000</color>
    <color name="white">#FFFFFF</color>
    <color name="button_proceed_color">#404040</color>
    <color name="dialog_bg_color">#404040</color>

    <color name="spots_dialog_color">@android:color/holo_blue_dark</color>
    <color name="red">#FF0000</color>
    <color name="question_bg_color">#5b9ad5</color>
    <color name="green">#58F319</color>
    <color name="toast_color">#FAB170</color>
    <color name="depot_list_item_background">#79A3EC</color>
    <color name="depot_list_item_background_heighlight">#FAB170</color>
    <color name="table_bg_color">#5b9ad5</color>
    <color name="yellow">#FFEB3B</color>
    <color name="table_data_bg_color">#c9dce9</color>
    <color name="grey">#808080</color>
    <color name="transparent_blue">#80004488</color>
    <color name="delivery_summary_card_color">#edf0f2</color>
    <color name="transparent">#00EDF0F2</color>
    <color name="grey_100_client">#F5F5F5</color>
    <color name="grey_200_client">#EEEEEE</color>
    <color name="polygonViewCircleStrokeColor">#ff59a9ff</color>
    <color name="polygonViewCircleBackground">#90e5f8ff</color>
</resources>
