<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="Dialog">
        <attr name="DialogTitleAppearance" format="reference" />
        <attr name="DialogTitleText" format="reference|string" />
        <attr name="DialogSpotColor" format="reference|color"/>
        <attr name="DialogSpotCount" format="integer"/>
    </declare-styleable>


<!--    Font awesome attribute to define the type of font-->
    <declare-styleable name="FontAwesomeRegular" >
        <attr name="fontAwesomeStyle" format="enum">
            <enum name="SOLID" value="0" />
            <enum name="REGULAR" value="1" />
        </attr>
    </declare-styleable>
</resources>