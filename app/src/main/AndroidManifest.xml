<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.unvired.oasis">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <application
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:replace="android:allowBackup,android:icon,android:theme"
        tools:targetApi="m">
        <activity
            android:name="com.abinbev.oasis.activities.FullLoadFBRReason"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.ScanCustomerPodClaimActivity"
            android:exported="false" />
        <activity
            android:name="com.abinbev.oasis.activities.ScanCustomerClaimPod"
            android:exported="false" />
        <activity
            android:name="com.abinbev.oasis.activities.CustomerSignatureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.CustomerRatingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.DeliverySummaryForm"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.UploadMenuActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.CheckInEmptiesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.CheckInFullsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.CheckInActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PrinterSetupActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.EndOfTripMenuActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.SettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PreTripInspectionVarianceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.ManagerSignatureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.EndOfVisitMenuActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.TrailerDropOffActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.TrailerPickupActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PalletsDropOffActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PalletsPickUpActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.UBRFormActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.FullFBRFormActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.FBR_BITFormActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.RcrFormActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.DeliveryMenuActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.UnplannedCustomerListActivity"
            android:screenOrientation="portrait">



            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.MAIN" /> -->


            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            <!-- </intent-filter> -->
        </activity>

        <activity
            android:name="com.abinbev.oasis.activities.PODClaimSummaryActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.abinbev.oasis.activities.TripSummaryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.OdometerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PreTripInspectionQuestionsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.ShipmentListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.PreTripInspectionSecurityActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.DriverSignatureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.DepotListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.abinbev.oasis.activities.DepoSetupActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.unvired.ui.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.StartUp" />
        <activity
            android:name="com.abinbev.oasis.activities.StartUpActivity"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="TODO"
                    android:path="unvired.com"
                    android:scheme="unv" />
            </intent-filter>
        </activity> <!-- FCM Start -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/transparent" />
        <meta-data
            android:name="io.fabric.ApiKey"
            tools:node="remove" />

        <service android:name="com.abinbev.oasis.util.FCMReceiver"
            android:exported="false">
            <intent-filter>
                <action
                    android:name="com.google.firebase.MESSAGING_EVENT"
                    android:screenOrientation="portrait"
                    android:exported="false"/>
            </intent-filter>
        </service>
        <service android:name="com.unvired.fcm.UnviredFCMMessagingService"
            android:exported="false">
            <intent-filter>
                <action
                    android:name="com.google.firebase.MESSAGING_EVENT"
                    android:screenOrientation="portrait" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.abinbev.oasis.activities.ScanCustomerClaimPod.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>
    </application>

</manifest>