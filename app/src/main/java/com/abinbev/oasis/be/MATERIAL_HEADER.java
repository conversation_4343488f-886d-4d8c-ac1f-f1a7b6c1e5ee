//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "MATERIAL".
*/	
public class MATERIAL_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "MATERIAL_HEADER";
	
		// DSD Connector: Material Number
    public static final String FIELD_MAT_NO = "MAT_NO";

	// DSD Connector: Description 40
    public static final String FIELD_MAT_DESC = "MAT_DESC";

	// DSD Connector: Material Category
    public static final String FIELD_TYPE = "TYPE";

	// DSD Connector: Unit of Measure
    public static final String FIELD_BASE_UOM = "BASE_UOM";

	// Cs_Per_Pal
    public static final String FIELD_CS_PER_PAL = "CS_PER_PAL";

	// DSD Connector: International article number
    public static final String FIELD_EAN = "EAN";

	// General item category group
    public static final String FIELD_ITM_CAT = "ITM_CAT";

	// External Material Group
    public static final String FIELD_PACK_CODE = "PACK_CODE";

	// Description for external material group
    public static final String FIELD_PACK_DESC = "PACK_DESC";

	// Material group 5
    public static final String FIELD_MAT_GRP5 = "MAT_GRP5";

	// Description
    public static final String FIELD_MAT_GRP5_DESC = "MAT_GRP5_DESC";

	// Product hierarchy
    public static final String FIELD_PROD_HIER = "PROD_HIER";

	public MATERIAL_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getMAT_NO() { 
		return (String)getField(FIELD_MAT_NO); 
	}

	public void setMAT_NO(String value) {
		setField(FIELD_MAT_NO, value);
	}

	public String getMAT_DESC() { 
		return (String)getField(FIELD_MAT_DESC); 
	}

	public void setMAT_DESC(String value) {
		setField(FIELD_MAT_DESC, value);
	}

	public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}

	public String getBASE_UOM() { 
		return (String)getField(FIELD_BASE_UOM); 
	}

	public void setBASE_UOM(String value) {
		setField(FIELD_BASE_UOM, value);
	}

	public Long getCS_PER_PAL() { 
		return (Long)getField(FIELD_CS_PER_PAL); 
	}

	public void setCS_PER_PAL(Long value) {
		setField(FIELD_CS_PER_PAL, value);
	}

	public String getEAN() { 
		return (String)getField(FIELD_EAN); 
	}

	public void setEAN(String value) {
		setField(FIELD_EAN, value);
	}

	public String getITM_CAT() { 
		return (String)getField(FIELD_ITM_CAT); 
	}

	public void setITM_CAT(String value) {
		setField(FIELD_ITM_CAT, value);
	}

	public String getPACK_CODE() { 
		return (String)getField(FIELD_PACK_CODE); 
	}

	public void setPACK_CODE(String value) {
		setField(FIELD_PACK_CODE, value);
	}

	public String getPACK_DESC() { 
		return (String)getField(FIELD_PACK_DESC); 
	}

	public void setPACK_DESC(String value) {
		setField(FIELD_PACK_DESC, value);
	}

	public String getMAT_GRP5() { 
		return (String)getField(FIELD_MAT_GRP5); 
	}

	public void setMAT_GRP5(String value) {
		setField(FIELD_MAT_GRP5, value);
	}

	public String getMAT_GRP5_DESC() { 
		return (String)getField(FIELD_MAT_GRP5_DESC); 
	}

	public void setMAT_GRP5_DESC(String value) {
		setField(FIELD_MAT_GRP5_DESC, value);
	}

	public String getPROD_HIER() { 
		return (String)getField(FIELD_PROD_HIER); 
	}

	public void setPROD_HIER(String value) {
		setField(FIELD_PROD_HIER, value);
	}
}