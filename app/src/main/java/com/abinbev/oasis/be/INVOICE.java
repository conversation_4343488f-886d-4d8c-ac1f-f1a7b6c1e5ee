//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class INVOICE extends DataStructure {
	
	public static final String TABLE_NAME = "INVOICE";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD Connector: Invoice Number
    public static final String FIELD_INV_NO = "INV_NO";

	// DSD Connector: Customer Number
    public static final String FIELD_CUST_NO = "CUST_NO";

	// DSD connector : Delivery Number
    public static final String FIELD_DELV_NO = "DELV_NO";

	// Text (20 Characters)
    public static final String FIELD_PRN_TIMESTAMP = "PRN_TIMESTAMP";

	// Text field with length of 5
    public static final String FIELD_DECLN_RSN = "DECLN_RSN";

	// Ship-to party character
    public static final String FIELD_GRN_NO = "GRN_NO";

	// Invoice date (date created)
    public static final String FIELD_INV_DATE = "INV_DATE";

	// Custom Specific Additional Field Customer Master
    public static final String FIELD_CUST_RATING = "CUST_RATING";

	public INVOICE() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getINV_NO() { 
		return (String)getField(FIELD_INV_NO); 
	}

	public void setINV_NO(String value) {
		setField(FIELD_INV_NO, value);
	}

	public String getCUST_NO() { 
		return (String)getField(FIELD_CUST_NO); 
	}

	public void setCUST_NO(String value) {
		setField(FIELD_CUST_NO, value);
	}

	public String getDELV_NO() { 
		return (String)getField(FIELD_DELV_NO); 
	}

	public void setDELV_NO(String value) {
		setField(FIELD_DELV_NO, value);
	}

	public String getPRN_TIMESTAMP() { 
		return (String)getField(FIELD_PRN_TIMESTAMP); 
	}

	public void setPRN_TIMESTAMP(String value) {
		setField(FIELD_PRN_TIMESTAMP, value);
	}

	public String getDECLN_RSN() { 
		return (String)getField(FIELD_DECLN_RSN); 
	}

	public void setDECLN_RSN(String value) {
		setField(FIELD_DECLN_RSN, value);
	}

	public String getGRN_NO() { 
		return (String)getField(FIELD_GRN_NO); 
	}

	public void setGRN_NO(String value) {
		setField(FIELD_GRN_NO, value);
	}

	public String getINV_DATE() { 
		return (String)getField(FIELD_INV_DATE); 
	}

	public void setINV_DATE(String value) {
		setField(FIELD_INV_DATE, value);
	}

	public String getCUST_RATING() { 
		return (String)getField(FIELD_CUST_RATING); 
	}

	public void setCUST_RATING(String value) {
		setField(FIELD_CUST_RATING, value);
	}
}