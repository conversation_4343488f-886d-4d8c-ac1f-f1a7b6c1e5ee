//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class VISIT extends DataStructure {
	
	public static final String TABLE_NAME = "VISIT";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD connecter : Visit ID
    public static final String FIELD_VISIT_NO = "VISIT_NO";

	// DSD Connector: Actual visit sequence
    public static final String FIELD_SEQ_NO = "SEQ_NO";

	// DSD Connector: Customer Number
    public static final String FIELD_CUST_NO = "CUST_NO";

	// Byte Value
    public static final String FIELD_STAT = "STAT";

	// General Flag
    public static final String FIELD_HHT = "HHT";

	// DSD Connector: End Mile
    public static final String FIELD_MILE = "MILE";

	public VISIT() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public Long getVISIT_NO() { 
		return (Long)getField(FIELD_VISIT_NO); 
	}

	public void setVISIT_NO(Long value) {
		setField(FIELD_VISIT_NO, value);
	}

	public Long getSEQ_NO() { 
		return (Long)getField(FIELD_SEQ_NO); 
	}

	public void setSEQ_NO(Long value) {
		setField(FIELD_SEQ_NO, value);
	}

	public String getCUST_NO() { 
		return (String)getField(FIELD_CUST_NO); 
	}

	public void setCUST_NO(String value) {
		setField(FIELD_CUST_NO, value);
	}

	public Long getSTAT() { 
		return (Long)getField(FIELD_STAT); 
	}

	public void setSTAT(Long value) {
		setField(FIELD_STAT, value);
	}

	public String getHHT() { 
		return (String)getField(FIELD_HHT); 
	}

	public void setHHT(String value) {
		setField(FIELD_HHT, value);
	}

	public Double getMILE() { 
		return (Double)getField(FIELD_MILE); 
	}

	public void setMILE(Double value) {
		setField(FIELD_MILE, value);
	}
}