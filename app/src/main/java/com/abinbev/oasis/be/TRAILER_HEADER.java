//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "TRAILER".
*/	
public class TRAILER_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "TRAILER_HEADER";
	
		// Vehicle Number
    public static final String FIELD_TRAILER_ID = "TRAILER_ID";

	public TRAILER_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getTRAILER_ID() { 
		return (String)getField(FIELD_TRAILER_ID); 
	}

	public void setTRAILER_ID(String value) {
		setField(FIELD_TRAILER_ID, value);
	}
}