//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "QUESTION".
*/	
public class QUESTION_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "QUESTION_HEADER";
	
		// Two digit number
    public static final String FIELD_Q_ID = "Q_ID";

	// Question Type
    public static final String FIELD_TYPE = "TYPE";

	// Question text
    public static final String FIELD_QUES = "QUES";

	public QUESTION_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public Long getQ_ID() { 
		return (Long)getField(FIELD_Q_ID); 
	}

	public void setQ_ID(Long value) {
		setField(FIELD_Q_ID, value);
	}

	public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}

	public String getQUES() { 
		return (String)getField(FIELD_QUES); 
	}

	public void setQUES(String value) {
		setField(FIELD_QUES, value);
	}
}