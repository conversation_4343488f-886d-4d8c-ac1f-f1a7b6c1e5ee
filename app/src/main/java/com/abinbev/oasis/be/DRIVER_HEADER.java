//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "DRIVER".
*/	
public class DRIVER_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "DRIVER_HEADER";
	
		// Driver Number
    public static final String FIELD_DRV_ID = "DRV_ID";

	// DSD Connector: Driver Name
    public static final String FIELD_DRV_NAME = "DRV_NAME";

	public DRIVER_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getDRV_ID() { 
		return (String)getField(FIELD_DRV_ID); 
	}

	public void setDRV_ID(String value) {
		setField(FIELD_DRV_ID, value);
	}

	public String getDRV_NAME() { 
		return (String)getField(FIELD_DRV_NAME); 
	}

	public void setDRV_NAME(String value) {
		setField(FIELD_DRV_NAME, value);
	}
}