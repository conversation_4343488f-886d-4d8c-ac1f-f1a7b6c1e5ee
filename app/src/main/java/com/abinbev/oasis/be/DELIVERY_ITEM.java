//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class DELIVERY_ITEM extends DataStructure {
	
	public static final String TABLE_NAME = "DELIVERY_ITEM";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD connector : Delivery Number
    public static final String FIELD_DELV_NO = "DELV_NO";

	// DSD Connector: Item number
    public static final String FIELD_ITM_NO = "ITM_NO";

	// Original Quantity of Delivery Item
    public static final String FIELD_QTY = "QTY";

	// Sales unit
    public static final String FIELD_SLS_UOM = "SLS_UOM";

	// Material Number
    public static final String FIELD_MAT_NO = "MAT_NO";

	// Text (20 Characters)
    public static final String FIELD_PROMO_NO = "PROMO_NO";

	// Text, 40 Characters Long
    public static final String FIELD_PROMO_DESC = "PROMO_DESC";

	// Text (20 Characters)
    public static final String FIELD_PROMO_TYPE = "PROMO_TYPE";

	// Text, 40 Characters Long
    public static final String FIELD_PROMO_TYPE_DESC = "PROMO_TYPE_DESC";

	// Text (20 Characters)
    public static final String FIELD_SLS_DEAL = "SLS_DEAL";

	// Text, 40 Characters Long
    public static final String FIELD_SLS_DEAL_DESC = "SLS_DEAL_DESC";

	// Field of type DATS
    public static final String FIELD_SLS_DEAL_DT = "SLS_DEAL_DT";

	// General Flag
    public static final String FIELD_IS_EMPTY = "IS_EMPTY";

	// General Flag
    public static final String FIELD_IS_RETURN = "IS_RETURN";

	// Actual quantity delivered (in sales units)
    public static final String FIELD_RCR_QTY = "RCR_QTY";

	// Actual quantity delivered (in sales units)
    public static final String FIELD_FBR_QTY = "FBR_QTY";

	// Actual quantity delivered (in sales units)
    public static final String FIELD_BIT_QTY = "BIT_QTY";

	// Text field with length of 5
    public static final String FIELD_FBR_RSN = "FBR_RSN";

	// Numc3, internal use
    public static final String FIELD_FBR_RSN_POS = "FBR_RSN_POS";

	// Text field with length of 5
    public static final String FIELD_BIT_RSN = "BIT_RSN";

	// General Flag
    public static final String FIELD_HH_CREATED = "HH_CREATED";

	// Actual quantity delivered (in sales units)
    public static final String FIELD_UBR_QTY = "UBR_QTY";

	// DSD Connector: Description 40
    public static final String FIELD_MAT_DESC = "MAT_DESC";

	public DELIVERY_ITEM() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getDELV_NO() { 
		return (String)getField(FIELD_DELV_NO); 
	}

	public void setDELV_NO(String value) {
		setField(FIELD_DELV_NO, value);
	}

	public Long getITM_NO() { 
		return (Long)getField(FIELD_ITM_NO); 
	}

	public void setITM_NO(Long value) {
		setField(FIELD_ITM_NO, value);
	}

	public Double getQTY() { 
		return (Double)getField(FIELD_QTY); 
	}

	public void setQTY(Double value) {
		setField(FIELD_QTY, value);
	}

	public String getSLS_UOM() { 
		return (String)getField(FIELD_SLS_UOM); 
	}

	public void setSLS_UOM(String value) {
		setField(FIELD_SLS_UOM, value);
	}

	public String getMAT_NO() { 
		return (String)getField(FIELD_MAT_NO); 
	}

	public void setMAT_NO(String value) {
		setField(FIELD_MAT_NO, value);
	}

	public String getPROMO_NO() { 
		return (String)getField(FIELD_PROMO_NO); 
	}

	public void setPROMO_NO(String value) {
		setField(FIELD_PROMO_NO, value);
	}

	public String getPROMO_DESC() { 
		return (String)getField(FIELD_PROMO_DESC); 
	}

	public void setPROMO_DESC(String value) {
		setField(FIELD_PROMO_DESC, value);
	}

	public String getPROMO_TYPE() { 
		return (String)getField(FIELD_PROMO_TYPE); 
	}

	public void setPROMO_TYPE(String value) {
		setField(FIELD_PROMO_TYPE, value);
	}

	public String getPROMO_TYPE_DESC() { 
		return (String)getField(FIELD_PROMO_TYPE_DESC); 
	}

	public void setPROMO_TYPE_DESC(String value) {
		setField(FIELD_PROMO_TYPE_DESC, value);
	}

	public String getSLS_DEAL() { 
		return (String)getField(FIELD_SLS_DEAL); 
	}

	public void setSLS_DEAL(String value) {
		setField(FIELD_SLS_DEAL, value);
	}

	public String getSLS_DEAL_DESC() { 
		return (String)getField(FIELD_SLS_DEAL_DESC); 
	}

	public void setSLS_DEAL_DESC(String value) {
		setField(FIELD_SLS_DEAL_DESC, value);
	}

	public String getSLS_DEAL_DT() { 
		return (String)getField(FIELD_SLS_DEAL_DT); 
	}

	public void setSLS_DEAL_DT(String value) {
		setField(FIELD_SLS_DEAL_DT, value);
	}

	public String getIS_EMPTY() { 
		return (String)getField(FIELD_IS_EMPTY); 
	}

	public void setIS_EMPTY(String value) {
		setField(FIELD_IS_EMPTY, value);
	}

	public String getIS_RETURN() { 
		return (String)getField(FIELD_IS_RETURN); 
	}

	public void setIS_RETURN(String value) {
		setField(FIELD_IS_RETURN, value);
	}

	public Double getRCR_QTY() { 
		return (Double)getField(FIELD_RCR_QTY); 
	}

	public void setRCR_QTY(Double value) {
		setField(FIELD_RCR_QTY, value);
	}

	public Double getFBR_QTY() { 
		return (Double)getField(FIELD_FBR_QTY); 
	}

	public void setFBR_QTY(Double value) {
		setField(FIELD_FBR_QTY, value);
	}

	public Double getBIT_QTY() { 
		return (Double)getField(FIELD_BIT_QTY); 
	}

	public void setBIT_QTY(Double value) {
		setField(FIELD_BIT_QTY, value);
	}

	public String getFBR_RSN() { 
		return (String)getField(FIELD_FBR_RSN); 
	}

	public void setFBR_RSN(String value) {
		setField(FIELD_FBR_RSN, value);
	}

	public Long getFBR_RSN_POS() { 
		return (Long)getField(FIELD_FBR_RSN_POS); 
	}

	public void setFBR_RSN_POS(Long value) {
		setField(FIELD_FBR_RSN_POS, value);
	}

	public String getBIT_RSN() { 
		return (String)getField(FIELD_BIT_RSN); 
	}

	public void setBIT_RSN(String value) {
		setField(FIELD_BIT_RSN, value);
	}

	public String getHH_CREATED() { 
		return (String)getField(FIELD_HH_CREATED); 
	}

	public void setHH_CREATED(String value) {
		setField(FIELD_HH_CREATED, value);
	}

	public Double getUBR_QTY() { 
		return (Double)getField(FIELD_UBR_QTY); 
	}

	public void setUBR_QTY(Double value) {
		setField(FIELD_UBR_QTY, value);
	}

	public String getMAT_DESC() { 
		return (String)getField(FIELD_MAT_DESC); 
	}

	public void setMAT_DESC(String value) {
		setField(FIELD_MAT_DESC, value);
	}
}