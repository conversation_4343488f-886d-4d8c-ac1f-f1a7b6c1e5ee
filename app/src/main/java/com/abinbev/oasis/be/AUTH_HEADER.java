//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "AUTH".
*/	
public class AUTH_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "AUTH_HEADER";
	
		// Plant
    public static final String FIELD_DEPOT = "DEPOT";

	// Text (100 characters)
    public static final String FIELD_NAME = "NAME";

	// Text (100 characters)
    public static final String FIELD_USER_ID = "USER_ID";

	// Text Field
    public static final String FIELD_PSWD = "PSWD";

	// Text (20 Characters)
    public static final String FIELD_TYPE = "TYPE";

	public AUTH_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getDEPOT() { 
		return (String)getField(FIELD_DEPOT); 
	}

	public void setDEPOT(String value) {
		setField(FIELD_DEPOT, value);
	}

	public String getNAME() { 
		return (String)getField(FIELD_NAME); 
	}

	public void setNAME(String value) {
		setField(FIELD_NAME, value);
	}

	public String getUSER_ID() { 
		return (String)getField(FIELD_USER_ID); 
	}

	public void setUSER_ID(String value) {
		setField(FIELD_USER_ID, value);
	}

	public String getPSWD() { 
		return (String)getField(FIELD_PSWD); 
	}

	public void setPSWD(String value) {
		setField(FIELD_PSWD, value);
	}

	public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}
}