//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class DELIVERY extends DataStructure {
	
	public static final String TABLE_NAME = "DELIVERY";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD connector : Delivery Number
    public static final String FIELD_DELV_NO = "DELV_NO";

	// DSD connecter : Visit ID
    public static final String FIELD_VISIT_NO = "VISIT_NO";

	// DSD Connector: Invoice Number
    public static final String FIELD_INV_NO = "INV_NO";

	// DSD Connector: Order Number
    public static final String FIELD_ORD_NO = "ORD_NO";

	// Sales Document Type
    public static final String FIELD_ORD_TYPE = "ORD_TYPE";

	// DSD Connector: Phone number
    public static final String FIELD_ORD_TEL = "ORD_TEL";

	// DSD Connector: Shipment Type (Doc Type)
    public static final String FIELD_DELV_TYPE = "DELV_TYPE";

	// Text (20 Characters)
    public static final String FIELD_CUST_REF = "CUST_REF";

	// DSD Connector: Payment Type
    public static final String FIELD_PAY_METHOD = "PAY_METHOD";

	// Text Line
    public static final String FIELD_PAY_METHOD_DESC = "PAY_METHOD_DESC";

	// Text Field
    public static final String FIELD_PAY_MESS = "PAY_MESS";

	// Text Field
    public static final String FIELD_PAY_MSG1 = "PAY_MSG1";

	// Text Field
    public static final String FIELD_PAY_MSG2 = "PAY_MSG2";

	// Text Line
    public static final String FIELD_NAT_INV_MSG1 = "NAT_INV_MSG1";

	// Text Line
    public static final String FIELD_NAT_INV_MSG2 = "NAT_INV_MSG2";

	// Text (20 Characters)
    public static final String FIELD_BLANK_TYPE = "BLANK_TYPE";

	// Plant
    public static final String FIELD_PLANT_ID = "PLANT_ID";

	// Single-Character Indicator
    public static final String FIELD_IS_FBR = "IS_FBR";

	// Text field with length of 5
    public static final String FIELD_FULL_FBR_RSN = "FULL_FBR_RSN";

	// Text field, length 4
    public static final String FIELD_PAY_TERM = "PAY_TERM";

	// Single-Character Indicator
    public static final String FIELD_HH_CREATED = "HH_CREATED";

	// Cash Discount Percentage 1
    public static final String FIELD_PERC_DISC = "PERC_DISC";

	// Baseline Date for Due Date Calculation
    public static final String FIELD_BASELN_DT = "BASELN_DT";

	public DELIVERY() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getDELV_NO() { 
		return (String)getField(FIELD_DELV_NO); 
	}

	public void setDELV_NO(String value) {
		setField(FIELD_DELV_NO, value);
	}

	public Long getVISIT_NO() { 
		return (Long)getField(FIELD_VISIT_NO); 
	}

	public void setVISIT_NO(Long value) {
		setField(FIELD_VISIT_NO, value);
	}

	public String getINV_NO() { 
		return (String)getField(FIELD_INV_NO); 
	}

	public void setINV_NO(String value) {
		setField(FIELD_INV_NO, value);
	}

	public String getORD_NO() { 
		return (String)getField(FIELD_ORD_NO); 
	}

	public void setORD_NO(String value) {
		setField(FIELD_ORD_NO, value);
	}

	public String getORD_TYPE() { 
		return (String)getField(FIELD_ORD_TYPE); 
	}

	public void setORD_TYPE(String value) {
		setField(FIELD_ORD_TYPE, value);
	}

	public String getORD_TEL() { 
		return (String)getField(FIELD_ORD_TEL); 
	}

	public void setORD_TEL(String value) {
		setField(FIELD_ORD_TEL, value);
	}

	public String getDELV_TYPE() { 
		return (String)getField(FIELD_DELV_TYPE); 
	}

	public void setDELV_TYPE(String value) {
		setField(FIELD_DELV_TYPE, value);
	}

	public String getCUST_REF() { 
		return (String)getField(FIELD_CUST_REF); 
	}

	public void setCUST_REF(String value) {
		setField(FIELD_CUST_REF, value);
	}

	public String getPAY_METHOD() { 
		return (String)getField(FIELD_PAY_METHOD); 
	}

	public void setPAY_METHOD(String value) {
		setField(FIELD_PAY_METHOD, value);
	}

	public String getPAY_METHOD_DESC() { 
		return (String)getField(FIELD_PAY_METHOD_DESC); 
	}

	public void setPAY_METHOD_DESC(String value) {
		setField(FIELD_PAY_METHOD_DESC, value);
	}

	public String getPAY_MESS() { 
		return (String)getField(FIELD_PAY_MESS); 
	}

	public void setPAY_MESS(String value) {
		setField(FIELD_PAY_MESS, value);
	}

	public String getPAY_MSG1() { 
		return (String)getField(FIELD_PAY_MSG1); 
	}

	public void setPAY_MSG1(String value) {
		setField(FIELD_PAY_MSG1, value);
	}

	public String getPAY_MSG2() { 
		return (String)getField(FIELD_PAY_MSG2); 
	}

	public void setPAY_MSG2(String value) {
		setField(FIELD_PAY_MSG2, value);
	}

	public String getNAT_INV_MSG1() { 
		return (String)getField(FIELD_NAT_INV_MSG1); 
	}

	public void setNAT_INV_MSG1(String value) {
		setField(FIELD_NAT_INV_MSG1, value);
	}

	public String getNAT_INV_MSG2() { 
		return (String)getField(FIELD_NAT_INV_MSG2); 
	}

	public void setNAT_INV_MSG2(String value) {
		setField(FIELD_NAT_INV_MSG2, value);
	}

	public String getBLANK_TYPE() { 
		return (String)getField(FIELD_BLANK_TYPE); 
	}

	public void setBLANK_TYPE(String value) {
		setField(FIELD_BLANK_TYPE, value);
	}

	public String getPLANT_ID() { 
		return (String)getField(FIELD_PLANT_ID); 
	}

	public void setPLANT_ID(String value) {
		setField(FIELD_PLANT_ID, value);
	}

	public String getIS_FBR() { 
		return (String)getField(FIELD_IS_FBR); 
	}

	public void setIS_FBR(String value) {
		setField(FIELD_IS_FBR, value);
	}

	public String getFULL_FBR_RSN() { 
		return (String)getField(FIELD_FULL_FBR_RSN); 
	}

	public void setFULL_FBR_RSN(String value) {
		setField(FIELD_FULL_FBR_RSN, value);
	}

	public String getPAY_TERM() { 
		return (String)getField(FIELD_PAY_TERM); 
	}

	public void setPAY_TERM(String value) {
		setField(FIELD_PAY_TERM, value);
	}

	public String getHH_CREATED() { 
		return (String)getField(FIELD_HH_CREATED); 
	}

	public void setHH_CREATED(String value) {
		setField(FIELD_HH_CREATED, value);
	}

	public Double getPERC_DISC() { 
		return (Double)getField(FIELD_PERC_DISC); 
	}

	public void setPERC_DISC(Double value) {
		setField(FIELD_PERC_DISC, value);
	}

	public String getBASELN_DT() { 
		return (String)getField(FIELD_BASELN_DT); 
	}

	public void setBASELN_DT(String value) {
		setField(FIELD_BASELN_DT, value);
	}
}