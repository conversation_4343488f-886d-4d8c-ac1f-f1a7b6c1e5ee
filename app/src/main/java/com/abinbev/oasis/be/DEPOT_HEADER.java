//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "DEPOT".
*/	
public class DEPOT_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "DEPOT_HEADER";
	
		// Plant
    public static final String FIELD_DEPOT = "DEPOT";

	// Name
    public static final String FIELD_NAME = "NAME";

	// DSD Connector: Phone number
    public static final String FIELD_FAX = "FAX";

	// Text, 255 Characters
    public static final String FIELD_ADDRESS = "ADDRESS";

	// Text (20 Characters)
    public static final String FIELD_NLLA = "NLLA";

	// Text (20 Characters)
    public static final String FIELD_VAT_PESC = "VAT_PESC";

	// Text, 40 Characters Long
    public static final String FIELD_INV_MSG1 = "INV_MSG1";

	// Text, 40 Characters Long
    public static final String FIELD_INV_MSG2 = "INV_MSG2";

	// Single-Character Indicator
    public static final String FIELD_SELECTED = "SELECTED";

	public DEPOT_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getDEPOT() { 
		return (String)getField(FIELD_DEPOT); 
	}

	public void setDEPOT(String value) {
		setField(FIELD_DEPOT, value);
	}

	public String getNAME() { 
		return (String)getField(FIELD_NAME); 
	}

	public void setNAME(String value) {
		setField(FIELD_NAME, value);
	}

	public String getFAX() { 
		return (String)getField(FIELD_FAX); 
	}

	public void setFAX(String value) {
		setField(FIELD_FAX, value);
	}

	public String getADDRESS() { 
		return (String)getField(FIELD_ADDRESS); 
	}

	public void setADDRESS(String value) {
		setField(FIELD_ADDRESS, value);
	}

	public String getNLLA() { 
		return (String)getField(FIELD_NLLA); 
	}

	public void setNLLA(String value) {
		setField(FIELD_NLLA, value);
	}

	public String getVAT_PESC() { 
		return (String)getField(FIELD_VAT_PESC); 
	}

	public void setVAT_PESC(String value) {
		setField(FIELD_VAT_PESC, value);
	}

	public String getINV_MSG1() { 
		return (String)getField(FIELD_INV_MSG1); 
	}

	public void setINV_MSG1(String value) {
		setField(FIELD_INV_MSG1, value);
	}

	public String getINV_MSG2() { 
		return (String)getField(FIELD_INV_MSG2); 
	}

	public void setINV_MSG2(String value) {
		setField(FIELD_INV_MSG2, value);
	}

	public String getSELECTED() { 
		return (String)getField(FIELD_SELECTED); 
	}

	public void setSELECTED(String value) {
		setField(FIELD_SELECTED, value);
	}
}