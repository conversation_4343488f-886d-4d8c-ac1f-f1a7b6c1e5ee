//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "CUSTOMER".
*/	
public class CUSTOMER_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "CUSTOMER_HEADER";
	
		// DSD Connector: Customer Number
    public static final String FIELD_CUST_NO = "CUST_NO";

	// Text (100 characters)
    public static final String FIELD_NAME = "NAME";

	// Time
    public static final String FIELD_TIME_WIN_FROM = "TIME_WIN_FROM";

	// Time
    public static final String FIELD_TIME_WIN_TO = "TIME_WIN_TO";

	// Text, 255 Characters
    public static final String FIELD_ADDRESS = "ADDRESS";

	// Text field with length of 5
    public static final String FIELD_VAT_IND = "VAT_IND";

	// Text (20 Characters)
    public static final String FIELD_BANK_REF_NO = "BANK_REF_NO";

	// Text (20 Characters)
    public static final String FIELD_VAT_NO = "VAT_NO";

	// Text, 40 Characters Long
    public static final String FIELD_LIC_NAME = "LIC_NAME";

	// Plant
    public static final String FIELD_SOLDTO_HM_DEPOT = "SOLDTO_HM_DEPOT";

	// DSD Connector: Customer Number
    public static final String FIELD_PAYER_NO = "PAYER_NO";

	// Text (100 characters)
    public static final String FIELD_BILL_TO_NAME = "BILL_TO_NAME";

	// Text, 255 Characters
    public static final String FIELD_BILL_TO_ADDRESS = "BILL_TO_ADDRESS";

	// Text field with length of 5
    public static final String FIELD_PAY_TERM = "PAY_TERM";

	// Geo location latitude
    public static final String FIELD_GPS_LAT = "GPS_LAT";

	// Geo location longitude
    public static final String FIELD_GPS_LONG = "GPS_LONG";

	// Rawstring data
    public static final String FIELD_MAP_IMG = "MAP_IMG";

	// Single-Character Indicator
    public static final String FIELD_UNPLANNED = "UNPLANNED";

	// Single-Character Indicator
    public static final String FIELD_IS_NAT_CUST = "IS_NAT_CUST";

	// Text (20 Characters)
    public static final String FIELD_DIV = "DIV";

	// Text (20 Characters)
    public static final String FIELD_ACC_MGR = "ACC_MGR";

	// DSD Connector: Phone number
    public static final String FIELD_ACC_MGR_TEL = "ACC_MGR_TEL";

	// Accum Discount
    public static final String FIELD_ACC_DISC_IND = "ACC_DISC_IND";

	// Text (20 Characters)
    public static final String FIELD_LIC_NO = "LIC_NO";

	// Text (20 Characters)
    public static final String FIELD_ATTRIBUTES = "ATTRIBUTES";

	public CUSTOMER_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getCUST_NO() { 
		return (String)getField(FIELD_CUST_NO); 
	}

	public void setCUST_NO(String value) {
		setField(FIELD_CUST_NO, value);
	}

	public String getNAME() { 
		return (String)getField(FIELD_NAME); 
	}

	public void setNAME(String value) {
		setField(FIELD_NAME, value);
	}

	public String getTIME_WIN_FROM() { 
		return (String)getField(FIELD_TIME_WIN_FROM); 
	}

	public void setTIME_WIN_FROM(String value) {
		setField(FIELD_TIME_WIN_FROM, value);
	}

	public String getTIME_WIN_TO() { 
		return (String)getField(FIELD_TIME_WIN_TO); 
	}

	public void setTIME_WIN_TO(String value) {
		setField(FIELD_TIME_WIN_TO, value);
	}

	public String getADDRESS() { 
		return (String)getField(FIELD_ADDRESS); 
	}

	public void setADDRESS(String value) {
		setField(FIELD_ADDRESS, value);
	}

	public String getVAT_IND() { 
		return (String)getField(FIELD_VAT_IND); 
	}

	public void setVAT_IND(String value) {
		setField(FIELD_VAT_IND, value);
	}

	public String getBANK_REF_NO() { 
		return (String)getField(FIELD_BANK_REF_NO); 
	}

	public void setBANK_REF_NO(String value) {
		setField(FIELD_BANK_REF_NO, value);
	}

	public String getVAT_NO() { 
		return (String)getField(FIELD_VAT_NO); 
	}

	public void setVAT_NO(String value) {
		setField(FIELD_VAT_NO, value);
	}

	public String getLIC_NAME() { 
		return (String)getField(FIELD_LIC_NAME); 
	}

	public void setLIC_NAME(String value) {
		setField(FIELD_LIC_NAME, value);
	}

	public String getSOLDTO_HM_DEPOT() { 
		return (String)getField(FIELD_SOLDTO_HM_DEPOT); 
	}

	public void setSOLDTO_HM_DEPOT(String value) {
		setField(FIELD_SOLDTO_HM_DEPOT, value);
	}

	public String getPAYER_NO() { 
		return (String)getField(FIELD_PAYER_NO); 
	}

	public void setPAYER_NO(String value) {
		setField(FIELD_PAYER_NO, value);
	}

	public String getBILL_TO_NAME() { 
		return (String)getField(FIELD_BILL_TO_NAME); 
	}

	public void setBILL_TO_NAME(String value) {
		setField(FIELD_BILL_TO_NAME, value);
	}

	public String getBILL_TO_ADDRESS() { 
		return (String)getField(FIELD_BILL_TO_ADDRESS); 
	}

	public void setBILL_TO_ADDRESS(String value) {
		setField(FIELD_BILL_TO_ADDRESS, value);
	}

	public String getPAY_TERM() { 
		return (String)getField(FIELD_PAY_TERM); 
	}

	public void setPAY_TERM(String value) {
		setField(FIELD_PAY_TERM, value);
	}

	public Double getGPS_LAT() { 
		return (Double)getField(FIELD_GPS_LAT); 
	}

	public void setGPS_LAT(Double value) {
		setField(FIELD_GPS_LAT, value);
	}

	public Double getGPS_LONG() { 
		return (Double)getField(FIELD_GPS_LONG); 
	}

	public void setGPS_LONG(Double value) {
		setField(FIELD_GPS_LONG, value);
	}

	public String getMAP_IMG() { 
		return (String)getField(FIELD_MAP_IMG); 
	}

	public void setMAP_IMG(String value) {
		setField(FIELD_MAP_IMG, value);
	}

	public String getUNPLANNED() { 
		return (String)getField(FIELD_UNPLANNED); 
	}

	public void setUNPLANNED(String value) {
		setField(FIELD_UNPLANNED, value);
	}

	public String getIS_NAT_CUST() { 
		return (String)getField(FIELD_IS_NAT_CUST); 
	}

	public void setIS_NAT_CUST(String value) {
		setField(FIELD_IS_NAT_CUST, value);
	}

	public String getDIV() { 
		return (String)getField(FIELD_DIV); 
	}

	public void setDIV(String value) {
		setField(FIELD_DIV, value);
	}

	public String getACC_MGR() { 
		return (String)getField(FIELD_ACC_MGR); 
	}

	public void setACC_MGR(String value) {
		setField(FIELD_ACC_MGR, value);
	}

	public String getACC_MGR_TEL() { 
		return (String)getField(FIELD_ACC_MGR_TEL); 
	}

	public void setACC_MGR_TEL(String value) {
		setField(FIELD_ACC_MGR_TEL, value);
	}

	public String getACC_DISC_IND() { 
		return (String)getField(FIELD_ACC_DISC_IND); 
	}

	public void setACC_DISC_IND(String value) {
		setField(FIELD_ACC_DISC_IND, value);
	}

	public String getLIC_NO() { 
		return (String)getField(FIELD_LIC_NO); 
	}

	public void setLIC_NO(String value) {
		setField(FIELD_LIC_NO, value);
	}

	public String getATTRIBUTES() { 
		return (String)getField(FIELD_ATTRIBUTES); 
	}

	public void setATTRIBUTES(String value) {
		setField(FIELD_ATTRIBUTES, value);
	}
}