//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class ATTACHMENT extends DataStructure {
	
	public static final String TABLE_NAME = "ATTACHMENT";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// Two digit number
    public static final String FIELD_TYPE = "TYPE";

	// Field of type TIMS
    public static final String FIELD_TIME = "TIME";

	// Text (20 Characters)
    public static final String FIELD_REF_NO = "REF_NO";

	// Text (20 Characters)
    public static final String FIELD_SIGNED_BY = "SIGNED_BY";

	// Rawstring data
    public static final String FIELD_DATA = "DATA";

	// Text field with length of 5
    public static final String FIELD_DATA_TYPE = "DATA_TYPE";

	public ATTACHMENT() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public Long getTYPE() { 
		return (Long)getField(FIELD_TYPE); 
	}

	public void setTYPE(Long value) {
		setField(FIELD_TYPE, value);
	}

	public String getTIME() { 
		return (String)getField(FIELD_TIME); 
	}

	public void setTIME(String value) {
		setField(FIELD_TIME, value);
	}

	public String getREF_NO() { 
		return (String)getField(FIELD_REF_NO); 
	}

	public void setREF_NO(String value) {
		setField(FIELD_REF_NO, value);
	}

	public String getSIGNED_BY() { 
		return (String)getField(FIELD_SIGNED_BY); 
	}

	public void setSIGNED_BY(String value) {
		setField(FIELD_SIGNED_BY, value);
	}

	public String getDATA() { 
		return (String)getField(FIELD_DATA); 
	}

	public void setDATA(String value) {
		setField(FIELD_DATA, value);
	}

	public String getDATA_TYPE() { 
		return (String)getField(FIELD_DATA_TYPE); 
	}

	public void setDATA_TYPE(String value) {
		setField(FIELD_DATA_TYPE, value);
	}
}