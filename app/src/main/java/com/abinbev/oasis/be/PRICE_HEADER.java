//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;
import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "PRICE".
*/

public class PRICE_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "PRICE_HEADER";
	
	// DSD Connector: Material Number
    public static final String FIELD_MAT_NO = "MAT_NO";

	// DSD Connector: Currency
    public static final String FIELD_CUR = "CUR";

	// DSD Connector: Net Price
    public static final String FIELD_PRICE = "PRICE";

	// Pricing: UOM
    public static final String FIELD_UOM = "UOM";

	public PRICE_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

	public String getMAT_NO() {
		return (String)getField(FIELD_MAT_NO); 
	}

	public void setMAT_NO(String value) {
		setField(FIELD_MAT_NO, value);
	}

	public String getCUR() { 
		return (String)getField(FIELD_CUR); 
	}

	public void setCUR(String value) {
		setField(FIELD_CUR, value);
	}

	public Double getPRICE() { 
		return (Double)getField(FIELD_PRICE); 
	}

	public void setPRICE(Double value) {
		setField(FIELD_PRICE, value);
	}

	public String getUOM() { 
		return (String)getField(FIELD_UOM); 
	}

	public void setUOM(String value) {
		setField(FIELD_UOM, value);
	}
}