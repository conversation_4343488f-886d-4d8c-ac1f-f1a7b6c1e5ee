//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class NUM_RANGE extends DataStructure {
	
	public static final String TABLE_NAME = "NUM_RANGE";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD Connector: Prefix
    public static final String FIELD_PREFIX = "PREFIX";

	// DSD Connector: Document Type
    public static final String FIELD_DOC_TYPE = "DOC_TYPE";

	// DSD Connector: Document Numbering 8 byte
    public static final String FIELD_LAST_USED_NUM = "LAST_USED_NUM";

	// DSD Connector: Extension field(30Char)
    public static final String FIELD_MOBILE_USER = "MOBILE_USER";

	public NUM_RANGE() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getPREFIX() { 
		return (String)getField(FIELD_PREFIX); 
	}

	public void setPREFIX(String value) {
		setField(FIELD_PREFIX, value);
	}

	public String getDOC_TYPE() { 
		return (String)getField(FIELD_DOC_TYPE); 
	}

	public void setDOC_TYPE(String value) {
		setField(FIELD_DOC_TYPE, value);
	}

	public String getLAST_USED_NUM() { 
		return (String)getField(FIELD_LAST_USED_NUM); 
	}

	public void setLAST_USED_NUM(String value) {
		setField(FIELD_LAST_USED_NUM, value);
	}

	public String getMOBILE_USER() { 
		return (String)getField(FIELD_MOBILE_USER); 
	}

	public void setMOBILE_USER(String value) {
		setField(FIELD_MOBILE_USER, value);
	}
}