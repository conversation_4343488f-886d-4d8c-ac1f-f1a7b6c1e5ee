//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "REASON".
*/	
public class REASON_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "REASON_HEADER";
	
		// Text (20 Characters)
    public static final String FIELD_TYPE = "TYPE";

	// Text field with length of 5
    public static final String FIELD_RSN_CODE = "RSN_CODE";

	// Text, 40 Characters Long
    public static final String FIELD_RSN_DESC = "RSN_DESC";

	// General Flag
    public static final String FIELD_IS_DEFAULT = "IS_DEFAULT";

	public REASON_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}

	public String getRSN_CODE() { 
		return (String)getField(FIELD_RSN_CODE); 
	}

	public void setRSN_CODE(String value) {
		setField(FIELD_RSN_CODE, value);
	}

	public String getRSN_DESC() { 
		return (String)getField(FIELD_RSN_DESC); 
	}

	public void setRSN_DESC(String value) {
		setField(FIELD_RSN_DESC, value);
	}

	public String getIS_DEFAULT() { 
		return (String)getField(FIELD_IS_DEFAULT); 
	}

	public void setIS_DEFAULT(String value) {
		setField(FIELD_IS_DEFAULT, value);
	}
}