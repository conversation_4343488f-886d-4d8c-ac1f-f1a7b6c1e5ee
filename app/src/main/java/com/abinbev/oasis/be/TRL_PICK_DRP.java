//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class TRL_PICK_DRP extends DataStructure {
	
	public static final String TABLE_NAME = "TRL_PICK_DRP";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD Connector: Customer Number
    public static final String FIELD_CUST_NO = "CUST_NO";

	// Vehicle Number
    public static final String FIELD_TRL_NO = "TRL_NO";

	// Two digit number
    public static final String FIELD_TRL_TYPE = "TRL_TYPE";

	// General Flag
    public static final String FIELD_IS_DRP = "IS_DRP";

	public TRL_PICK_DRP() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getCUST_NO() { 
		return (String)getField(FIELD_CUST_NO); 
	}

	public void setCUST_NO(String value) {
		setField(FIELD_CUST_NO, value);
	}

	public String getTRL_NO() { 
		return (String)getField(FIELD_TRL_NO); 
	}

	public void setTRL_NO(String value) {
		setField(FIELD_TRL_NO, value);
	}

	public Long getTRL_TYPE() { 
		return (Long)getField(FIELD_TRL_TYPE); 
	}

	public void setTRL_TYPE(Long value) {
		setField(FIELD_TRL_TYPE, value);
	}

	public String getIS_DRP() { 
		return (String)getField(FIELD_IS_DRP); 
	}

	public void setIS_DRP(String value) {
		setField(FIELD_IS_DRP, value);
	}
}