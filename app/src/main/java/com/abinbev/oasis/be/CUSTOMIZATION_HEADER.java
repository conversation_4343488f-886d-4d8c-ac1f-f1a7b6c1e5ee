//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "CUSTOMIZATION".
*/	
public class CUSTOMIZATION_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "CUSTOMIZATION_HEADER";
	
		// Comment
    public static final String FIELD_KEY_NAME = "KEY_NAME";

	// Comment
    public static final String FIELD_KEY_VALUE = "KEY_VALUE";

	// Text (100 characters)
    public static final String FIELD_KEY_DESC = "KEY_DESC";

	public CUSTOMIZATION_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getKEY_NAME() { 
		return (String)getField(FIELD_KEY_NAME); 
	}

	public void setKEY_NAME(String value) {
		setField(FIELD_KEY_NAME, value);
	}

	public String getKEY_VALUE() { 
		return (String)getField(FIELD_KEY_VALUE); 
	}

	public void setKEY_VALUE(String value) {
		setField(FIELD_KEY_VALUE, value);
	}

	public String getKEY_DESC() { 
		return (String)getField(FIELD_KEY_DESC); 
	}

	public void setKEY_DESC(String value) {
		setField(FIELD_KEY_DESC, value);
	}
}