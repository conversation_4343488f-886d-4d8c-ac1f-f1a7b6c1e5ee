//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "IMAGE".
*/	
public class IMAGE_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "IMAGE_HEADER";
	
		// Text field length 255: texts
    public static final String FIELD_IMG_NAME = "IMG_NAME";

	// Short description of contents
    public static final String FIELD_IMG_DESC = "IMG_DESC";

	// No desc available
    public static final String FIELD_IMG = "IMG";

	public IMAGE_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getIMG_NAME() { 
		return (String)getField(FIELD_IMG_NAME); 
	}

	public void setIMG_NAME(String value) {
		setField(FIELD_IMG_NAME, value);
	}

	public String getIMG_DESC() { 
		return (String)getField(FIELD_IMG_DESC); 
	}

	public void setIMG_DESC(String value) {
		setField(FIELD_IMG_DESC, value);
	}

	public String getIMG() { 
		return (String)getField(FIELD_IMG); 
	}

	public void setIMG(String value) {
		setField(FIELD_IMG, value);
	}
}