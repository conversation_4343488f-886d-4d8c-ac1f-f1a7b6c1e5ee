//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "INPUT_GET_SHIPMENTS".
*/	
public class INPUT_GET_SHIPMENTS_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "INPUT_GET_SHIPMENTS_HEADER";
	
		// Plant
    public static final String FIELD_DEPOT = "DEPOT";

	public INPUT_GET_SHIPMENTS_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getDEPOT() { 
		return (String)getField(FIELD_DEPOT); 
	}

	public void setDEPOT(String value) {
		setField(FIELD_DEPOT, value);
	}
}