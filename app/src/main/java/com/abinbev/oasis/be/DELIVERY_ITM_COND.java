//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class DELIVERY_ITM_COND extends DataStructure {
	
	public static final String TABLE_NAME = "DELIVERY_ITM_COND";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD connector : Delivery Number
    public static final String FIELD_DELV_NO = "DELV_NO";

	// DSD Connector: Item number
    public static final String FIELD_ITM_NO = "ITM_NO";

	// Condition Type
    public static final String FIELD_COND_TYPE_ID = "COND_TYPE_ID";

	// Currency amount in BAPI interfaces
    public static final String FIELD_RATE = "RATE";

	// Currency amount in BAPI interfaces
    public static final String FIELD_VAL = "VAL";

	// Base Unit of Measure
    public static final String FIELD_COND_UNIT = "COND_UNIT";

	public DELIVERY_ITM_COND() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getDELV_NO() { 
		return (String)getField(FIELD_DELV_NO); 
	}

	public void setDELV_NO(String value) {
		setField(FIELD_DELV_NO, value);
	}

	public Long getITM_NO() { 
		return (Long)getField(FIELD_ITM_NO); 
	}

	public void setITM_NO(Long value) {
		setField(FIELD_ITM_NO, value);
	}

	public String getCOND_TYPE_ID() { 
		return (String)getField(FIELD_COND_TYPE_ID); 
	}

	public void setCOND_TYPE_ID(String value) {
		setField(FIELD_COND_TYPE_ID, value);
	}

	public Double getRATE() { 
		return (Double)getField(FIELD_RATE); 
	}

	public void setRATE(Double value) {
		setField(FIELD_RATE, value);
	}

	public Double getVAL() { 
		return (Double)getField(FIELD_VAL); 
	}

	public void setVAL(Double value) {
		setField(FIELD_VAL, value);
	}

	public String getCOND_UNIT() { 
		return (String)getField(FIELD_COND_UNIT); 
	}

	public void setCOND_UNIT(String value) {
		setField(FIELD_COND_UNIT, value);
	}
}