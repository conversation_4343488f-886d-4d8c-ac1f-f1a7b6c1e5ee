//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "ANSWER_OPTION".
*/	
public class ANSWER_OPTION_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "ANSWER_OPTION_HEADER";
	
		// Two digit number
    public static final String FIELD_Q_ID = "Q_ID";

	// 5 Character Numeric NUMC
    public static final String FIELD_SEQ = "SEQ";

	// Two digit number
    public static final String FIELD_ANS_ID = "ANS_ID";

	// Text for Q and Ans
    public static final String FIELD_ANS_TEXT = "ANS_TEXT";

	// General Flag
    public static final String FIELD_IS_AUTH_REQD = "IS_AUTH_REQD";

	public ANSWER_OPTION_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public Long getQ_ID() { 
		return (Long)getField(FIELD_Q_ID); 
	}

	public void setQ_ID(Long value) {
		setField(FIELD_Q_ID, value);
	}

	public Long getSEQ() { 
		return (Long)getField(FIELD_SEQ); 
	}

	public void setSEQ(Long value) {
		setField(FIELD_SEQ, value);
	}

	public Long getANS_ID() { 
		return (Long)getField(FIELD_ANS_ID); 
	}

	public void setANS_ID(Long value) {
		setField(FIELD_ANS_ID, value);
	}

	public String getANS_TEXT() { 
		return (String)getField(FIELD_ANS_TEXT); 
	}

	public void setANS_TEXT(String value) {
		setField(FIELD_ANS_TEXT, value);
	}

	public String getIS_AUTH_REQD() { 
		return (String)getField(FIELD_IS_AUTH_REQD); 
	}

	public void setIS_AUTH_REQD(String value) {
		setField(FIELD_IS_AUTH_REQD, value);
	}
}