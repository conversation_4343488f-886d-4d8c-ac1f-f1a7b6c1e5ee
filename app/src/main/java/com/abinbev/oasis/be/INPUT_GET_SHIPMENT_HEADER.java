//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "INPUT_GET_SHIPMENT".
*/	
public class INPUT_GET_SHIPMENT_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "INPUT_GET_SHIPMENT_HEADER";
	
		// Plant
    public static final String FIELD_DEPOT_NO = "DEPOT_NO";

	// General Flag
    public static final String FIELD_DNLD_MASTERDATA = "DNLD_MASTERDATA";

	// Character 100
    public static final String FIELD_MOBILE_USER = "MOBILE_USER";

	// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	public INPUT_GET_SHIPMENT_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getDEPOT_NO() { 
		return (String)getField(FIELD_DEPOT_NO); 
	}

	public void setDEPOT_NO(String value) {
		setField(FIELD_DEPOT_NO, value);
	}

	public String getDNLD_MASTERDATA() { 
		return (String)getField(FIELD_DNLD_MASTERDATA); 
	}

	public void setDNLD_MASTERDATA(String value) {
		setField(FIELD_DNLD_MASTERDATA, value);
	}

	public String getMOBILE_USER() { 
		return (String)getField(FIELD_MOBILE_USER); 
	}

	public void setMOBILE_USER(String value) {
		setField(FIELD_MOBILE_USER, value);
	}

	public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}
}