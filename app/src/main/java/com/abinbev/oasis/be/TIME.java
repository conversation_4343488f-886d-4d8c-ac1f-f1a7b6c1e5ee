//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class TIME extends DataStructure {
	
	public static final String TABLE_NAME = "TIME";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// Time Type
    public static final String FIELD_TYPE = "TYPE";

	// Text (20 Characters)
    public static final String FIELD_TIME = "TIME";

	// Text (length 35)
    public static final String FIELD_ID = "ID";

	// Geo location latitude
    public static final String FIELD_GPS_LAT = "GPS_LAT";

	// Geo location longitude
    public static final String FIELD_GPS_LONG = "GPS_LONG";

	public TIME() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}

	public String getTIME() { 
		return (String)getField(FIELD_TIME); 
	}

	public void setTIME(String value) {
		setField(FIELD_TIME, value);
	}

	public String getID() { 
		return (String)getField(FIELD_ID); 
	}

	public void setID(String value) {
		setField(FIELD_ID, value);
	}

	public Double getGPS_LAT() { 
		return (Double)getField(FIELD_GPS_LAT); 
	}

	public void setGPS_LAT(Double value) {
		setField(FIELD_GPS_LAT, value);
	}

	public Double getGPS_LONG() { 
		return (Double)getField(FIELD_GPS_LONG); 
	}

	public void setGPS_LONG(Double value) {
		setField(FIELD_GPS_LONG, value);
	}
}