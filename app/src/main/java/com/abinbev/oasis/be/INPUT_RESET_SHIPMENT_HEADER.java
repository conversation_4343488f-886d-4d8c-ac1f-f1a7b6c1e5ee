//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "INPUT_RESET_SHIPMENT".
*/	
public class INPUT_RESET_SHIPMENT_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "INPUT_RESET_SHIPMENT_HEADER";
	
		// Text (100 characters)
    public static final String FIELD_MOBILE_USER = "MOBILE_USER";

	// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	public INPUT_RESET_SHIPMENT_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getMOBILE_USER() { 
		return (String)getField(FIELD_MOBILE_USER); 
	}

	public void setMOBILE_USER(String value) {
		setField(FIELD_MOBILE_USER, value);
	}

	public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}
}