//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class STOCK extends DataStructure {
	
	public static final String TABLE_NAME = "STOCK";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// Single-Character Indicator
    public static final String FIELD_TYPE = "TYPE";

	// DSD Connector: Material Number
    public static final String FIELD_MAT_NO = "MAT_NO";

	// DSD Connector: Quantity
    public static final String FIELD_ACT_QTY = "ACT_QTY";

	// DSD Connector: Unit of Measure
    public static final String FIELD_UOM = "UOM";

	// DSD Connector: Quantity
    public static final String FIELD_PLN_QTY = "PLN_QTY";

	// DSD Connector: Description 40
    public static final String FIELD_MAT_DESC = "MAT_DESC";

	// DSD Connector: Quantity
    public static final String FIELD_CHCK_IN_QTY = "CHCK_IN_QTY";

	// Text field with length of 5
    public static final String FIELD_RSN = "RSN";

	public STOCK() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getTYPE() { 
		return (String)getField(FIELD_TYPE); 
	}

	public void setTYPE(String value) {
		setField(FIELD_TYPE, value);
	}

	public String getMAT_NO() { 
		return (String)getField(FIELD_MAT_NO); 
	}

	public void setMAT_NO(String value) {
		setField(FIELD_MAT_NO, value);
	}

	public Double getACT_QTY() { 
		return (Double)getField(FIELD_ACT_QTY); 
	}

	public void setACT_QTY(Double value) {
		setField(FIELD_ACT_QTY, value);
	}

	public String getUOM() { 
		return (String)getField(FIELD_UOM); 
	}

	public void setUOM(String value) {
		setField(FIELD_UOM, value);
	}

	public Double getPLN_QTY() { 
		return (Double)getField(FIELD_PLN_QTY); 
	}

	public void setPLN_QTY(Double value) {
		setField(FIELD_PLN_QTY, value);
	}

	public String getMAT_DESC() { 
		return (String)getField(FIELD_MAT_DESC); 
	}

	public void setMAT_DESC(String value) {
		setField(FIELD_MAT_DESC, value);
	}

	public Double getCHCK_IN_QTY() { 
		return (Double)getField(FIELD_CHCK_IN_QTY); 
	}

	public void setCHCK_IN_QTY(Double value) {
		setField(FIELD_CHCK_IN_QTY, value);
	}

	public String getRSN() { 
		return (String)getField(FIELD_RSN); 
	}

	public void setRSN(String value) {
		setField(FIELD_RSN, value);
	}
}