//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class INVOICE_ITEM extends DataStructure {
	
	public static final String TABLE_NAME = "INVOICE_ITEM";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// DSD Connector: Invoice Number
    public static final String FIELD_INV_NO = "INV_NO";

	// DSD connector : Delivery Number
    public static final String FIELD_DELV_NO = "DELV_NO";

	// DSD Connector: Item number
    public static final String FIELD_DELV_ITM_NO = "DELV_ITM_NO";

	// DSD Connector: Price
    public static final String FIELD_NET_VALUE = "NET_VALUE";

	public INVOICE_ITEM() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getINV_NO() { 
		return (String)getField(FIELD_INV_NO); 
	}

	public void setINV_NO(String value) {
		setField(FIELD_INV_NO, value);
	}

	public String getDELV_NO() { 
		return (String)getField(FIELD_DELV_NO); 
	}

	public void setDELV_NO(String value) {
		setField(FIELD_DELV_NO, value);
	}

	public Long getDELV_ITM_NO() { 
		return (Long)getField(FIELD_DELV_ITM_NO); 
	}

	public void setDELV_ITM_NO(Long value) {
		setField(FIELD_DELV_ITM_NO, value);
	}

	public Double getNET_VALUE() { 
		return (Double)getField(FIELD_NET_VALUE); 
	}

	public void setNET_VALUE(Double value) {
		setField(FIELD_NET_VALUE, value);
	}
}