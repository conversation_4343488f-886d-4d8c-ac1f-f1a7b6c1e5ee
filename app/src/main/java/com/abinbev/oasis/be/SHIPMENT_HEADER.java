//	Generated using Unvired Modeller - Build R-4.000.0139

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/
public class SHIPMENT_HEADER extends DataStructure {

	public static final String TABLE_NAME = "SHIPMENT_HEADER";

	// Shipment Number
	public static final String FIELD_SHIP_NO = "SHIP_NO";

	// Driver Number
	public static final String FIELD_DRV1 = "DRV1";

	// Driver Number
	public static final String FIELD_DRV2 = "DRV2";

	// Vehicle Number
	public static final String FIELD_TRUCK = "TRUCK";

	// Vehicle Number
	public static final String FIELD_TRL1 = "TRL1";

	// Vehicle Number
	public static final String FIELD_TRL2 = "TRL2";

	// Crew Count
	public static final String FIELD_CREW = "CREW";

	// DSD - RA:  Number of Transmission
	public static final String FIELD_TRIP = "TRIP";

	// DSD Connector: Driver Name
	public static final String FIELD_DRV1_NAME = "DRV1_NAME";

	// DSD Connector: Driver Name
	public static final String FIELD_DRV2_NAME = "DRV2_NAME";

	// DSD Connector: Begin Mile
	public static final String FIELD_START_MILE = "START_MILE";

	// DSD Connector: End Mile
	public static final String FIELD_END_MILE = "END_MILE";

	// Vehicle Type
	public static final String FIELD_TRUCK_TYPE = "TRUCK_TYPE";

	// Byte Value
	public static final String FIELD_STAT = "STAT";

	// 2 byte integer (signed)
	public static final String FIELD_RCR_LIMIT = "RCR_LIMIT";

	// Vehicle Number
	public static final String FIELD_TRL1_P = "TRL1_P";

	// Vehicle Number
	public static final String FIELD_TRL2_P = "TRL2_P";

	// Plant
	public static final String FIELD_DEPOT = "DEPOT";

	// Shipment Reasons
	public static final String FIELD_FULL_FBR_RSN_CODE = "FULL_FBR_RSN_CODE";

	public SHIPMENT_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

	public String getSHIP_NO() {
		return (String)getField(FIELD_SHIP_NO);
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public String getDRV1() {
		return (String)getField(FIELD_DRV1);
	}

	public void setDRV1(String value) {
		setField(FIELD_DRV1, value);
	}

	public String getDRV2() {
		return (String)getField(FIELD_DRV2);
	}

	public void setDRV2(String value) {
		setField(FIELD_DRV2, value);
	}

	public String getTRUCK() {
		return (String)getField(FIELD_TRUCK);
	}

	public void setTRUCK(String value) {
		setField(FIELD_TRUCK, value);
	}

	public String getTRL1() {
		return (String)getField(FIELD_TRL1);
	}

	public void setTRL1(String value) {
		setField(FIELD_TRL1, value);
	}

	public String getTRL2() {
		return (String)getField(FIELD_TRL2);
	}

	public void setTRL2(String value) {
		setField(FIELD_TRL2, value);
	}

	public Long getCREW() {
		return (Long)getField(FIELD_CREW);
	}

	public void setCREW(Long value) {
		setField(FIELD_CREW, value);
	}

	public Long getTRIP() {
		return (Long)getField(FIELD_TRIP);
	}

	public void setTRIP(Long value) {
		setField(FIELD_TRIP, value);
	}

	public String getDRV1_NAME() {
		return (String)getField(FIELD_DRV1_NAME);
	}

	public void setDRV1_NAME(String value) {
		setField(FIELD_DRV1_NAME, value);
	}

	public String getDRV2_NAME() {
		return (String)getField(FIELD_DRV2_NAME);
	}

	public void setDRV2_NAME(String value) {
		setField(FIELD_DRV2_NAME, value);
	}

	public Double getSTART_MILE() {
		return (Double)getField(FIELD_START_MILE);
	}

	public void setSTART_MILE(Double value) {
		setField(FIELD_START_MILE, value);
	}

	public Double getEND_MILE() {
		return (Double)getField(FIELD_END_MILE);
	}

	public void setEND_MILE(Double value) {
		setField(FIELD_END_MILE, value);
	}

	public String getTRUCK_TYPE() {
		return (String)getField(FIELD_TRUCK_TYPE);
	}

	public void setTRUCK_TYPE(String value) {
		setField(FIELD_TRUCK_TYPE, value);
	}

	public Long getSTAT() {
		return (Long)getField(FIELD_STAT);
	}

	public void setSTAT(Long value) {
		setField(FIELD_STAT, value);
	}

	public Long getRCR_LIMIT() {
		return (Long)getField(FIELD_RCR_LIMIT);
	}

	public void setRCR_LIMIT(Long value) {
		setField(FIELD_RCR_LIMIT, value);
	}

	public String getTRL1_P() {
		return (String)getField(FIELD_TRL1_P);
	}

	public void setTRL1_P(String value) {
		setField(FIELD_TRL1_P, value);
	}

	public String getTRL2_P() {
		return (String)getField(FIELD_TRL2_P);
	}

	public void setTRL2_P(String value) {
		setField(FIELD_TRL2_P, value);
	}

	public String getDEPOT() {
		return (String)getField(FIELD_DEPOT);
	}

	public void setDEPOT(String value) {
		setField(FIELD_DEPOT, value);
	}

	public String getFULL_FBR_RSN_CODE() {
		return (String)getField(FIELD_FULL_FBR_RSN_CODE);
	}

	public void setFULL_FBR_RSN_CODE(String value) {
		setField(FIELD_FULL_FBR_RSN_CODE, value);
	}
}