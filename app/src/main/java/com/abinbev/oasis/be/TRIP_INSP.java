//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "SHIPMENT".
*/	
public class TRIP_INSP extends DataStructure {
	
	public static final String TABLE_NAME = "TRIP_INSP";
	
		// Shipment Number
    public static final String FIELD_SHIP_NO = "SHIP_NO";

	// Two digit number
    public static final String FIELD_Q_ID = "Q_ID";

	// 5 Character Numeric NUMC
    public static final String FIELD_SEQ = "SEQ";

	// Two digit number
    public static final String FIELD_PRIO = "PRIO";

	// Two digit number
    public static final String FIELD_ANS_ID = "ANS_ID";

	// 5 Character Numeric NUMC
    public static final String FIELD_REASON = "REASON";

	// Text (20 Characters)
    public static final String FIELD_AUTH_BY = "AUTH_BY";

	// Text for Q and Ans
    public static final String FIELD_ANS_TEXT = "ANS_TEXT";

	// answer type value range
    public static final String FIELD_ANS_TYPE = "ANS_TYPE";

	// General Flag
    public static final String FIELD_MULTI_SEL = "MULTI_SEL";

	// Question Type
    public static final String FIELD_INSP_TYPE = "INSP_TYPE";

	public TRIP_INSP() throws DBException {
		super(TABLE_NAME, false);
	}

		public String getSHIP_NO() { 
		return (String)getField(FIELD_SHIP_NO); 
	}

	public void setSHIP_NO(String value) {
		setField(FIELD_SHIP_NO, value);
	}

	public Long getQ_ID() { 
		return (Long)getField(FIELD_Q_ID); 
	}

	public void setQ_ID(Long value) {
		setField(FIELD_Q_ID, value);
	}

	public Long getSEQ() { 
		return (Long)getField(FIELD_SEQ); 
	}

	public void setSEQ(Long value) {
		setField(FIELD_SEQ, value);
	}

	public Long getPRIO() { 
		return (Long)getField(FIELD_PRIO); 
	}

	public void setPRIO(Long value) {
		setField(FIELD_PRIO, value);
	}

	public Long getANS_ID() { 
		return (Long)getField(FIELD_ANS_ID); 
	}

	public void setANS_ID(Long value) {
		setField(FIELD_ANS_ID, value);
	}

	public Long getREASON() { 
		return (Long)getField(FIELD_REASON); 
	}

	public void setREASON(Long value) {
		setField(FIELD_REASON, value);
	}

	public String getAUTH_BY() { 
		return (String)getField(FIELD_AUTH_BY); 
	}

	public void setAUTH_BY(String value) {
		setField(FIELD_AUTH_BY, value);
	}

	public String getANS_TEXT() { 
		return (String)getField(FIELD_ANS_TEXT); 
	}

	public void setANS_TEXT(String value) {
		setField(FIELD_ANS_TEXT, value);
	}

	public Long getANS_TYPE() { 
		return (Long)getField(FIELD_ANS_TYPE); 
	}

	public void setANS_TYPE(Long value) {
		setField(FIELD_ANS_TYPE, value);
	}

	public String getMULTI_SEL() { 
		return (String)getField(FIELD_MULTI_SEL); 
	}

	public void setMULTI_SEL(String value) {
		setField(FIELD_MULTI_SEL, value);
	}

	public String getINSP_TYPE() { 
		return (String)getField(FIELD_INSP_TYPE); 
	}

	public void setINSP_TYPE(String value) {
		setField(FIELD_INSP_TYPE, value);
	}
}