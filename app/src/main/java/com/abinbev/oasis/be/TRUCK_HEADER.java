//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "TRUCK".
*/	
public class TRUCK_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "TRUCK_HEADER";
	
		// Vehicle Number
    public static final String FIELD_VECH_NO = "VECH_NO";

	public TRUCK_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getVECH_NO() { 
		return (String)getField(FIELD_VECH_NO); 
	}

	public void setVECH_NO(String value) {
		setField(FIELD_VECH_NO, value);
	}
}