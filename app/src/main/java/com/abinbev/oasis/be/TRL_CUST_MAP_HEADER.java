//	Generated using Unvired Modeller - Build R-4.000.0130

package com.abinbev.oasis.be;

import com.unvired.database.DBException;
import com.unvired.model.DataStructure;

/*
This class is part of the BE "TRL_CUST_MAP".
*/	
public class TRL_CUST_MAP_HEADER extends DataStructure {
	
	public static final String TABLE_NAME = "TRL_CUST_MAP_HEADER";
	
		// DSD Connector: Customer Number
    public static final String FIELD_CUST_NO = "CUST_NO";

	// Vehicle Number
    public static final String FIELD_TRAILER_ID = "TRAILER_ID";

	public TRL_CUST_MAP_HEADER() throws DBException {
		super(TABLE_NAME, true);
	}

		public String getCUST_NO() { 
		return (String)getField(FIELD_CUST_NO); 
	}

	public void setCUST_NO(String value) {
		setField(FIELD_CUST_NO, value);
	}

	public String getTRAILER_ID() { 
		return (String)getField(FIELD_TRAILER_ID); 
	}

	public void setTRAILER_ID(String value) {
		setField(FIELD_TRAILER_ID, value);
	}
}