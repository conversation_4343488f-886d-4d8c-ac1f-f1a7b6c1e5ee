package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;

import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.OasisCache;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class MaterialController {
    private static MaterialController materialController = null;
    //private IDataManager iDataManager = null;
    MaterialController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static MaterialController getInstance() {
        if (materialController == null) {
            materialController = new MaterialController();
        }

        return materialController;
    }

    public List<MATERIAL_HEADER> getMaterials(DeliveryController.Quantity qtyType){
        List<MATERIAL_HEADER> materialHeaderList=new ArrayList<>();
        String query ="";
        switch (qtyType)
        {
            case RCR:
                query = "(" + MATERIAL_HEADER.FIELD_TYPE + " = '" + Constants.LEER + "' OR " + MATERIAL_HEADER.FIELD_TYPE + " = '" + Constants.VERP + "') AND " + MATERIAL_HEADER.FIELD_ITM_CAT + " <> '" + Constants.ITEM_CATEGORY_PALLET + "' ORDER BY " + MATERIAL_HEADER.FIELD_MAT_NO;
                break;
            case PALLETPICK:
                //query = "WHERE (" + MATERIAL_HEADER.TYPEColumn + " = '" + OASISConstants.LEER + "' OR " + MATERIAL_HEADER.TYPEColumn + " = '" + OASISConstants.VERP + "') AND " + MATERIAL_HEADER.ITM_CATColumn + " = '" + OASISConstants.ITEM_CATEGORY_PALLET + "'";
                //break;
            case PALLETDROP:
                query = "(" + MATERIAL_HEADER.FIELD_TYPE + " = '" + Constants.LEER + "' OR " + MATERIAL_HEADER.FIELD_TYPE + " = '" + Constants.VERP + "') AND " + MATERIAL_HEADER.FIELD_ITM_CAT + " = '" + Constants.ITEM_CATEGORY_PALLET + "' ORDER BY " + MATERIAL_HEADER.FIELD_MAT_NO;
                break;
        }

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(MATERIAL_HEADER.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    materialHeaderList.add((MATERIAL_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if(materialHeaderList.size()>0){
            Collections.sort(materialHeaderList, new Comparator<MATERIAL_HEADER>() {
                @Override
                public int compare(MATERIAL_HEADER lhs, MATERIAL_HEADER rhs) {
                    return lhs.getMAT_NO().compareTo(rhs.getMAT_NO());
                }
            });
            return materialHeaderList;
        }else {
            return  null;
        }
    }

    public String[] getDummyMaterialNos() {
        List<String> matNos = new ArrayList<>();
        if (OasisCache.DummyMaterials == null) {
            String query = "SELECT MAT_NO FROM MATERIAL_HEADER WHERE ITM_CAT='" + Constants.MATERIAL_DUMMY_TYPE + "'";
            try {
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
                if (cursor != null && cursor.getCount() > 0) {
                    while (cursor.moveToNext()) {
                        String mat_no = cursor.getString(0);
                        matNos.add(mat_no);
                        if (cursor.isLast()) {
                            cursor.close();
                        }
                    }
                    String[] mStringArray = new String[matNos.size()];
                    mStringArray = matNos.toArray(mStringArray);
                    OasisCache.DummyMaterials = mStringArray;
                } else {
                    cursor.close();
                    return OasisCache.DummyMaterials;
                }
            } catch (DBException e) {
                Logger.e("", e);
                return OasisCache.DummyMaterials;
            }

        }
        return OasisCache.DummyMaterials;
    }


    public String[] getWoodenPalletMaterials(){
        List<String> matNos = new ArrayList<>();

        if(OasisCache.PalletMaterials == null){
            String query = "SELECT MAT_NO FROM MATERIAL_HEADER WHERE ITM_CAT='" + Constants.ITEM_CATEGORY_PALLET + "'";
            try {
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
                if (cursor != null && cursor.getCount() > 0) {
                    while (cursor.moveToNext()) {
                        String mat_no = cursor.getString(0);
                        matNos.add(mat_no);
                        if (cursor.isLast()) {
                            cursor.close();
                        }
                    }
                    String[] mStringArray = new String[matNos.size()];
                    mStringArray = matNos.toArray(mStringArray);
                    OasisCache.PalletMaterials = mStringArray;
                } else {
                    cursor.close();
                    return OasisCache.PalletMaterials;
                }
            } catch (DBException e) {
                Logger.e("", e);
                return OasisCache.PalletMaterials;
            }
        }
        return OasisCache.PalletMaterials;
    }

    public List<MATERIAL_HEADER> getAllFullMaterials() {
        List<MATERIAL_HEADER> materialHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(MATERIAL_HEADER.TABLE_NAME,MATERIAL_HEADER.FIELD_TYPE+" = '"+Constants.FERT+"' AND "+MATERIAL_HEADER.FIELD_ITM_CAT+" <> '"+Constants.MATERIAL_DUMMY_TYPE+"'");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    materialHeaderList.add((MATERIAL_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(materialHeaderList.size()>0){
            Collections.sort(materialHeaderList, new Comparator<MATERIAL_HEADER>() {
                @Override
                public int compare(MATERIAL_HEADER lhs, MATERIAL_HEADER rhs) {
                    return lhs.getMAT_NO().compareTo(rhs.getMAT_NO());
                }
            });
            return materialHeaderList;
        }else {
            return null;
        }

    }

    public List<MATERIAL_HEADER> getEmptyMaterials() {
        List<MATERIAL_HEADER> materialHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(MATERIAL_HEADER.TABLE_NAME,MATERIAL_HEADER.FIELD_TYPE+" = '"+Constants.LEER+"'");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    materialHeaderList.add((MATERIAL_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(materialHeaderList.size()>0){
            Collections.sort(materialHeaderList, new Comparator<MATERIAL_HEADER>() {
                @Override
                public int compare(MATERIAL_HEADER lhs, MATERIAL_HEADER rhs) {
                    return lhs.getMAT_NO().compareTo(rhs.getMAT_NO());
                }
            });
            return materialHeaderList;
        }else {
            return null;
        }
    }

    public List<MATERIAL_HEADER> getAllMaterialHeaders(){
        List<MATERIAL_HEADER> materialHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(MATERIAL_HEADER.TABLE_NAME);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    materialHeaderList.add((MATERIAL_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if(materialHeaderList.size()>0){
            return materialHeaderList;
        }else {
            return null;
        }
    }
}
