package com.abinbev.oasis.util;

import com.abinbev.oasis.be.PRICE_HEADER;
import com.abinbev.oasis.util.Controllers.DBHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Created by kunchok on 28/01/2021
 */
public class PricingUtil {
    public static BigDecimal DecimalRound(BigDecimal number, int DecimalPlaces)
    {
        //need to confirm this rounding
        BigDecimal roundedNumber;
        if (number.intValue() >= 0)
        {
            roundedNumber = BigDecimal.valueOf((number.doubleValue() * (Math.pow(10, (DecimalPlaces + 1))) + 5) / 10);
            roundedNumber = BigDecimal.valueOf(Math.floor(roundedNumber.doubleValue()));
        }
        else
        {
            roundedNumber = BigDecimal.valueOf((number.doubleValue() * (Math.pow(10, (DecimalPlaces + 1))) - 5) / 10);
            roundedNumber =roundedNumber.setScale(2, RoundingMode.DOWN);
        }
        roundedNumber = BigDecimal.valueOf(roundedNumber.doubleValue() * (Math.pow(10, -DecimalPlaces)));

        return roundedNumber;
    }

    public static Double calcConditionValue(Double LastPrice, String UOM, Double Rate, boolean isPerc, Double CPP)
    {
        Double local = 0d;

        if (UOM.equals("PAL"))
        {
            if (isPerc)
            {
                local = (((Rate / 100) * LastPrice / CPP));
            }
            else
            {
                local = (Rate / CPP);
            }
        }
        else
        {
            if (isPerc)
            {
                local =  ((Rate / 100) * LastPrice);
            }
            else
            {
                local = Rate;
            }
        }
        return local;
    }

    public static List<PRICE_HEADER> getPrices() {
        List<PRICE_HEADER> priceHeaderList = DBHelper.getInstance().getPriceHeaderList();
        return priceHeaderList;
    }
}
