package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.NUM_RANGE;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;

import java.util.List;

/**
 * Created by kunchok on 29/01/2021
 */
public class NumRangeController {
    public static String IncrementAndUpdateNumRange(String docType) throws DBException {
        NUM_RANGE numRangeRow = getNumRange(docType);

        String numRange = "1";
        if (numRangeRow != null)
        {
            if (Utils.isNullOrEmpty(numRangeRow.getLAST_USED_NUM()) )
                numRangeRow.setLAST_USED_NUM("1");

            numRange = String.valueOf((Integer.parseInt(numRangeRow.getLAST_USED_NUM()))+ 1);
            numRangeRow.setLAST_USED_NUM(numRange);
            if (!Utils.isNullOrEmpty(numRangeRow.getPREFIX()))
            {
                /*todo check*/
                //numRange = numRange.PadLeft(10 - (numRangeRow.getPREFIX().length() +  numRange.length() -1), '0');
                //numRange = String.format(String.valueOf(10 - (numRangeRow.getPREFIX().length() +  numRange.length() -1)), '0');
                numRange = PrinterUtils.padLeft(numRange,(10-(numRangeRow.getPREFIX().length()+numRange.length()-1)),'0');
            }
            DBHelper.getInstance().insertOrUpdateNumRange(numRangeRow);
            numRange = numRangeRow.getPREFIX() + numRange;
            return numRange;
        }
        return numRange;
    }

    public static NUM_RANGE getNumRange(String docType) throws DBException {
        NUM_RANGE numRangeDataTable = new NUM_RANGE();
        String query =  NUM_RANGE.FIELD_SHIP_NO + " = '" +DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + NUM_RANGE.FIELD_DOC_TYPE + " = '" + docType + "'";
        List<NUM_RANGE> numRangeResult =  DBHelper.getInstance().getNumRange(query,docType);

        if (numRangeResult.size() > 0 && numRangeResult.get(0) != null)
        {
            return numRangeResult.get(0);
        }
        return null;
    }
}
