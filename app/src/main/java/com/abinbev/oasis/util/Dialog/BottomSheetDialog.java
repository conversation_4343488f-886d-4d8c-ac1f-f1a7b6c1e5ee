package com.abinbev.oasis.util.Dialog;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.activities.RcrFormActivity;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.unvired.oasis.R;
import com.unvired.utils.Context;

public class BottomSheetDialog extends BottomSheetDialogFragment {

    int layout;
    BottomSheetViewListner bottomSheetViewListner;
    public BottomSheetDialog() {

    }

    public BottomSheetDialog( int layout,BottomSheetViewListner bottomSheetViewListner) {
        this.layout = layout;
        this.bottomSheetViewListner=bottomSheetViewListner;
    }



    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void setupDialog(Dialog dialog, int style) {
        View contentView = View.inflate(getContext(), layout, null);
        dialog.setContentView(contentView);
        ((View) contentView.getParent()).setBackgroundColor(getResources().getColor(android.R.color.transparent));
        bottomSheetViewListner.onBottomSheetSetup(contentView);
    }


    public interface BottomSheetViewListner{
        public void onBottomSheetSetup(View view);
    }

}
