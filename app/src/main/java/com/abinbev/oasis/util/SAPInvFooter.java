package com.abinbev.oasis.util;

import java.math.BigDecimal;

/**
 * Created by kunchok on 29/01/2021
 */
public class SAPInvFooter {
    public  String delRetSubTotalLabel = "Del'd Ret'd Sub-total ";
    public  String paymentTermDiscountLabel = "Payment term discount ";
    public  String paymentMethodDiscountLabel = "Payment method discount";
    public  String cashWithOrderDiscountLabel = "CWO on invoice disc";
    public static String ullageLabel = "Ullage Allowance";
    public  String bulkHandlingLabel = "DST Allowance";
    public  String nonScheduleDeliveryLabel="NonSchedule Delivery";
    public  String subTotalLabel = "Sub-total";
    public static String VATAmountLabel = "Vat";
    public  Double VATPerc;
    static int lenVATPerc = 4;
    public static String totalInvoiceValueLabel = "Total Invoice Value";
    public double nonScheduleCharges;
    static int lenNonScheduleDelivery=17;
    public  Double delRetSubTotal = 0.00d;
    public static int lenDelRetSubTotal = 17;
    public  Double paymentTermDiscount = 0.00d;
    static int lenPaymentTermDiscount = 15;
    public  Double paymentMethodDiscount = 0.00d;
    static int lenPaymentMethodDiscount = 16;
    public  Double cashWithOrderDiscount = 0.00d;
    static int lenCashWithOrderDiscount = 17;
    public String  ullage;
    static int lenUllage = 17;
    public  Double bulkDiscount = 0.00d;
    static int lenBulkHandling = 17;
    public  Double subTotal;
    static int lenSubTotal = 17;
    public  Double VATAmount;
    static int lenVATAmount = 17;
    public  double totalInvoiceValue;
    static int lenTotalInvoiceValue = 17;
    public String  driver;
    public static String customerRatingLabel = " YOU HAVE RATED THIS DELIVERY'S SERVICE AS : ";
    public static String customerRatingHyphen = " - ";
    public String  customerRatingValue;
    public static  int lenCustomerRatingValue = 1;
    public String  customerRatingDesc;
    public static  int lenCustomerRatingDesc = 9;
    public  byte[] combinedSignatureImageByte;
    public static  int distanceBetweenSignatures = 65;
    public String  nationalMsg;
    public String  NLLAMsg;
    public String [] SARSMsgArray;
    public String [] DisclaimerMsgArray;
    public String [] FLXMsgArray;
    public  boolean  accumDiscIndicator;
    public String  baselineDiscDate;
    public String  baselineDiscPercentage;
    public String  baselineDiscActualDiscount;
    public String  baselineDiscTotalDiscount;
    public static String baselineDiscMessage1 = "Your agreed payment terms are: ";
    public static String baselineDiscMessage2 = ", which means if you pay by ";
    public static String baselineDiscMessage3 = " you will receive a";
    public static String baselineDiscMessage4 = "% discount of R";
    public static String baselineDiscMessage5 = " (vat inclusive), ";
    public static String baselineDiscMessage6 = "giving a payment amount of R";
    public static  int lenBaselineDiscStatusMaxLength = 4;

    public static  int lenDueDate = -14;
    public static  int lenDiscountPercentage = -6;
    public static  int lenDiscountAmount = 18;
    public static  int lenPayableAmount = 18;
    public static String duedateLabel = "Due date";
    public static String discountPercentageLabel = "Disc %";
    public static String discountAmountLabel = "Discount Amount";
    public static String payableAmountLabel = "Payable Amount";
    public static String amountLabel = "Amount";

    public static String custOrderNoLabel = "Customer Order No: ";
    public String  custOrderNo;
    public static  int lenCustOrderNo = 20;

    public static String GRNNoLabel = "GRN/GRV No:            ";
    public String  GRNNo;
    public static  int lenGRNNo = 12;

    public static String nlaTextForManufacturer = "Registered as a manufacturer and distributor of liquor under NLA Reference No. ";
    public static String nlaTextForDistributor = "Registered as a distributor of liquor under NLA Reference No. ";

    public static String[] getNationalMsgArray(String nationalMsg)
    {
        if (!Utils.isNullOrEmpty(nationalMsg))
        {
            String[] nationalMsgArray = new String[] { };

            int StartIndex = 0;
            int EndIndex = 0;
            int stringLength = 89;
            String strNationalMessage1 = "Messages: " + nationalMsg;

            if (strNationalMessage1.length() <= stringLength)
            {
                nationalMsgArray[0] = strNationalMessage1;
                return nationalMsgArray;
            }
            else
            {
                EndIndex = strNationalMessage1.indexOf(" ", stringLength);

                if ((strNationalMessage1.trim().length() > 0))
                {
                    int intNationalMessage1 = (int)Math.round(strNationalMessage1.length() / stringLength) + 1;

                    nationalMsgArray[0] = strNationalMessage1.substring(StartIndex, EndIndex);
                    StartIndex = EndIndex;

                    for (int b = 1; b <= intNationalMessage1 - 1; b++)
                    {
                        if ((strNationalMessage1.length() < StartIndex + 1))
                        {
                            nationalMsgArray[b] = strNationalMessage1.substring(StartIndex + 1, EndIndex);
                            StartIndex = EndIndex;
                        }
                    }
                }
                return nationalMsgArray;
            }
        }

        return null;
    }
}
