package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.SAPInvFooter;
import com.abinbev.oasis.util.SAPInvHeader;
import com.abinbev.oasis.util.SAPInvItem;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import android.graphics.Bitmap;

import com.abinbev.oasis.ModelClasses.DeliveryItemPricing;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.DRIVER_HEADER;
import com.abinbev.oasis.be.INVOICE;
import com.abinbev.oasis.be.INVOICE_ITEM;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.TreeMap;

import static com.abinbev.oasis.util.Utils.getDateFromString;


public class InvoiceController {


    private static InvoiceController invoiceController = null;
   // private IDataManager iDataManager = null;

    InvoiceController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }
    public static InvoiceController getInstance() {
        if (invoiceController == null) {
            invoiceController = new InvoiceController();
        }

        return invoiceController;
    }

    public List<String> getCompletedInvoicing(List<String> delvList)
    {
        List<String> invList = new ArrayList<String>();
        String deliveryINStr = "";
        for (String delv : delvList)
        {
            if (deliveryINStr != null && !deliveryINStr.isEmpty())
                deliveryINStr = deliveryINStr + ", '" + delv + "'";
            else
                deliveryINStr = "'" + delv + "'";
        }
        String queryStr = "SELECT DISTINCT TIME.ID, DELIVERY.DELV_NO FROM TIME INNER JOIN DELIVERY ON TIME.ID = DELIVERY.INV_NO WHERE (DELIVERY.DELV_NO IN (" + deliveryINStr + "))";
         try {
            Cursor delList = ApplicationManager.getInstance().getDataManager().executeQuery(queryStr);
            if(delList != null && delList.getCount() > 0) {
                while (delList.moveToNext())
                {
                    if (!delList.isNull(1))
                    {
                        invList.add(delList.getString(1));
                    }
                }
                delList.close();
            }else {
                delList.close();
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return invList;
    }

    public void deleteInvoiceItemsIfExistForZeroReturns(List<DELIVERY> deliveryList) {
        if (deliveryList == null || deliveryList.size() <= 0)
            return;
        String deliveries = "";
        for (DELIVERY delivery : deliveryList){
            if(deliveries.equals("")){
                deliveries = "'" + delivery.getDELV_NO()+"'";
            }else {
                deliveries = deliveries + ",'" + delivery.getDELV_NO() + "'";
            }
        }

        String shipNo = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String deleteQuery = "DELETE FROM INVOICE_ITEM WHERE SHIP_NO = '" + shipNo + "' AND DELV_ITM_NO NOT IN(SELECT ITM_NO FROM DELIVERY_ITEM WHERE SHIP_NO = '" + shipNo + "' AND DELV_NO IN (" + deliveries + ")) AND DELV_NO IN (" + deliveries + ")";
        String deleteQuery = "SHIP_NO = '" + shipNo + "' AND DELV_ITM_NO NOT IN(SELECT ITM_NO FROM DELIVERY_ITEM WHERE SHIP_NO = '" + shipNo + "' AND DELV_NO IN (" + deliveries + ")) AND DELV_NO IN (" + deliveries + ")";
//        try {
//            Cursor cursor = iDataManager.executeQuery(deleteQuery);
//            cursor.close();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
        try {
            ApplicationManager.getInstance().getDataManager().delete(INVOICE_ITEM.TABLE_NAME,deleteQuery);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }
    public static String getDriverName(String driverId) throws DBException {
        if (!Utils.isNullOrEmpty(driverId))
        {
            String whereClause = " DRV_ID = '" + driverId + "'";
            DRIVER_HEADER driver_header = new DRIVER_HEADER();
            List<DRIVER_HEADER> driverDataTable = DBHelper.getInstance().getDriverList(whereClause);

            if (driverDataTable != null && driverDataTable.size() > 0)
            {
                driver_header =  driverDataTable.get(0);
                return driver_header.getDRV_NAME();
            }
        }
        return "";
    }



    public SAPInvHeader getSAPInvoiceHeader(CUSTOMER_HEADER customerHeader, DELIVERY delivery, DEPOT_HEADER currentDepot) throws DBException {
        SAPInvHeader sapInvHeader = new SAPInvHeader();

        if (customerHeader == null)
            return sapInvHeader;

        sapInvHeader.legalEntAccountNo = customerHeader.getCUST_NO();
        sapInvHeader.custName = Utils.isNullOrEmpty(customerHeader.getNAME()) ? "" : customerHeader.getNAME();
        sapInvHeader.custAddress1 = Utils.isNullOrEmpty(customerHeader.getLIC_NAME()) ? "" : customerHeader.getLIC_NAME() + " (T/A)";
        sapInvHeader.custAddress2 = Utils.isNullOrEmpty(customerHeader.getBILL_TO_NAME()) ? "" : customerHeader.getBILL_TO_NAME();

        String billToAddress = Utils.isNullOrEmpty(customerHeader.getBILL_TO_ADDRESS()) ? "" : customerHeader.getBILL_TO_ADDRESS();
        String[] billToAddressSplit = sapInvHeader.getSplitAddress(billToAddress);
        if (billToAddressSplit != null)
        {
            sapInvHeader.custAddress3 = billToAddressSplit[0];
            sapInvHeader.custAddress4 = billToAddressSplit[1];
            sapInvHeader.custAddress5 = billToAddressSplit[2];
        }

        String deliveryAddress = Utils.isNullOrEmpty(customerHeader.getADDRESS()) ? "" : customerHeader.getADDRESS();
        String[] deliveryAddressSplit = sapInvHeader.getSplitAddress(deliveryAddress);
        if (billToAddressSplit != null)
        {
            sapInvHeader.deliveryAddress1 = deliveryAddressSplit[0];
            sapInvHeader.deliveryAddress2 = deliveryAddressSplit[1];
            sapInvHeader.deliveryAddress3 = deliveryAddressSplit[2];
            sapInvHeader.deliveryAddress4 = deliveryAddressSplit[3];
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.ENGLISH);
        sapInvHeader.invDate = dateFormat.format(new Date());
        sapInvHeader.accountNo = customerHeader.getPAYER_NO();
        sapInvHeader.bankingRef = customerHeader.getBANK_REF_NO();
        sapInvHeader.custOrderNo = Utils.isNullOrEmpty(delivery.getCUST_REF()) ? "" : delivery.getCUST_REF();
        sapInvHeader.SABOrderNo = Utils.isNullOrEmpty(delivery.getORD_NO()) ? "" : delivery.getORD_NO();
        sapInvHeader.deliveryNo = delivery.getDELV_NO();
        sapInvHeader.shipmentNo = delivery.getSHIP_NO();
        String homeDepot = Utils.isNullOrEmpty(customerHeader.getSOLDTO_HM_DEPOT()) ? "" : customerHeader.getSOLDTO_HM_DEPOT();

        if (!Utils.isNullOrEmpty(homeDepot))
        {
            DEPOT_HEADER homeDepotRow = DBHelper.getInstance().getDepot(homeDepot);
            if (homeDepotRow != null)
                sapInvHeader.depot = Utils.isNullOrEmpty(homeDepotRow.getNAME()) ? "" : homeDepotRow.getNAME();
        }

        if (currentDepot != null)
        {
            sapInvHeader.issueDepot = currentDepot.getNAME();
            sapInvHeader.depotAddress = currentDepot.getADDRESS();
            sapInvHeader.faxTel = currentDepot.getFAX();
        }

        sapInvHeader.ordersTel = OasisCache.getAccQueries();
        sapInvHeader.accQueries = OasisCache.getAccQueries();
        if (!Utils.isNullOrEmpty(OasisCache.getAccClerk()))
        {
            int secondIndex = -1;

            for (int i = 0; i < 2; i++)
            {
                secondIndex = OasisCache.getAccClerk().indexOf(" ", secondIndex + 1);
                if (secondIndex == -1)
                    break;

            }

            if (secondIndex == -1)
            {
                sapInvHeader.creditControl1 = OasisCache.getAccClerk();
                sapInvHeader.creditControl2 = "";
            }
            else
            {
                sapInvHeader.creditControl1 = OasisCache.getAccClerk().substring(0, secondIndex);
                sapInvHeader.creditControl2 = OasisCache.getAccClerk().substring(secondIndex + 1);
            }
        }

        sapInvHeader.payTerm = Utils.isNullOrEmpty(delivery.getPAY_TERM()) ? "" : delivery.getPAY_TERM();

        //Set payment term messages
        if (!Utils.isNullOrEmpty(delivery.getBASELN_DT())) //UBR will not have baseline date
            SAPInvHeader.setPaymentTermsMessages(sapInvHeader, delivery.getPAY_TERM(), delivery.getBASELN_DT());

        sapInvHeader.payMethod =Utils.isNullOrEmpty( delivery.getPAY_METHOD_DESC()) ? "" : delivery.getPAY_METHOD_DESC();
        sapInvHeader.custVATNo = Utils.isNullOrEmpty(customerHeader.getVAT_NO()) ? "" : customerHeader.getVAT_NO();
        sapInvHeader.liquorLicenceNo = Utils.isNullOrEmpty(customerHeader.getLIC_NO()) ? "" : customerHeader.getLIC_NO();

        return sapInvHeader;
    }

    public static String[] formatMessage(String paymentTermMsg)
    {
        String[] paymentTermMsgArray = new String[5];
        int arrayIndex = 0;


        int currentIndex = -1;
        int splitIndex = 0;

        try
        {
            while (splitIndex != -1 && arrayIndex <= 4)
            {
                splitIndex = paymentTermMsg.indexOf("DT", currentIndex + 1);

                if (splitIndex != -1)
                {

                    if (currentIndex == -1)
                    {
                        currentIndex = 0;
                    }

                    if (splitIndex == currentIndex)
                    {
                        continue;
                    }

                    paymentTermMsgArray[arrayIndex++] = paymentTermMsg.substring(currentIndex, (splitIndex));

                    currentIndex = splitIndex;
                }
            }

            if (currentIndex != -1 && arrayIndex <= 4)
            {
                paymentTermMsgArray[arrayIndex++] = paymentTermMsg.substring(currentIndex);
            }
        }
        catch (Exception e)
        {
            Logger.e("", e);
        }
        return paymentTermMsgArray;
    }
    public static List<SAPInvItem> getSAPInvDeliveredItems(String delv_no) {

        if (DeliveryItemPricingDictionary == null || DeliveryItemPricingDictionary.size() <= 0 || !DeliveryItemPricingDictionary.containsKey(delv_no))
        {
            return null;
        }

        List<DeliveryItemPricing> deliveryItemPricingList = DeliveryItemPricingDictionary.get(delv_no);

        if (deliveryItemPricingList == null || deliveryItemPricingList.size() <= 0)
        {
            return null;
        }
        Random getrandom = new Random();

        List<SAPInvItem> sapInvDeliveredItemList = new ArrayList<>();

        for (DeliveryItemPricing deliveryItemPricing : deliveryItemPricingList)
        {
            if (!Utils.isNullOrEmpty(deliveryItemPricing.getIsReturn_()))
                continue;

            SAPInvItem sapInvDeliveredItem = new SAPInvItem();
            sapInvDeliveredItem.setProdCode(deliveryItemPricing.getMatNo_());
            sapInvDeliveredItem.setProdDesc(deliveryItemPricing.getMatDesc_());
            sapInvDeliveredItem.setUnitPrice(deliveryItemPricing.getUnitPrice_());
            sapInvDeliveredItem.setUnitDeposit(deliveryItemPricing.getUnitDeposit_());
            sapInvDeliveredItem.setItemNo(deliveryItemPricing.getItemNo_());

            Double qty = deliveryItemPricing.getQty_();
            sapInvDeliveredItem.setDelQty(qty);

            if (qty > 0)
            {
                Double beerValue = Utils.round(qty*(deliveryItemPricing.getUnitPrice_()),2);
                Double depositValue = qty*(deliveryItemPricing.getUnitDeposit_());
                Double totalValue = beerValue+(depositValue);
                sapInvDeliveredItem.delQty = qty;
                sapInvDeliveredItem.beerValue = beerValue;
                sapInvDeliveredItem.depositValue = depositValue;
                sapInvDeliveredItem.totalValue = totalValue;
                sapInvDeliveredItem.totalValueOfNonSAB = getNonSABBeerValues(sapInvDeliveredItem.getProdCode(),beerValue);

                sapInvDeliveredItemList.add(sapInvDeliveredItem);

                if (deliveryItemPricing.isPromoApplicable_())
                {
                    SAPInvItem sapInvZPROItem = new SAPInvItem();
                    sapInvZPROItem.setProdCode(deliveryItemPricing.getPromoNo_());
                    sapInvZPROItem.setProdDesc(deliveryItemPricing.getPromoDesc_());
                    Double ZPROValue = Utils.round(deliveryItemPricing.getZPRO_(),2);
                    Double ZPROQty = Utils.round(deliveryItemPricing.getZPROQty_(),2);
                    ZPROValue = Utils.round(Math.abs(ZPROValue) * -1,2);
                    sapInvZPROItem.setUnitPrice(Utils.round(deliveryItemPricing.getZPRORate_(),2));

                    if (ZPROQty > deliveryItemPricing.getQty_())
                        sapInvZPROItem.setDelQty(Utils.round(deliveryItemPricing.getQty_(),2));
                    else
                        sapInvZPROItem.setDelQty( Utils.round(ZPROQty,2));

                    //Since promotional item doesn't has any item number set a random number to every item;
                    sapInvZPROItem.setItemNo(getrandom.nextInt());

                    sapInvZPROItem.setZPROLine(true);
                    sapInvZPROItem.setBeerValue(Math.abs(ZPROValue) * -1);
                    sapInvZPROItem.setTotalValue(Math.abs(ZPROValue) * -1);
                    sapInvDeliveredItemList.add(sapInvZPROItem);

                    SAPInvItem sapInvZPROSubItem = new SAPInvItem();

                    double subZPROUnitPrice = deliveryItemPricing.getUnitPrice_()+deliveryItemPricing.getZPRORate_();
                    double subZPROTotalValue = totalValue+(ZPROValue);
                    double subZPROBeerValue = beerValue+(ZPROValue);

                    sapInvZPROSubItem.setProdDesc("Total per line after discount");
                    sapInvZPROSubItem.setUnitPrice(Utils.round(subZPROUnitPrice,2));
                    sapInvZPROSubItem.setBeerValue(subZPROBeerValue);
                    sapInvZPROSubItem.setTotalValue(subZPROTotalValue);
                    sapInvZPROSubItem.setZPROSubLine(true);
                    sapInvDeliveredItemList.add(sapInvZPROSubItem);
                }
            }
        }
        return sapInvDeliveredItemList;
    }

    public static float InvoiceSum = 0;
    public static Map<String, List<DeliveryItemPricing>> DeliveryItemPricingDictionary = new TreeMap<>();

    public static Map<String, List<DeliveryItemPricing>> getDeliveryItemPricingDictionary() {
        return DeliveryItemPricingDictionary;
    }

    public static void setDeliveryItemPricingDictionary(Map<String, List<DeliveryItemPricing>> deliveryItemPricingDictionary) {
        DeliveryItemPricingDictionary = deliveryItemPricingDictionary;
    }

    public static void generateInvoice(VISIT visitRow) throws Exception {
        List<DELIVERY> deliveryDataTable = DeliveryController.getInstance().getDeliveryHeaders(visitRow.getVISIT_NO(), DeliveryController.Quantity.ALL);

        if (deliveryDataTable == null || deliveryDataTable.size() <= 0)
            return;

        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();

        if (shipRow == null)
            return;

        //iterate through deliveries
        for (DELIVERY delivery : deliveryDataTable)
        {
            List<INVOICE_ITEM> invoiceItemDataTable = null;

            //check invoice present.if not,create and save
            if (Utils.isNullOrEmpty(delivery.getINV_NO()))
            {
                String invNo = NumRangeController.IncrementAndUpdateNumRange(Constants.NUMRANGE_DOC_TYPE_INVOICE);
                createAndSaveInvoice(visitRow, delivery.getDELV_NO(), invNo, shipRow.getLid());
                delivery.setINV_NO(invNo);
            }
            else
            {
                createAndSaveInvoice(visitRow, delivery.getDELV_NO(), delivery.getINV_NO(), shipRow.getLid());
            }

            //get InvoiceItems
            invoiceItemDataTable = getInvoiceItems(delivery.getINV_NO());
            if (invoiceItemDataTable == null)
            {
                invoiceItemDataTable = new ArrayList<>();
            }

            //iterate through deliveryItems
            List<DELIVERY_ITEM> deliveryItemsDataTable = DeliveryController.getInstance().getDeliveryItem(delivery.getDELV_NO(), DeliveryController.Quantity.ALL);
            if (deliveryItemsDataTable != null && deliveryItemsDataTable.size() > 0)
            {
                for (DELIVERY_ITEM deliveryItem : deliveryItemsDataTable)
                {
                    if (Utils.isNullOrEmpty(deliveryItem.getMAT_NO()))
                        continue;

                    //check invoiceItem present.if not,create
                    INVOICE_ITEM invoiceItemRow = getInvoiceItems(deliveryItem, delivery.getINV_NO(), invoiceItemDataTable, shipRow.getLid());
                    invoiceItemDataTable.add(invoiceItemRow);

                    //Create or update DeliveryItemPricing and add it to Hashtable
                    createOrUpdateDeliveryItemPricing(Utils.isNullOrEmpty(delivery.getORD_TYPE()) ? "" : delivery.getORD_TYPE(), deliveryItem);
                }

                reconcileToFixRCRIssue(deliveryItemsDataTable, delivery.getDELV_NO());
            }
            updateInvoiceItems(invoiceItemDataTable);

            if (invoiceItemDataTable.size() == 0)
            {
                throw new Exception("No Materials to generate an invoice.Do you want to clear data for this visit");
            }
        }
        DeliveryController.updateDelivery(deliveryDataTable);
        //Update time
        /*todo*/
        TimeController.createNewTime(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString(), Constants.TIME_TYPE_CUSTOMER_PRE_INVOICE);
    }


    private static List<INVOICE_ITEM> getInvoiceItems(String inv_no) throws DBException {
        List<INVOICE_ITEM> invoiceList = new ArrayList<>();
        String whrClause1 =  INVOICE_ITEM.FIELD_INV_NO +inv_no;
        String whrClause =  INVOICE_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + INVOICE_ITEM.FIELD_INV_NO + " = '" + inv_no + "'";
        invoiceList = DBHelper.getInstance().getInvoiceItemList(whrClause);
        //DBHelper.getInstance().insertOrUpdateInvoice(invoiceList);
        return invoiceList;
    }

    private static void createAndSaveInvoice(VISIT visitRow, String delv_no, String invNo, String shipLid) throws DBException {
        String query = INVOICE.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + INVOICE.FIELD_INV_NO + " = '" + invNo + "'";
        List<INVOICE> invoiceList = new ArrayList<>();
        invoiceList = DBHelper.getInstance().getInvoiceList(query);
        if (invoiceList != null && invoiceList.size() > 0 && invoiceList.get(0) != null)
            return;

        invoiceList = new ArrayList<>();
        INVOICE invoiceRow = new INVOICE();
        invoiceRow.setLid(invoiceRow.getLid());
        invoiceRow.setFid(shipLid);
        invoiceRow.setDELV_NO(delv_no);
        invoiceRow.setCUST_NO(visitRow.getCUST_NO());
        invoiceRow.setSHIP_NO(visitRow.getSHIP_NO());
        invoiceRow.setINV_NO(invNo);
        invoiceRow.setPRN_TIMESTAMP(Utils.get_UTC_DatetimeAsString());
        invoiceRow.setINV_DATE(new SimpleDateFormat("yyyy-MM-dd",Locale.ENGLISH).format(new Date()));

        invoiceList.add(invoiceRow);
        DBHelper.getInstance().insertOrUpdateInvoice(invoiceList);

    }

    private static INVOICE_ITEM getInvoiceItems(DELIVERY_ITEM deliveryItem, String inv_no, List<INVOICE_ITEM> invoiceItemsDataTable, String shipLid) throws DBException {
        INVOICE_ITEM invoiceItemRow = null;
        List<INVOICE_ITEM> invoiceItemList = new ArrayList<>();
        for(INVOICE_ITEM invoiceItem : invoiceItemList) {
            if(invoiceItem.getDELV_NO().equals(deliveryItem.getDELV_NO()) && invoiceItem.getDELV_ITM_NO().equals(deliveryItem.getITM_NO())){
                invoiceItemList.add(invoiceItem);
            }
        }
        if (invoiceItemList == null || invoiceItemList.size() <= 0 || invoiceItemList.get(0) == null)
        {
            invoiceItemRow =new INVOICE_ITEM();
            invoiceItemRow.setINV_NO(inv_no);
            invoiceItemRow.setLid(invoiceItemRow.getLid());
            invoiceItemRow.setDELV_NO(deliveryItem.getDELV_NO());
            invoiceItemRow.setDELV_ITM_NO(deliveryItem.getITM_NO());
            invoiceItemRow.setFid(shipLid);
            invoiceItemRow.setSHIP_NO(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
            invoiceItemsDataTable.add(invoiceItemRow);
        }
        else
        {
            invoiceItemRow = invoiceItemList.get(0);
        }
        return invoiceItemRow;
    }
    private static void createOrUpdateDeliveryItemPricing(String OrderType, DELIVERY_ITEM deliveryItem)
    {
        if (DeliveryItemPricingDictionary == null)
            DeliveryItemPricingDictionary = new HashMap<String, List<DeliveryItemPricing>>();

        if (DeliveryItemPricingDictionary.size() <= 0 || !DeliveryItemPricingDictionary.containsKey(deliveryItem.getDELV_NO()))
            DeliveryItemPricingDictionary.put(deliveryItem.getDELV_NO(), new ArrayList<DeliveryItemPricing>());

        DeliveryItemPricing deliveryItemPricing = null;
        List<DeliveryItemPricing> deliveryItemPricingList = DeliveryItemPricingDictionary.get(deliveryItem.getDELV_NO());
        if (deliveryItemPricingList != null && deliveryItemPricingList.size() > 0)
        {
            String materialno = Utils.isNullOrEmpty(deliveryItem.getMAT_NO()) ? "" : deliveryItem.getMAT_NO();
            String isReturn = Utils.isNullOrEmpty(deliveryItem.getIS_RETURN()) ? "" : deliveryItem.getIS_RETURN();

            List<DeliveryItemPricing> deliveryItemPricingEnumerable = new ArrayList<>();
            for(DeliveryItemPricing deliveryItemPricing1: deliveryItemPricingList) {
                if(deliveryItemPricing1.getMatNo_().equalsIgnoreCase(materialno) &&
                        deliveryItemPricing1.getIsReturn_().equalsIgnoreCase(isReturn)
                        && deliveryItemPricing1.getItemNo_() == deliveryItem.getITM_NO()){
                    deliveryItemPricingEnumerable.add(deliveryItemPricing1);
                }
            }



            if (deliveryItemPricingEnumerable.size() > 0 && deliveryItemPricingEnumerable.get(0) != null)
            {
                deliveryItemPricing = deliveryItemPricingEnumerable.get(0);
            }

            //deliveryItemPricingList.Remove(deliveryItemPricing);
            List<DeliveryItemPricing> tempListToBeRemoved = new ArrayList<>();
            if (deliveryItemPricingEnumerable.size() > 0)
            {
                for (DeliveryItemPricing temp : deliveryItemPricingEnumerable)
                {
                    tempListToBeRemoved.add(temp);
                }

                for (DeliveryItemPricing temp : tempListToBeRemoved)
                {
                    deliveryItemPricingList.remove(temp);
                }
            }
        }

        if (deliveryItemPricing == null)
        {
            deliveryItemPricing = new DeliveryItemPricing();
            deliveryItemPricing.initialize(OrderType, deliveryItem);
        }

        deliveryItemPricing.setActualQty(deliveryItem);
        if (deliveryItemPricingList != null) {
            deliveryItemPricingList.add(deliveryItemPricing);
        }
    }

    private static void reconcileToFixRCRIssue(List<DELIVERY_ITEM> deliveryItemsDataTable, String delvNo) {
        if (DeliveryItemPricingDictionary == null || DeliveryItemPricingDictionary.size() <= 0)
            return;

        if (!DeliveryItemPricingDictionary.containsKey(delvNo))
            return;

        if (deliveryItemsDataTable == null || deliveryItemsDataTable.size() <= 0)
        {
            DeliveryItemPricingDictionary.remove(delvNo);
            return;
        }

        List<DeliveryItemPricing> deliveryItemPricingList = DeliveryItemPricingDictionary.get(delvNo);
        if (deliveryItemPricingList == null || deliveryItemPricingList.size() <= 0)
            return;

        List<DeliveryItemPricing> deliveryItemPricingListNew = new ArrayList<>();
        for (DeliveryItemPricing item : deliveryItemPricingList)
        {
            try
            {
                //filter delivery base on material no
                List<DELIVERY_ITEM> deliveryItemPricingEnumerable = new ArrayList<>();
                for(DELIVERY_ITEM delivery_item : deliveryItemsDataTable){
                    if(item.getMatNo_().equals((Utils.isNullOrEmpty(delivery_item.getMAT_NO())?"":delivery_item.getMAT_NO()))
                            && (item.getIsReturn_()).equals(Utils.isNullOrEmpty(delivery_item.getIS_RETURN())?"":delivery_item.getIS_RETURN())
                    ){
                        deliveryItemPricingEnumerable.add(delivery_item);
                    }
                }


                if (deliveryItemPricingEnumerable.size() <= 0 || deliveryItemPricingEnumerable.get(0) == null)
                {
                    //deliveryItemPricingList.Remove(item);
                }
                else
                {
                    deliveryItemPricingListNew.add(item);
                }
            }
            catch (Exception e)
            {
                Logger.e("", e);
                continue;
            }
        }
        DeliveryItemPricingDictionary.remove(delvNo);
        DeliveryItemPricingDictionary.put(delvNo, deliveryItemPricingListNew);


        //Remove BFR/BIT QTY from EmptyCollectionDelivery
        for(DELIVERY_ITEM delvItemRow : deliveryItemsDataTable)
        {
            if ((delvItemRow.getIS_RETURN()==null || Utils.isNullOrEmpty(delvItemRow.getIS_RETURN())) && (delvItemRow.getPROMO_DESC()!=null && "OSB".equals(delvItemRow.getPROMO_TYPE_DESC())))
            {
                List<DeliveryItemPricing> deliveryItemPricingEnum = new ArrayList<>();
                for(DeliveryItemPricing item : deliveryItemPricingList){
                    if((item.getMatNo_()).equalsIgnoreCase(delvItemRow.getMAT_NO())
                            && (item.getItemNo_()==(delvItemRow.getITM_NO()))
                    ){
                        deliveryItemPricingEnum.add(item);
                    }
                }

                DeliveryItemPricing deliveryItemPricing = null;
                if (deliveryItemPricingEnum.size() > 0)
                {
                    deliveryItemPricing = deliveryItemPricingEnum.get(0);
                }

                if (deliveryItemPricing != null)
                {
                    deliveryItemPricing.setQty_(0d);;
                    deliveryItemPricing.setDeliveredQty_(0d);
                }
            }
        }
    }
    private static void updateInvoiceItems(List<INVOICE_ITEM> invoiceItemDataTable) {
        try
        {
            DBHelper.getInstance().insertOrUpdateInvoiceItem(invoiceItemDataTable);
        }
        catch (Exception dex)
        {
            Logger.e("", dex);
        }
    }

    public static SAPInvFooter getSAPInvFooter(CUSTOMER_HEADER customerHeader, DELIVERY delivery, Double delReturnSubTotal, DEPOT_HEADER currentDepot, Double ZPRORunningTotal, Double delvReturntotalBeerValue, boolean isCheckList, Double delvTotalNonSABBeerValues) throws ParseException {
        SAPInvFooter sapInvFooter = new SAPInvFooter();
        sapInvFooter.delRetSubTotal = delReturnSubTotal;
        if (DeliveryItemPricingDictionary == null || DeliveryItemPricingDictionary.size() <= 0 || !DeliveryItemPricingDictionary.containsKey(delivery.getDELV_NO()))
        {
            return sapInvFooter;
        }

        List<DeliveryItemPricing> deliveryItemPricingList = DeliveryItemPricingDictionary.get(delivery.getDELV_NO());

        if (deliveryItemPricingList == null || deliveryItemPricingList.size() <= 0)
        {
            return sapInvFooter;
        }

        //Changed By Pruthvi
        //IEnumerable<DeiveryItemPricing> deliveryItemPricingDeliveredList = deliveryItemPricingList.Where(u => string.IsNullOrEmpty(u.IS_RETURN));
        List<DeliveryItemPricing> deliveryItemPricingDeliveredList = new ArrayList<>();
        for(DeliveryItemPricing deliveryItemPricing : deliveryItemPricingList){
            if(Utils.isNullOrEmpty(deliveryItemPricing.getIsEmpty_())){
                deliveryItemPricingDeliveredList.add(deliveryItemPricing);
            }
        }
        Double subTotalB4VAT = 0.00d;

        if (deliveryItemPricingDeliveredList != null || deliveryItemPricingDeliveredList.size() > 0)
        {
            deliveryItemPricingList = deliveryItemPricingDeliveredList;

            if (delReturnSubTotal != 0)
            {
                sapInvFooter.paymentTermDiscount = getDiscountTotal(deliveryItemPricingList, Constants.SAPDiscountType.ZPMT)*(-1d);
                Double paymentMethodDiscount = getDiscountTotal(deliveryItemPricingList, Constants.SAPDiscountType.ZPMD);
                sapInvFooter.cashWithOrderDiscount = getDiscountTotal(deliveryItemPricingList, Constants.SAPDiscountType.ZCWO)*((-1)) ;
                Double bulkDiscount = getDiscountTotal(deliveryItemPricingList, Constants.SAPDiscountType.ZDST);

                if (!Utils.isNullOrEmpty(delivery.getORD_TYPE()) && delivery.getORD_TYPE().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                {
                    paymentMethodDiscount = Math.abs(paymentMethodDiscount);
                    bulkDiscount = (Math.abs(bulkDiscount));
                    //Fix for #803 Bug 2
                    sapInvFooter.cashWithOrderDiscount =  (Math.abs(sapInvFooter.cashWithOrderDiscount));
                }

                //m_Ullage = Math.Abs(m_Ullage);
                else
                {
                    paymentMethodDiscount = (Math.abs(paymentMethodDiscount) * -1);
                    bulkDiscount = (Math.abs(bulkDiscount) * -1);
                }

                //m_Ullage = Math.Abs(m_Ullage) * -1;

                sapInvFooter.bulkDiscount = bulkDiscount;
                sapInvFooter.paymentMethodDiscount = paymentMethodDiscount;
            }
            subTotalB4VAT = sapInvFooter.delRetSubTotal+(sapInvFooter.paymentTermDiscount+( sapInvFooter.paymentMethodDiscount)+(sapInvFooter.cashWithOrderDiscount)+( sapInvFooter.bulkDiscount));
        }
        else
        {
            subTotalB4VAT = sapInvFooter.delRetSubTotal;
        }
        double ZFDDRate = DeliveryController.getInstance().getSAPChargesValue(delivery.getDELV_NO());
        if(delivery.getIS_FBR()!=null && !delivery.getIS_FBR().isEmpty() && delivery.getIS_FBR().equals("X")){
            List<REASON_HEADER> reasonHeaderList = ReasonController.getInstance().getReasons(Constants.REASON_TYPE_FULL_FBR_ZFDD);
            if(reasonHeaderList!=null && reasonHeaderList.size()>0){
                for(int i=0;i<reasonHeaderList.size();i++){
                    if(reasonHeaderList.get(i).getRSN_CODE().equals(delivery.getFULL_FBR_RSN())){
                        ZFDDRate = 0;
                    }
                }
            }
        }
        if(ZFDDRate!=0){
            sapInvFooter.nonScheduleCharges=ZFDDRate;
            //Added extra charges
            subTotalB4VAT=subTotalB4VAT+sapInvFooter.nonScheduleCharges;
        }

        sapInvFooter.subTotal = subTotalB4VAT;
        if (Utils.isNullOrEmpty(currentDepot.getVAT_PESC()))
        {
            sapInvFooter.VATPerc =Utils.isNullOrEmpty(currentDepot.getVAT_PESC())?0d:Double.parseDouble(currentDepot.getVAT_PESC());
        }

        if (sapInvFooter.VATPerc == 0)
        {
            //read from depo or MWST
            Double MWSTRate = DeliveryController.getMWSTDeliveryItemConditionRate();
            if (MWSTRate != 0)
                sapInvFooter.VATPerc = MWSTRate;
        }

        if (sapInvFooter.VATPerc == 0)
        {
            sapInvFooter.VATPerc = 14d;
        }
        //Log.e("VAT", String.valueOf((subTotalB4VAT* (sapInvFooter.VATPerc/((100d)) ))));
        double m_VAT = Utils.round((subTotalB4VAT* (sapInvFooter.VATPerc/((100d)) )), 2);
        //Log.e("VAT_ROUND", String.valueOf(Utils.round((subTotalB4VAT* (sapInvFooter.VATPerc/((100d)) )),2)));
        //Log.e("subTotalB4VAT", String.valueOf(subTotalB4VAT));
        //Log.e("TOATAL", String.valueOf(subTotalB4VAT+m_VAT));

        sapInvFooter.VATAmount = m_VAT;
        sapInvFooter.totalInvoiceValue = subTotalB4VAT+(m_VAT) ;

        if (!Utils.isNullOrEmpty(OasisCache.getCustomerRating()))
        {
            List<REASON_HEADER> reasonHeaders = DBHelper.getInstance().getReasons(Constants.REASON_TYPE_DRV_RATING);
            if (reasonHeaders != null && reasonHeaders.size() > 0)
            {
                for (REASON_HEADER item : reasonHeaders)
                {
                    if (item.getRSN_CODE().equals(OasisCache.getCustomerRating()) && item.getRSN_DESC()!=null)
                    {
                        sapInvFooter.customerRatingValue = item.getRSN_CODE();
                        sapInvFooter.customerRatingDesc = item.getRSN_DESC();
                        break;
                    }
                }
            }
        }

        SHIPMENT_HEADER shipmentHeaderRow = DBHelper.getInstance().getShipmentRow();
        if (shipmentHeaderRow != null)
            sapInvFooter.driver = shipmentHeaderRow.getDRV1()==null ? "" : shipmentHeaderRow.getDRV1();

        if (!Utils.isNullOrEmpty(sapInvFooter.driver))
        {
            Bitmap combinedSignatureImage = DBHelper.getInstance().getDriverCustomerMergedBitmap(sapInvFooter.driver, delivery.getDELV_NO().toString(), SAPInvFooter.distanceBetweenSignatures);
            if (combinedSignatureImage != null)
                sapInvFooter.combinedSignatureImageByte = ImageUtil.getByteFromBitmap(combinedSignatureImage);
        }

        sapInvFooter.custOrderNo = delivery.getCUST_REF()==null ? "" : delivery.getCUST_REF();
        if (customerHeader.getIS_NAT_CUST()!=null && !Utils.isNullOrEmpty(customerHeader.getIS_NAT_CUST()) && delivery.getINV_NO()!=null && !Utils.isNullOrEmpty(delivery.getINV_NO()))
        {
            List<INVOICE> invoiceDataTable = new ArrayList<>();
            String query = INVOICE.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + INVOICE.FIELD_INV_NO + " = '" + delivery.getINV_NO() + "'";
            invoiceDataTable = DBHelper.getInstance().getInvoiceList(query);

            if (invoiceDataTable != null && invoiceDataTable.size() > 0 && invoiceDataTable.get(0) != null)
            {
                sapInvFooter.GRNNo = invoiceDataTable.get(0).getGRN_NO()==null ? "" : invoiceDataTable.get(0).getGRN_NO();
            }
        }

        //baseline Discount logic needs to be revisited when ZPRO confirmation comes
        String payTerm = delivery.getPAY_TERM()==null ? "" : delivery.getPAY_TERM();
        String baseLineDate = delivery.getBASELN_DT()==null ? "" : delivery.getBASELN_DT();
        sapInvFooter.accumDiscIndicator = customerHeader == null || customerHeader.getACC_DISC_IND()==null || Utils.isNullOrEmpty(customerHeader.getACC_DISC_IND()) || customerHeader.getACC_DISC_IND().equals("01") ? false : true;
        if (!Utils.isNullOrEmpty(baseLineDate)){
            sapInvFooter.baselineDiscDate = Utils.getStringFromDate(getDateFromString(baseLineDate,"yyyy-MM-dd"),"dd/MM/yyyy");

        }
        String yearInt="0";
        if(baseLineDate!=null){
            String [] dateParts = baseLineDate.split("-");
            yearInt = dateParts[0];
        }


        if (!sapInvFooter.accumDiscIndicator && !Utils.isNullOrEmpty(baseLineDate) && Integer.parseInt(yearInt) > 1990 && sapInvFooter.cashWithOrderDiscount == 0)
        {
            double percDisc = (delivery.getPERC_DISC()==null ? 0.00 : (delivery.getPERC_DISC() / 100));
            sapInvFooter.baselineDiscPercentage = String.valueOf(Utils.round((float) (percDisc*((100))), 2));

            double vatOnTotalDlvRetnBeerValue = (delvReturntotalBeerValue-(ZPRORunningTotal))*( (sapInvFooter.VATPerc/((100))));
            //double vatOnTotalNonSABValue = (delvTotalNonSABBeerValues -(ZPRORunningTotal))*( (sapInvFooter.VATPerc/((100))));
            double vatOnTotalNonSABValue = 0.0;
            if(delvTotalNonSABBeerValues!=null && delvTotalNonSABBeerValues!=0.0){
                vatOnTotalNonSABValue = (delvTotalNonSABBeerValues -(ZPRORunningTotal))*( (sapInvFooter.VATPerc/((100))));
            }
            double totalDelvItemforNonSAB = vatOnTotalNonSABValue  + delvTotalNonSABBeerValues;
            double baselineDiscActualDiscount = (((delvReturntotalBeerValue-(ZPRORunningTotal)-totalDelvItemforNonSAB )+(vatOnTotalDlvRetnBeerValue))*(percDisc));

            sapInvFooter.baselineDiscActualDiscount = String.valueOf(Utils.round((float)baselineDiscActualDiscount, 2));
            sapInvFooter.baselineDiscTotalDiscount = String.valueOf(Utils.round((float)((sapInvFooter.totalInvoiceValue-(baselineDiscActualDiscount))), 2));
        }

        String nationalMsg = delivery.getNAT_INV_MSG1()==null ? "" : delivery.getNAT_INV_MSG1();
        if (Utils.isNullOrEmpty(nationalMsg))
        {
            nationalMsg = delivery.getNAT_INV_MSG2()==null ? "" : delivery.getNAT_INV_MSG2();
        }
        else
        {
            nationalMsg = nationalMsg + (delivery.getNAT_INV_MSG2()==null ? "" : delivery.getNAT_INV_MSG2());
        }
        sapInvFooter.nationalMsg = nationalMsg;

        List<CUSTOMIZATION_HEADER> invoiceFooterKeysDataTable = DBHelper.getInstance().getInvoiceKeys("INVF_");

        if (invoiceFooterKeysDataTable != null && invoiceFooterKeysDataTable.size() > 0)
        {
            List<CUSTOMIZATION_HEADER> disclaimerRows = new ArrayList<>();

            for(CUSTOMIZATION_HEADER header: invoiceFooterKeysDataTable){
                if(header.getKEY_NAME().contains("INVF_DISCLMR")){
                    disclaimerRows.add(header);
                }
            }

            if (disclaimerRows != null && disclaimerRows.size() > 0)
            {
                sapInvFooter.DisclaimerMsgArray = new String[disclaimerRows.size()];
                for (int i = 0; i < disclaimerRows.size(); i++)
                {
                    CUSTOMIZATION_HEADER disclaimerRow = disclaimerRows.get(i);
                    sapInvFooter.DisclaimerMsgArray[i] = disclaimerRow.getKEY_DESC()==null ? "" : disclaimerRow.getKEY_DESC();
                }
            }
            List<CUSTOMIZATION_HEADER> SARSRows = new ArrayList<>();

            for(CUSTOMIZATION_HEADER header: invoiceFooterKeysDataTable){
                if(header.getKEY_NAME().contains("INVF_SARS")){
                    SARSRows.add(header);
                }
            }
            if (SARSRows != null && SARSRows.size() > 0)
            {
                sapInvFooter.SARSMsgArray = new String[SARSRows.size()];
                for (int i = 0; i < SARSRows.size(); i++)
                {
                    CUSTOMIZATION_HEADER SARSRow = SARSRows.get(i);
                    sapInvFooter.SARSMsgArray[i] = SARSRow.getKEY_DESC()==null ? "" : SARSRow.getKEY_DESC();
                }
            }


            //Payment method discount part
            if(baseLineDate.isEmpty()){
                baseLineDate = Utils.getStringFromDate(new Date(),"YYYY-MM-dd");
            }
            String [] dateParts = baseLineDate.split("-");
            String year = dateParts[0];
            if (!Utils.isNullOrEmpty(payTerm) && !Utils.isNullOrEmpty(baseLineDate) && Integer.parseInt(year) > 1990)
            {
                List<CUSTOMIZATION_HEADER> PaymentTermCustomizationRows = new ArrayList<>();


                String PAYMENT_TERM_PREFIX_LBL = "NINVF_";
                List<CUSTOMIZATION_HEADER> customizationTable = null;

                if (isCheckList)
                {
                    PAYMENT_TERM_PREFIX_LBL = "NICLF_";
                    customizationTable = DBHelper.getInstance().getInvoiceKeys(PAYMENT_TERM_PREFIX_LBL);

                    if (customizationTable != null && customizationTable.size() > 0)
                    {
                        PaymentTermCustomizationRows.addAll(customizationTable);
                    }
                }

                customizationTable = DBHelper.getInstance().getInvoiceKeys("NINVF_");

                if (customizationTable != null && customizationTable.size() > 0)
                {
                    PaymentTermCustomizationRows.addAll(customizationTable);
                }

                String PAYMENT_TERM_MSG_LBL = "MSG";
                String PAYMENT_TERM_DAY_LBL = "DAY";
                String PAYMENT_TERM_PERC_LBL = "PERC";

                List<CUSTOMIZATION_HEADER> PaymentTermRows = new ArrayList<>();

                for(CUSTOMIZATION_HEADER header: PaymentTermCustomizationRows){
                    if(header.getKEY_NAME().contains(PAYMENT_TERM_PREFIX_LBL + payTerm + "_" + PAYMENT_TERM_MSG_LBL)){
                        PaymentTermRows.add(header);
                    }
                }

                if (PaymentTermRows != null && PaymentTermRows.size() > 0)
                {
                    sapInvFooter.FLXMsgArray = new String[PaymentTermRows.size()];
                    List<CUSTOMIZATION_HEADER> PaymentTermMsgDays = new ArrayList<>();

                    for(CUSTOMIZATION_HEADER header: PaymentTermCustomizationRows){
                        if(header.getKEY_NAME().contains("NINVF_" + payTerm + "_" + PAYMENT_TERM_DAY_LBL)){
                            PaymentTermMsgDays.add(header);
                        }
                    }

                    List<CUSTOMIZATION_HEADER> PaymentTermDiscPercentages = new ArrayList<>();

                    for(CUSTOMIZATION_HEADER header: PaymentTermCustomizationRows){
                        if(header.getKEY_NAME().contains("NINVF_" + payTerm + "_" + PAYMENT_TERM_PERC_LBL)){
                            PaymentTermDiscPercentages.add(header);
                        }
                    }


                    int PaymentTermMesgsCount = PaymentTermRows.size();
                    int DaysLabelCount = PaymentTermMsgDays.size();

                    int arrayIndexCounter = 0;
                    int daysArrayIndex = 0;
                    int percentageArrayIndexCounter = 0;

                    boolean isHeaderMessage = true;


                    if (PaymentTermMsgDays != null && DaysLabelCount > 0)
                    {
                        for (int i = 0; (i <= PaymentTermMesgsCount && daysArrayIndex < DaysLabelCount); i++)
                        {
                            CUSTOMIZATION_HEADER PaymentTermRow = PaymentTermRows.get(i);
                            String keyDesc = PaymentTermRow.getKEY_DESC()==null ? "" : PaymentTermRow.getKEY_DESC();


                            if (-1 != keyDesc.indexOf("DT"))
                            {
                                Date baseLineDateTime =Utils.getDateFromString(baseLineDate,"yyyy-MM-dd");

                                try
                                {
                                    Double discountPercent = 0d;
                                    if (-1 != keyDesc.indexOf("DP") && percentageArrayIndexCounter < PaymentTermDiscPercentages.size())
                                    {
                                        try
                                        {
                                            String percentage = PaymentTermDiscPercentages.get(percentageArrayIndexCounter).getKEY_DESC()==null ? "" : PaymentTermDiscPercentages.get(percentageArrayIndexCounter).getKEY_DESC();
                                            percentageArrayIndexCounter++;

                                            discountPercent = Utils.round(Float.parseFloat(percentage), 2);
                                            String formattedDescPercent = String.valueOf(new BigDecimal(String.valueOf(discountPercent)).setScale(2, RoundingMode.FLOOR));

                                            //Do not replace the "DT" with right padding if it is a first message(first message is not part of table format messages)
                                            //For checkList also no need to give right padding
                                            if (isCheckList || isHeaderMessage)
                                            {
                                                keyDesc = keyDesc.replace("DP", discountPercent + "");
                                            }
                                            else
                                            {
                                                keyDesc = keyDesc.replace("DP", formattedDescPercent);
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            Logger.e("", e);
                                        }
                                    }

                                    CUSTOMIZATION_HEADER PaymentTermMSgDaysRow = PaymentTermMsgDays.get(daysArrayIndex++);
                                    int NoOfDaysToBeAdded = PaymentTermMSgDaysRow.getKEY_DESC()==null ? 0 : Integer.parseInt(PaymentTermMSgDaysRow.getKEY_DESC());

                                    //keyDesc = keyDesc.Replace("DT",string.Format(new TruncateFormatProvider(),"{0:"+InvoicePrintingStructures.SAPInvFooter.lenDueDate+"}",baseLineDateTime.AddDays(NoOfDaysToBeAdded).ToString("dd/MM/yyyy")));
                                    Calendar c = Calendar.getInstance();
                                    c.setTime(baseLineDateTime);
                                    c.add(Calendar.DATE, NoOfDaysToBeAdded);
                                    baseLineDateTime = c.getTime();
                                    String replacedDateString =Utils.getStringFromDate(baseLineDateTime,"dd/MM/yyyy");
                                    String formattedDate = replacedDateString;

                                    //beerValue - DST + VAT

                                    //changes made according to changes made in windows code
                                    //double vatOnTotalDlvRetnBeerValue = (delvReturntotalBeerValue-(sapInvFooter.bulkDiscount*((-1))))*((sapInvFooter.VATPerc/(100)));
                                    double vatOnTotalDlvRetnBeerValue = (delvReturntotalBeerValue)*((sapInvFooter.VATPerc/(100)));
                                    double vatOnTotalNonSABRetnValue = 0.0;
                                    if(delvTotalNonSABBeerValues!=null && delvTotalNonSABBeerValues!=0.0){
                                        vatOnTotalNonSABRetnValue = (delvTotalNonSABBeerValues -(ZPRORunningTotal))*( (sapInvFooter.VATPerc/((100))));
                                    }
                                    double totalDelvItemforStarMat = vatOnTotalNonSABRetnValue   + delvTotalNonSABBeerValues;
                                    //TODO: Add running total for zpro to beervalsubtot in the line below.
                                    //changes made according to changes made in windows code
                                    //double baselineDiscActualDiscount = (((delvReturntotalBeerValue-((sapInvFooter.bulkDiscount)*((-1))))+(vatOnTotalDlvRetnBeerValue))*(discountPercent/((100))));
                                    double baselineDiscActualDiscount = (((delvReturntotalBeerValue)+(vatOnTotalDlvRetnBeerValue)-totalDelvItemforStarMat)*(discountPercent/((100))));
                                    //String baselineDiscActualDiscountStr = PricingUtil.DecimalRound(baselineDiscActualDiscount, 2).toString();
                                    //String baselineDiscTotalDiscountStr = PricingUtil.DecimalRound(((sapInvFooter.totalInvoiceValue.subtract(baselineDiscActualDiscount)).setScale(2,BigDecimal.ROUND_FLOOR)), 2).toString();
                                    String baselineDiscActualDiscountStr = String.valueOf(Utils.round((float)(baselineDiscActualDiscount),2));
                                    String baselineDiscTotalDiscountStr = String.valueOf(Utils.round((float)(sapInvFooter.totalInvoiceValue-(baselineDiscActualDiscount)),2));
                                    //Log.e("VALUE",baselineDiscTotalDiscountStr);
                                     //Feature #1203 - no payment term disc if CWO discount (sapInvFooter.cashWithOrderDiscount) is available.
                                    if (sapInvFooter.cashWithOrderDiscount != 0)
                                    {
                                        baselineDiscActualDiscountStr = "0.00";
                                        baselineDiscTotalDiscountStr = String.valueOf(Utils.round((float)((sapInvFooter.totalInvoiceValue)), 2));
                                    }

                                    //decimal totalDiscount = PricingUtil.DecimalRound(((float)(sapInvFooter.totalInvoiceValue * discountPercent / 100)), 2);
                                    //decimal totalPayable = PricingUtil.DecimalRound(((float)(sapInvFooter.totalInvoiceValue - totalDiscount)), 2);

                                    String formattedDiscountValue =  "R" + baselineDiscActualDiscountStr;
                                    String formattedTotalValue = "R" + baselineDiscTotalDiscountStr;

                                    if (isCheckList || isHeaderMessage)
                                    {
                                        keyDesc = keyDesc.replace("DV", baselineDiscActualDiscountStr + "");
                                        keyDesc = keyDesc.replace("TV", baselineDiscTotalDiscountStr + "");
                                        keyDesc = keyDesc.replace("DT", replacedDateString);
                                    }
                                    else
                                    {
                                        keyDesc = keyDesc.replace("DV", formattedDiscountValue);
                                        keyDesc = keyDesc.replace("TV", formattedTotalValue);
                                        keyDesc = keyDesc.replace("DT", formattedDate);
                                    }
                                }
                                catch (Exception e)
                                {
                                    //Logger.log(Logger.Severity.ERROR, Logger.LogCategory.APPLICATION, "Exception caught while creating ", e);
                                    continue;
                                }
                            }
                            sapInvFooter.FLXMsgArray[arrayIndexCounter++] = keyDesc;
                            isHeaderMessage = false;
                        }
                    }
                }
            }

            //Commented below for NLLA not dynamic as per depot and introduced new logic below
            //IEnumerable<oasisDataSet.CUSTOMIZATION_HEADERRow> NLLARows = invoiceFooterKeysDataTable.Where(u => u.KEY_NAME.Equals("INVF_NLLA_REF_TEXT"));
            //if (NLLARows != null && NLLARows.Count() > 0 && NLLARows.First() != null)
            //{
            //    sapInvFooter.NLLAMsg = NLLARows.First().IsKEY_DESCNull() ? "" : NLLARows.First().KEY_DESC;
            //}

            String nllaText = "Registered as a manufacturer and distributor of liquor under NLA Reference No. RG 0000891";
            DEPOT_HEADER depot = DepotController.getInstance().getSelectedDepotRow();
            if (depot.getNAME()!=null && !depot.getNLLA().equals(""))
            {
                String NLLAContent = depot.getNLLA();
                String[] tokens = NLLAContent.split("\\|");
                if (tokens != null && tokens.length == 2)
                {
                    String RGNo = tokens[0];
                    String indicator = tokens[1];
                    if ("M".equalsIgnoreCase(indicator))
                        nllaText = SAPInvFooter.nlaTextForManufacturer + RGNo;
                    else if ("D".equalsIgnoreCase(indicator))
                        nllaText = SAPInvFooter.nlaTextForDistributor + RGNo;
                }
            }
            sapInvFooter.NLLAMsg = nllaText;
        }
        return sapInvFooter;
    }
    private static Double getDiscountTotal(List<DeliveryItemPricing> deliveryItemPricingList, Constants.SAPDiscountType discountType)
    {
        Double local=0.0;
        Double Discount ;

        if (deliveryItemPricingList == null || deliveryItemPricingList.size() <= 0)
        {
            return local;
        }

        switch (discountType)
        {
            case ALL:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZDST_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount = Discount*-1;
                    local =local+(Discount) ;
                    Discount = deliveryItem.getZPMA_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*(-1);;
                    local =local+(Discount);
                    Discount = deliveryItem.getZPMD_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                    Discount = deliveryItem.getZCWO_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                    Discount = deliveryItem.getZPMM_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                    Discount = deliveryItem.getZPMT_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                    Discount = deliveryItem.getZPRO_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                    Discount = deliveryItem.getZULL_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZDST:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZDST_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount = Discount*-1;
                    local =local+(Discount);
                }

                break;
            case ZPMA:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZPMA_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZPMD:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZPMD_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZCWO:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZCWO_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZPMM:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZPMM_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZPMT:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZPMT_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount = Discount*((-1));
                    local =local+(Discount);
                }

                break;
            case ZPRO:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZPRO_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
            case ZULL:
                for (DeliveryItemPricing deliveryItem : deliveryItemPricingList)
                {
                    Discount = deliveryItem.getZULL_();
                    if (!deliveryItem.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()))
                        Discount =Discount*((-1));;
                    local =local+(Discount);
                }

                break;
        }
        return local;
    }


    public void saveGRVNumber(String cust_no, String currentDeliveryNo, String grvNumber) {
            if(cust_no!=null && !cust_no.isEmpty() ){

                try {
                    IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(INVOICE.TABLE_NAME, INVOICE.FIELD_CUST_NO+" = '"+cust_no+"' AND "+INVOICE.FIELD_DELV_NO+"= '"+currentDeliveryNo+"'");
                    if (structures != null && structures.length > 0) {
                        for (IDataStructure structure : structures) {
                            INVOICE invoice = (INVOICE) structure;
                            invoice.setGRN_NO(grvNumber);
                            insertOrUpdateInvoice(invoice);
                        }
                    }
                } catch (DBException e) {
                    Logger.e("", e);

                }

            }
    }

    public void insertOrUpdateInvoice(INVOICE invoice) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(invoice);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }


    public void deleteFromAttachment(String s) {
        String query = "DELETE FROM ATTACHMENT WHERE REF_NO IN(" + s + ") AND TYPE =" + Constants.ATTACH_TYPE_INVOICE + " AND DATA_TYPE = '" + Constants.IMAGE_TYPE + "'";
        try {
            ApplicationManager.getInstance().getDataManager().delete(ATTACHMENT.TABLE_NAME, ATTACHMENT.FIELD_REF_NO + " IN ( " + s + " ) AND " + ATTACHMENT.FIELD_TYPE + " = '" +  (long)Constants.ATTACH_TYPE_INVOICE + "' AND " + ATTACHMENT.FIELD_DATA_TYPE + " = '" + Constants.IMAGE_TYPE + "'");
//            Cursor cursor = iDataManager.executeQuery(query);
//            cursor.close();
        } catch (DBException e) {
            Logger.e("", e);
            return;
        }
    }


    public static List<SAPInvItem> getSAPInvReturnedItems(String delv_no,boolean checkList) {
        if (DeliveryItemPricingDictionary == null || DeliveryItemPricingDictionary.size() <= 0 || !DeliveryItemPricingDictionary.containsKey(delv_no))
        {
            return null;
        }

        List<DeliveryItemPricing> deliveryItemPricingList = DeliveryItemPricingDictionary.get(delv_no);

        if (deliveryItemPricingList == null || deliveryItemPricingList.size() <= 0)
        {
            return null;
        }

        Random getrandom = new Random();
        List<SAPInvItem> sapInvReturnedItemList = new ArrayList<>();
        String[] dummyMatArr = MaterialController.getInstance().getDummyMaterialNos();
        for (DeliveryItemPricing deliveryItemPricing : deliveryItemPricingList)
        {
            boolean dummyMatFound = false;
            if(dummyMatArr!=null) {
                for (int i = 0; i < dummyMatArr.length; i++) {
                    if (deliveryItemPricing.getMatNo_().equals(dummyMatArr[i])) {
                        dummyMatFound = true;
                        break;
                    }
                }
            }
            if (dummyMatFound)
                continue;

            SAPInvItem sapInvReturnsItemBit = new SAPInvItem();

            SAPInvItem sapInvReturnsItem = new SAPInvItem();
            sapInvReturnsItem.prodCode = deliveryItemPricing.getMatNo_();
            sapInvReturnsItem.prodDesc = deliveryItemPricing.getMatDesc_();
            sapInvReturnsItem.unitPrice = Math.abs(deliveryItemPricing.getUnitPrice_());

            sapInvReturnsItem.unitDeposit = Math.abs(deliveryItemPricing.getUnitDeposit_());
            double qty = 0;
            double qtybit = 0;

            boolean fbrPresent = false;

            if (!Utils.isNullOrEmpty(deliveryItemPricing.getOrderType_()) && deliveryItemPricing.getOrderType_().equals(Constants.ORDER_TYPE.ZFID.toString()))
            {
                //need to check this..
                continue;
            }

            if (!Utils.isNullOrEmpty(deliveryItemPricing.getOrderType_()) && deliveryItemPricing.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString()) && deliveryItemPricing.getUbrQty_() >= 0)
            {
                sapInvReturnsItem.returnCode = Constants.RETURN_CODES.UBR.toString();

                //Fix for Issue : 403 https://support.unvired.com/issues/403
                //- Pruthvi

                //Fix for Issue : 415
                //deliveryItemPricing.SlsDeal_  will return the vale "X" if UBR_QTY is deliberately set 0 in the UBR form;
                //Otherwise deliveryItemPricing.SlsDeal_ will return "" or NULL
                if (checkList)
                {
                    if (!Utils.isNullOrEmpty(deliveryItemPricing.getSlsDeal_()) || deliveryItemPricing.getUbrQty_() > 0)
                    {
                        qty = deliveryItemPricing.getUbrQty_() * -1;
                    }
                    else
                    {
                        qty = deliveryItemPricing.getQty_() * -1;
                    }
                }
                else
                {
                    qty = deliveryItemPricing.getUbrQty_() * -1;
                }
            }
            else
            {
                if (!Utils.isNullOrEmpty(deliveryItemPricing.getIsReturn_()))
                {
                    if (deliveryItemPricing.getRcrQty_() <= 0)
                    {
                        //pallet pick condition
                        sapInvReturnsItem.returnCode = Constants.RETURN_CODES.PAL.toString();
                        qty = deliveryItemPricing.getQty_() * -1; ;
                    }
                    else
                    {
                        //rcr condition
                        sapInvReturnsItem.returnCode = Constants.RETURN_CODES.EMP.toString();
                        qty = deliveryItemPricing.getRcrQty_() * -1; ;
                    }
                }
                else
                {
                    if (deliveryItemPricing.getFbrQty_() > 0)
                    {
                        sapInvReturnsItem.returnCode = Constants.RETURN_CODES.FBR.toString();
                        qty = deliveryItemPricing.getFbrQty_() * -1; ;
                        fbrPresent = true;
                    }

                    if (deliveryItemPricing.getBitQty_() > 0)
                    {
                        if (fbrPresent)
                        {
                            sapInvReturnsItemBit = new SAPInvItem();
                            sapInvReturnsItemBit.prodCode = deliveryItemPricing.getMatNo_();
                            sapInvReturnsItemBit.prodDesc = deliveryItemPricing.getMatDesc_();
                            sapInvReturnsItemBit.returnCode = Constants.RETURN_CODES.BIT.toString();
                            sapInvReturnsItemBit.unitPrice = deliveryItemPricing.getUnitPrice_();
                            sapInvReturnsItemBit.unitDeposit = deliveryItemPricing.getUnitDeposit_();
                            qtybit = deliveryItemPricing.getBitQty_() * -1;
                        }
                        else
                        {
                            sapInvReturnsItem.returnCode = Constants.RETURN_CODES.BIT.toString();
                            qty = deliveryItemPricing.getBitQty_() * -1;
                        }
                    }
                }
            }

            if (qty != 0)
            {
                double beerValue = Utils.round(qty * Math.abs(deliveryItemPricing.getUnitPrice_()), 2);
                double depositValue = (qty) * Math.abs(deliveryItemPricing.getUnitDeposit_());

                if (sapInvReturnsItem.unitDeposit == 0 && qty != 0)
                    sapInvReturnsItem.unitDeposit = Utils.round((Math.abs(depositValue / qty)), 2);

                double totalValue = beerValue + depositValue;

                sapInvReturnsItem.delQty = qty;
                sapInvReturnsItem.beerValue = beerValue;
                sapInvReturnsItem.depositValue = depositValue;
                sapInvReturnsItem.totalValue = totalValue;
                sapInvReturnsItem.totalValueOfNonSAB = getNonSABBeerValues(sapInvReturnsItem.getProdCode(),beerValue);

                sapInvReturnedItemList.add(sapInvReturnsItem);

                if (deliveryItemPricing.isPromoApplicable_())
                {
                    SAPInvItem sapInvZPROItem = new SAPInvItem();
                    sapInvZPROItem.prodCode = deliveryItemPricing.getPromoNo_();
                    sapInvZPROItem.prodDesc = deliveryItemPricing.getPromoDesc_();
                    //decimal ZPROValue = deliveryItemPricing.ZPRO;
                    //float ZPROQty = deliveryItemPricing.ZPRO_QTY;
                    //ZPROValue = Math.Abs(ZPROValue);
                    sapInvZPROItem.unitPrice = deliveryItemPricing.getZPRORate_();

                    double ZPROQty = qty;
                    double ZPROValue = Utils.round(qty * deliveryItemPricing.getZPRORate_(), 2);
                    ZPROValue = Math.abs(ZPROValue);
                    if (ZPROQty > deliveryItemPricing.getQty_())
                        sapInvZPROItem.delQty = deliveryItemPricing.getQty_();
                    else
                        sapInvZPROItem.delQty = Utils.round(ZPROQty, 2);


                    //Since promotional item doesn't has any item number set a random number to every item;
                    sapInvZPROItem.itemNo = getrandom.nextInt();

                    sapInvZPROItem.isZPROLine = true;
                    sapInvZPROItem.beerValue = Math.abs(ZPROValue);
                    sapInvZPROItem.totalValue = Math.abs(ZPROValue);
                    sapInvReturnedItemList.add(sapInvZPROItem);

                    SAPInvItem sapInvZPROSubItem = new SAPInvItem();

                    double subZPROUnitPrice = deliveryItemPricing.getUnitPrice_() + deliveryItemPricing.getZPRORate_();
                    double subZPROTotalValue = totalValue + ZPROValue;
                    double subZPROBeerValue = beerValue + ZPROValue;

                    sapInvZPROSubItem.prodDesc = "Total per line after discount";
                    sapInvZPROSubItem.unitPrice = Utils.round(subZPROUnitPrice, 2);
                    sapInvZPROSubItem.beerValue = subZPROBeerValue;
                    sapInvZPROSubItem.totalValue = subZPROTotalValue;
                    sapInvZPROSubItem.isZPROSubLine = true;
                    sapInvReturnedItemList.add(sapInvZPROSubItem);
                }
            }

            if (qtybit != 0)
            {
                double beerValue = Utils.round(qtybit * Math.abs(deliveryItemPricing.getUnitPrice_()), 2);
                double depositValue = (qtybit) * Math.abs(deliveryItemPricing.getUnitDeposit_());

                if (sapInvReturnsItemBit.unitDeposit == 0 && qtybit != 0)
                    sapInvReturnsItemBit.unitDeposit = Utils.round((Math.abs(depositValue / qtybit)), 2);

                double totalValue = beerValue + depositValue;

                sapInvReturnsItemBit.delQty = qtybit;
                sapInvReturnsItemBit.beerValue = beerValue;
                sapInvReturnsItemBit.depositValue = depositValue;
                sapInvReturnsItemBit.totalValue = totalValue;
                sapInvReturnedItemList.add(sapInvReturnsItemBit);

                if (deliveryItemPricing.isPromoApplicable_())
                {
                    SAPInvItem sapInvZPROItem = new SAPInvItem();
                    sapInvZPROItem.prodCode = deliveryItemPricing.getPromoNo_();
                    sapInvZPROItem.prodDesc = deliveryItemPricing.getPromoDesc_();
                    double ZPROValue = Utils.round(qtybit * deliveryItemPricing.getZPRORate_(), 2);//deliveryItemPricing.ZPRO;
                    double ZPROQty = qtybit;//deliveryItemPricing.ZPRO_QTY;
                    ZPROValue = Math.abs(ZPROValue);
                    sapInvZPROItem.unitPrice = deliveryItemPricing.getZPRORate_();

                    if (ZPROQty > deliveryItemPricing.getQty_())
                        sapInvZPROItem.delQty = deliveryItemPricing.getQty_();
                    else
                        sapInvZPROItem.delQty = Utils.round(ZPROQty, 2);

                    sapInvZPROItem.isZPROLine = true;
                    sapInvZPROItem.itemNo = getrandom.nextInt();
                    sapInvZPROItem.beerValue = Math.abs(ZPROValue);
                    sapInvZPROItem.totalValue = Math.abs(ZPROValue);
                    sapInvReturnedItemList.add(sapInvZPROItem);

                    SAPInvItem sapInvZPROSubItem = new SAPInvItem();

                    double subZPROUnitPrice  = deliveryItemPricing.getUnitPrice_() + deliveryItemPricing.getZPRORate_();
                    double subZPROTotalValue = totalValue + ZPROValue;
                    double subZPROBeerValue  = beerValue + ZPROValue;

                    sapInvZPROSubItem.prodDesc = "Total per line after discount";
                    sapInvZPROSubItem.unitPrice = Utils.round(subZPROUnitPrice, 2);
                    sapInvZPROSubItem.beerValue = subZPROBeerValue;
                    sapInvZPROSubItem.totalValue = subZPROTotalValue;
                    sapInvZPROSubItem.isZPROSubLine = true;
                    sapInvReturnedItemList.add(sapInvZPROSubItem);
                }
            }
        }
        return sapInvReturnedItemList;
    }

    public void saveCustomerRating(String cust_no, String currentDeliveryNo, String rating) {
        if (cust_no != null && !cust_no.isEmpty()) {
            try {
                IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(INVOICE.TABLE_NAME, INVOICE.FIELD_CUST_NO + " = '" + cust_no + "' AND " + INVOICE.FIELD_DELV_NO + " = '" + currentDeliveryNo + "'");
                if (structures != null && structures.length > 0) {
                    INVOICE invoice = (INVOICE) structures[0];
                    invoice.setCUST_RATING(rating);
                    ApplicationManager.getInstance().getDataManager().update(invoice);
                }
            } catch (DBException e) {
                Logger.e("", e);
            }
        }

    }

    public static double getNonSABBeerValues(String proCode, Double beerValues){
        Double nonSABBeerValues = 0.0;


        CUSTOMIZATION_HEADER customizationheader = null;
        if(DBHelper.getInstance().getCustomizationHeader("INV_1P_TEXT")!=null){
            customizationheader = DBHelper.getInstance().getCustomizationHeader("INV_1P_TEXT");
        }
        MATERIAL_HEADER material_header = DBHelper.getInstance().getMaterialList(proCode);
        if(material_header!=null && customizationheader!=null){
            if(material_header.getPROD_HIER().startsWith(customizationheader.getKEY_VALUE())){
                nonSABBeerValues += beerValues;
            }
        }
        return nonSABBeerValues;
    }
}
