
package com.abinbev.oasis.util;
public class Constants {
    // App Name
    public static final String APP_NAME = "OASIS";
    public static final String APP_COMPANY = "OASIS";
    // BE Names
    public static final String BE_ANSWER_OPTION = "ANSWER_OPTION";
    public static final String BE_AUTH = "AUTH";
    public static final String BE_CUSTOMER = "CUSTOMER";
    public static final String BE_CUSTOMIZATION = "CUSTOMIZATION";
    public static final String BE_DEPOT = "DEPOT";
    public static final String BE_DRIVER = "DRIVER";
    public static final String BE_IMAGE = "IMAGE";
    public static final String BE_INPUT_GET_SHIPMENT = "INPUT_GET_SHIPMENT";
    public static final String BE_INPUT_GET_SHIPMENTS = "INPUT_GET_SHIPMENTS";
    public static final String BE_INPUT_RESET_SHIPMENT = "INPUT_RESET_SHIPMENT";
    public static final String BE_MATERIAL = "MATERIAL";
    public static final String BE_PRICE = "PRICE";
    public static final String BE_QUESTION = "QUESTION";
    public static final String BE_REASON = "REASON";
    public static final String BE_SHIPMENT = "SHIPMENT";
    public static final String BE_TRAILER = "TRAILER";
    public static final String BE_TRL_CUST_MAP = "TRL_CUST_MAP";
    public static final String BE_TRUCK = "TRUCK";
    // PA Function Names
    public static final String PA_GET_DEPOT_LIST = "OASIS_PA_GET_DEPOT_LIST"; // PA function to get Depot List
    public static final String PA_GET_IMAGES = "OASIS_PA_GET_IMAGES"; // PA call to get Empty SKU icons
    public static final String PA_GET_SHIPMENT = "OASIS_PA_GET_SHIPMENT"; // PA function to get Shipment
    public static final String PA_GET_SHIPMENT_LIST = "OASIS_PA_GET_SHIPMENT_LIST"; // PA function to get Shipment List for a Depot
    public static final String PA_RESET_SHIPMENT = "OASIS_PA_RESET_SHIPMENT"; // PA call to reset shipment
    public static final String PA_UPLOAD_SHIPMENT = "OASIS_PA_UPLOAD_SHIPMENT"; // PA call to upload a Shipment
    public static final int MODE_HOME = 0;
    public static final int MODE_GET = 1;
    public static final String RESPONSE_CODE_ERROR = "ERROR";
    public static final String RESPONSE_CODE_SUCCESSFUL = "SUCCESS";
    public static final int SHIP_STATUS_NEW = 0;
    public static final int SHIP_STATUS_PRE_TRIP_COMPL = 1;
    public static final int SHIP_STATUS_PRE_FORKLIFT_COMPL = 2;
    public static final int SHIP_STATUS_INITIAL_ODOMETER_COMPL = 3;
    public static final int SHIP_STATUS_POST_FORKLIFT = 4;
    public static final int SHIP_STATUS_END_TRIP = 5;
    public static final int SHIP_STATUS_FINAL_REPORT_PRINTED = 6;
    public static final int SHIP_STATUS_FINAL_ODOREADING_RECORDED = 7;
    public static final int SHIP_STATUS_START_CHECKIN = 8;
    public static final int SHIP_STATUS_END_CHECKIN = 9;
    public static final int SHIP_STATUS_UPLAOD_START = 10;
    public static final int SHIP_STATUS_UPLAOD_END = 11;
    public static final int SHIP_STATUS_UPLAOD_ERROR = 12;

    //Flag used to check the navigation from which screen
 //   public static final String SCREEN_NAVIGATION = "SCREEN_HELPER";
    public static final String PRE_FORK_LIFT_INSPECTION = "PRE_FORK_LIFT_INSPECTION";
    public static final String POST_FORK_LIFT_INSPECTION = "POST_FORK_LIFT_INSPECTION";
    public static final String INVOICE_GENERATION = "INVOICE_GENERATION";

    //Attachment Doc Types
    public static final int ATTACH_TYPE_CHECKIN = 0;
    public static final int ATTACH_TYPE_PRETRIP_MGR = 1;
    public static final int ATTACH_TYPE_PRETRIP_DRV = 4;
    public static final int ATTACH_TYPE_PRETRIP_SEC = 5;
    public static final int ATTACH_TYPE_INVOICE = 2;
    public static final int ATTACH_TYPE_INVOICE_HH_DELV = 3;
    public static final int ATTACH_TYPE_POSTTRIP_MGR = 6;
    public static final int ATTACH_TYPE_POSTTRIP_DRV = 7;
    public static final int ATTACH_TYPE_POSTTRIP_SEC = 8;
    public static final int ATTACH_TYPE_POD = 9;
    public static final int ATTACH_TYPE_CLAIM = 10;

    public static final String IMAGE_TYPE = "PNG";
    public static final String PDF_TYPE = "PDF";
    public static final String IMAGE_TYPE_JPG = "JPG";

    //TRIP INSP Types
    public static final String TRIP_INSP_TYPE_PRETRIP = "PRETRIP";
    public static final String TRIP_INSP_TYPE_POSTTRIP = "POSTTRIP";
    public static final String TRIP_INSP_TYPE_PRE_FORKLIFT = "PRETRIPFL";
    public static final String TRIP_INSP_TYPE_POST_FORKLIFT = "POSTTRIPFL";

    //Time Types Functional
    public static final String TIME_TYPE_ODOMETER_INITIAL              = "DD";  //SAP - DD (Depot Departure) - Initial Odometer
    public static final String TIME_TYPE_VISIT_START                   = "CA";  //SAP - CA (Customer Arrival)- Odometer reading capture
    public static final String TIME_TYPE_CUSTOMER_RCR_UPDATE           = "CB";  //SAP - CB (Customer Return Update Time) - RCR Capture
    public static final String TIME_TYPE_CUSTOMER_FBR_BIT_UPDATE       = "CD";  //SAP - CD (Customer Delivery Update Time) – Capture FBR/BIT
    public static final String TIME_TYPE_CUSTOMER_PRE_INVOICE          = "CC";  //SAP - CC (Customer Pre-Invoice Time) – Display Invoice on HH
    public static final String TIME_TYPE_CUSTOMER_PRINT_INVOICE        = "CI";  //SAP - CI (Customer Print Invoice Time) – Print Invoice on HH
    public static final String TIME_TYPE_CUSTOMER_PRINT_INPUT_CHECKLIST= "CS";  //SAP - CS (Customer Stock Report Print) – Print Input Checklist
    public static final String TIME_TYPE_DEPOT_STOCK_INITIAL           = "DSI"; //SAP - DS (Depot Stock) – Print Initial Reports
    public static final String TIME_TYPE_DEPOT_STOCK_FINAL             = "DSF"; //SAP - DS (Depot Stock) – Print Final Reports
    public static final String TIME_TYPE_ODOMETER_FINAL                = "DA";  //SAP - DA (Depot Arrival) – Final Odometer

    public static final String TIME_TYPE_VISIT_END                 = "VISIT_END";
    public static final String TIME_TYPE_DELIVERY                  = "DELIVERY";
    public static final String TIME_TYPE_TRAILER                   = "TRAILER";
    public static final String TIME_TYPE_CHECKIN_START             = "CHECK_IN_START";
    public static final String TIME_TYPE_CHECKIN_END               = "CHECK_IN_END";
    public static final String TIME_TYPE_END_TRIP                  = "END_TRIP";

    //Time Types Technical
    public static final String TIME_TYPE_SHIP_DNLD_START       = "SHIP_DNLD_START";
    public static final String TIME_TYPE_SHIP_DNLD_END         = "SHIP_DNLD_END";

    public static final String TIME_TYPE_INV_GEN_START         = "INV_GEN_START";
    public static final String TIME_TYPE_INV_GEN_END           = "INV_GEN_END";

    public static final String TIME_TYPE_INV_PRN_START         = "INV_PRN_START";
    public static final String TIME_TYPE_INV_PRN_END           = "INV_PRN_END";

    public static final String TIME_TYPE_INV_PDFGEN_START      = "INV_PDFGEN_START";
    public static final String TIME_TYPE_INV_PDFGEN_END        = "INV_PDFGEN_END";

    public static final String TIME_TYPE_SHIP_UPLOAD_START     = "SHIP_UPLD_START";
    public static final String TIME_TYPE_SHIP_UPLOAD_END       = "SHIP_UPLD_END";

    public static final String TIME_ENABLE_CONFIG_DEFAULT = "1111111111111"; //By default enable all
    public static final String GPS_ENABLE_CONFIG_DEFAULT = "0000000000000"; //By default disable all
    public static final int GPS_READ_TIMEOUT_DEFAULT = 60000;
    public static final String KEY_TIME_ENABLE_CONFIG = "TIME_CAPTURE";
    public static final String KEY_GPS_ENABLE_CONFIG = "GPS_CAPTURE";
    public static final String KEY_GPS_READ_TIMEOUT = "GPS_READ_TIMEOUT";

    public static final String TIME_FORMAT_HH_MM_SS = "HH:MM:SS";

    public static final String CHECKIN_TYPE                    = "CHECKIN_USER";


    //*************************CONSTANTS**********************************
    //Default POLLING_INTERVAL_TIME
    public static final int POLLING_INTERVAL_DEFAULT = 300000;  // 5 mins
    public static final String FAILURE ="FAILURE" ;
    public static final String SUCCESS = "SUCCESS";
    public static final String NORMAL_TIME_FORMAT_24 ="HH:mm:ss" ;
    public static final String DATE_FORMAT_FROM_SERVER = "yyyy-MM-dd";
    public static final int B_CR_BOTTLES_PER_CASE_VALUE = 12;
    //Printer Constants
    public static boolean PRN_VIA_BT;
    public static boolean BYPASS_PRN;
    public static String PRN_BT_ADDRESS = "";
    public static String PRN_BT_PAIR_CODE_DEFAULT = "1234";
    public static final String KEY_PRN_BT_PAIR_CODE = "PRN_BT_PAIR_CODE";
    public static final String PRN_SERIAL_PORT_DEFAULT = "COM3";
    public static final int PRN_BAUD_RATE_DEFAULT = 9600;
    public static final int PRN_BIT_RATE_DEFAULT = 8;
    //Default Printer Connection props
    public static final String KEY_PRN_SERIAL_PORT = "PRN_SERIAL_PORT";
    public static final String KEY_PRN_SERIAL_PORT_LOCAL = "PRN_SERIAL_PORT_LOCAL";
    public static final String KEY_PRN_BAUD_RATE = "PRN_BAUD_RATE";
    public static final String KEY_PRN_BIT_RATE = "PRN_BIT_RATE";
    public static final String KEY_POLLING_INTERVAL = "POLLING_INTERVAL";


    //Invoice Keys
    public static final String KEY_CUSTOMER_RATING = "CUSTOMER_RATING";
    //public static final String KEY_CUSTOMER_GRVNUMBER = "CUSTOMER_GRVNUMBER";

    //DEpot Keys
    public static final String KEY_ACC_QUERIES = "ACC_QUERIES";
    public static final String KEY_ACC_CLERK = "ACC_CLERK";

    //Delivery Item No Increment
    public static final int DELV_ITMNO_INCREMENT_DEFAULT = 10;
    public static final String KEY_DELV_ITMNO_INCREMENT = "DELV_ITMNO_INCREMENT";

    //RCR LIMIT
    public static final int RCR_LIMIT_DEFAULT = 2310;
    public static final String KEY_RCR_LIMIT = "RCR_LIMIT";

    //PALLET LIMIT
    public static final int PAL_LIMIT_DEFAULT = 420;
    public static final String KEY_PAL_LIMIT = "PAL_LIMIT";

    //Pallet Mat Constants
    public static final String PAL_MAT_DEFAULT = "98001";
    public static final String KEY_DEF_PAL_MAT = "DEF_PAL_MAT";
    public static final String MATERIAL_DUMMY_TYPE = "ZDUM";

    public static final String SCREEN_NAVIGATION = "SCREEN_NAVIGATION_MODE";
    public enum SCREEN_NAVIGATION_MODE
    {
        PretripInspection, PreForkLiftInspection, PostTripInspection, PostForkLiftInspection,InvoiceGeneration
    };

    //Pre Trip Priority Types
    public static final int PRE_TRIP_PRIO_CRITICAL = 0;
    public static final int PRE_TRIP_PRIO_HIGH = 1;
    public static final int PRE_TRIP_PRIO_MEDIUM = 2;
    public static final int PRE_TRIP_PRIO_LOW = 3;
    public static final String ANSWER_TEXT_NO = "NO";
    public static final String IS_AUTH_REQ = "X";

    //NatCustomer Values
    public static final String NOT_NAT_CUSTOMER = "0";
    public static final String NAT_CUSTOMER = "1";
    public static final String NAT_CUSTOMER_NO_GRV = "2";

    //COM Ports
    public static final String COM_PORT_1 = "COM1";
    public static final String COM_PORT_2 = "COM2";
    public static final String COM_PORT_3 = "COM3";
    public static final String COM_PORT_4 = "COM4";
    public static final String COM_PORT_5 = "COM5";
    public static final String COM_PORT_6 = "COM6";
    public static final String COM_PORT_7 = "COM7";
    public static final String COM_PORT_8 = "COM8";
    public static final String COM_PORT_9 = "COM9";
    public static final String COM_PORT_10 = "COM10";
    public static final String COM_PORT_11 = "COM11";
    public static final String COM_PORT_12 = "COM12";

    //Custom width of image
    public static final String IMAGE_WIDTH = "SCAN_IMG_WIDTH";

    public static final int IMAGE_WIDTH_DEFAULT = 352;

    //Visit Statues
    public static final int VISIT_STATUS_START = 0;
    public static final int VISIT_STATUS_END = 1;
    public static final int VISIT_STATUS_CHECKLIST_PRINTED = 2;
    public static final int VISIT_STATUS_END_REACHED = 3;
    //Below visit new status to fix issue #1317 https://support.unvired.com/issues/1317
    //new statuses added to handle all restart app scenarios.
    public static final int VISIT_STATUS_CUSTOMER_SIGN_CAPTURED = 4;
    public static final int VISIT_STATUS_PROFORMA_PRINTED = 5;


    //Reason Types
    public static final String REASON_TYPE_PRETRIP = "PRETRIP";
    public static final String REASON_TYPE_FBR = "FBR";
    public static final String REASON_TYPE_BIR = "BIT";
    public static final String REASON_TYPE_FULL_FBR = "FULL_FBR";
    public static final String REASON_TYPE_INV_DECLINE = "INV_DECLN";
    public static final String REASON_TYPE_DRV_RATING = "DRV_RATING";
    public static final String REASON_TYPE_CHECKIN = "CHECKIN";
    public static final String REASON_TYPE_DELIVERY = "DELIVERY";
    public static final String REASON_TYPE_WAREHOUSE_FULL = "FULFBRWHSE";
    public static final String REASON_TYPE_LOGISTIC_FULL = "FULFBRDIST";
    public static final String REASON_TYPE_FULL_FBR_ZFDD = "FULFBRZFDD";



    //Material Types
    public static final String FERT = "FERT";
    public static final String LEER = "LEER";
    public static final String VERP = "VERP";
    public static final String ITEM_CATEGORY_PALLET = "ZRP1";



    //AUTH Types
    public static final String AUTH_TYPE_MANAGER = "DMG";
    public static final String AUTH_TYPE_SUPERVISOR = "SUP";
    public static final String AUTH_TYPE_CHECKER = "CHK";
    public static final String KEY_DEPOT_PASSWORD = "DEPOT_PASWD";
    public static final String DEPOT_PASSWORD_DEFAULT = "627472";


    //Spinner Mode
    public static final String MODE_ANSWER = "ANSWER";
    public static final String MODE_USERNAME = "USERNAME";


    //Document Types
    public static final String NUMRANGE_DOC_TYPE_DELIVERY = "D";
    public static final String NUMRANGE_DOC_TYPE_INVOICE = "I";

    //Trailer Type
    public static final String TRAILERPICK = "P";
    public static final String TRAILERDROP = "D";

    // Stock Table material type constants
    public static final String FBR_TYPE = "F";
    public static final String BIT_TYPE = "B";
    public static final String RCR_TYPE = "E";





    public static enum ORDER_TYPE
    {
        //excluded for FBR/BIT and RCR
        ZFRD,
        ZFPD,
        ZFTD,
        ZFCD,
        ZFSD,

        //excluded for FBR/BIT and RCR
        ZUBR,

        //considered for FBR/BIT and RCR
        ZSD,
        ZFID
    }
    public enum DocType
    {
        InputChecklist,
        ProForma,
        Invoice
    }

    public enum CopyType
    {
        SUPPLIER,
        CUSTOMER
    }
    public  enum settingType
    {
        SEND_LOG,
        SETUP_DEPOT,
        RESET_SHIPMENT,
        RESET_APP,
        SEND_APP_DB,
        EXIT_APP
    }

    //Delivery Item Conditions
    public static final String ZBAS = "ZBAS";
    public static final String ZPMA = "ZPMA";
    public static final String ZPMT = "ZPMT";
    public static final String ZPMD = "ZPMD";
    public static final String ZCWO = "ZCWO";
    public static final String ZPMM = "ZPMM";
    public static final String ZPRO = "ZPRO";
    public static final String ZULL = "ZULL";
    public static final String ZDST = "ZDST";
    public static final String ZDEP = "ZDEP";
    public static final String ZFDD = "ZFDD";
    public static final String MWST = "MWST";
    public static boolean GlassSplit = false;

    public enum  SAPChargesType{
        ZFDD
    }

    public enum SAPDiscountType
    {
        ALL ,
        ZPMA ,
        ZPMT ,
        ZPMD ,
        ZPMM ,
        ZPRO ,
        ZULL ,
        ZDST ,
        ZCWO ,
    }
    public static final String PRINTER_ADDRESS = "PRINTER_ADDRESS";
    public static final String PRINTER_NAME = "PRINTER_NAME";

    public enum RETURN_CODES
    {
        Normal,
        FBR,
        BIT,
        UBR,
        EMP,
        PAL,
    }

    public static final String DEFAULT_IMAGE = "iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAABSVJREFUWEfNmbFLW1EUxlNwyOCQweENDgYcKnQw4GDARaGDgQ4VOjTQQYJDkQ5FOpTQJYQORTqU4CChg6CDkA4FOxTsaIeC3ewf0MGhQ4YODhnS38n3vHk+k+fTXtM+DuG98+4997vfOfece1/udLvdzH91Aei/ujJp0XS6Z39xdTtpx0kFqN1uN9411p+trz+9oWw83zj8cpgGVApAnS5QsuNZIi3buzJjYdDp0Wn6j+dB2X87lgkmg+Nvx1diugIQ3Gy82MB+fjofBAEMMdeVhyuMxAArj1Z4RJbuL+UmcpW1ih55xQTQMBO1n7k3gyY/lT/4dJCMKQkQMQMaDCGF2UJhzgTTmj8QpUEYCYi8CjWzhZBROp63mblrHel19PUoAdNQQHBjnsqaRWw1thrN900EkgzfXAGs0iCV1QqYNt9shprtZmm5hAZSnbL6sgpnkIf+8PPhsDAfDEieUnAwJyEozhcRxuARu6CUBhFDsKhHGtOAi+6uGfzluCZy4skwDboGAMJTcENfemKXuZYehMLA8hfWnZIboNCeSHJKi6qeBa7iQrGvX1xSpGNqoO/igEJuxrPlx2WmiK3Wfuvg4wGyu7NrUTJbYAx8JKWECTDp5nbTafAd3IiV8pOy05M+MIuGV3S5HOMXAXW6imLQgAN6ooB4hXUtupSAsIMF7DAZYTJAk0H1VZUbSEJiuaAPSGvKHDyVVwJkeCbBYtajhcVkwBjmhfliNEkuLdqyZ95Oyb0Ixnc4iAZ6RabALB7knmBQ+ojmzD6gkx8nDP9P6iy5yi26PqDj78fiwy3m0dxAEmzhH625C4AIQ7wWjdYR3CcBgiHSPBE3SiFXDWUIQEQcURYmIcKyF8jeRYVFZrlJAsTqYEHySzvcx7132Xy7yaLDU7KcKoa0/mk9LIZaH1qkQTzL5MhJCCuFsuXyTULw0Vf7BbXxAAigWLSK0avkYUVbKGpHAMTdvTANDoTlExC26rU6uQ4hwEihcFN/XQcihEEP1Bq+hWIyu34YEhpSrSo5GWugg6g5uA9YAL1dhhgeN9kyDKwYgS8hwmgAi4MR+4ohSrqt1SBgs5YmYeI1WLyM208MUUa0C8MdadCoDTyxyGPt/QCyzDRmRT55BcXGpjGAYiR5AIRd1rMSRnp61BJq/QMiaKyqTORSRk8yaA8MkYK1sY/5i9xDpGvHAgiSAhRqI4unLIOvWZbyzxBGtbWLTp0lTUix6zNAZMU9e7So721giDmdVdg7xxa/B4bMZRyyLgKyusjxNJfjwMUYUCVAZGEeSULaMpNI3TFB8/EAiClSp8hAUZdhlxRgRwuOkezYp/N2Nh3LwBnNaGyYpvP8+gfE2DoVRZOKVbFaneGpXNCgfQuEWdGo1UmhUGXF7lJu9MCQHV96XgOEi1B8ZAG0WgElcG1577eEDCfa8XK5RDzF6PHjMvmesYlQLaioABGfCqjzKTjQ8Hs5kfphyGYGAb1tUGzSDAkTcIOncJ/2a+wJ4Qm2aI/XuOGtt6B2fMCEskt03igBqk9BiA6WVDEQ8IgerGhuBZAigHiy7fZWI3TTzq6KKOnKCY+mqdURgXNbtpu4DLZZJvwmFAEdI69b2uR6na/VN9WemsnpPGQz22r4FXxKRiU7XAMQkyBW9HHJ/9X7CEZ9vAYgxQp9FKd+hUiIfklK5bIbRMaNu1xxtqc+s0pHKUPP9u1fbRWsUV+TARlhwPchvs2c/T47/Xk6ajk9jX4iTvHXwpX/Bnht8AcL4y0tZYU1SgAAAABJRU5ErkJggg==";

    public enum AttributeType {
        SPLITBILLENABLED,
        PROFORMAENABLED,
        PODCLAIMENABLED,
        BOTTLESENABLED,
        OSB2INVENABLED  
    }
}