package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.TRL_CUST_MAP_HEADER;
import com.abinbev.oasis.be.TRL_PICK_DRP;
import com.abinbev.oasis.util.Constants;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class TrailerController {
    private static TrailerController trailerController = null;
   // private IDataManager iDataManager = null;
    TrailerController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static TrailerController getInstance() {
        if (trailerController == null) {
            trailerController = new TrailerController();
        }

        return trailerController;
    }


    public List<TRL_PICK_DRP> getPickedOrDroppedTrailersForCustomer(String cust_id, String type){
        String whrClause = "";
        List<TRL_PICK_DRP> trlPickDrps=new ArrayList<>();
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";

        switch (type)
        {
            case Constants.TRAILERPICK:
                whrClause = TRL_PICK_DRP.FIELD_SHIP_NO + " = '" + ship_no + "' AND " + TRL_PICK_DRP.FIELD_CUST_NO + " = '" + cust_id + "' AND (" + TRL_PICK_DRP.FIELD_IS_DRP + " IS NULL OR " + TRL_PICK_DRP.FIELD_IS_DRP + " = '')";
                break;
            case Constants.TRAILERDROP:
                whrClause = TRL_PICK_DRP.FIELD_SHIP_NO + " = '" + ship_no + "' AND " + TRL_PICK_DRP.FIELD_CUST_NO + " = '" + cust_id + "' AND " + TRL_PICK_DRP.FIELD_IS_DRP + " = 'X'";
                break;
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TRL_PICK_DRP.TABLE_NAME, whrClause);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    trlPickDrps.add((TRL_PICK_DRP) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(trlPickDrps.size()>0){
            Collections.sort(trlPickDrps, new Comparator<TRL_PICK_DRP>() {
                @Override
                public int compare(TRL_PICK_DRP lhs, TRL_PICK_DRP rhs) {
                    return lhs.getTRL_NO().compareTo(rhs.getTRL_NO());
                }
            });
            return trlPickDrps;
        }else {
            return  null;
        }

    }

    public List<TRL_CUST_MAP_HEADER> getTrailersForCustomer(String custId) {
        List<TRL_CUST_MAP_HEADER> trl_cust_map_headers=new ArrayList<>();
        String whrClause = TRL_CUST_MAP_HEADER.FIELD_CUST_NO + " = '" + custId + "'";
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TRL_CUST_MAP_HEADER.TABLE_NAME);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    trl_cust_map_headers.add((TRL_CUST_MAP_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(trl_cust_map_headers.size()>0){
            Collections.sort(trl_cust_map_headers, new Comparator<TRL_CUST_MAP_HEADER>() {
                @Override
                public int compare(TRL_CUST_MAP_HEADER lhs, TRL_CUST_MAP_HEADER rhs) {
                    return lhs.getTRAILER_ID().compareTo(rhs.getTRAILER_ID());
                }
            });
            return trl_cust_map_headers;
        }else {
            return null;
        }
    }

    public void insertOrUpdateTRLPickDrop(TRL_PICK_DRP header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void updateTrailerPickDrop(List<TRL_PICK_DRP> trl_pick_drpList) {
        for (TRL_PICK_DRP trl_pick_drp : trl_pick_drpList){
            insertOrUpdateTRLPickDrop(trl_pick_drp);
        }
    }

    public void clearTrailerPickDrop(TRL_PICK_DRP trl_pick_drp) {
        try {
            ApplicationManager.getInstance().getDataManager().delete(trl_pick_drp);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }
}
