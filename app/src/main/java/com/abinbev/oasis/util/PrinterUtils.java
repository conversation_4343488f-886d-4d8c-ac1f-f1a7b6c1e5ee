package com.abinbev.oasis.util;

import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.abinbev.oasis.activities.PrinterSetupActivity;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.unvired.core.ApplicationManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;
import com.unvired.login.LoginParameters;
import com.abinbev.oasis.R;
import com.unvired.print.PrinterFactory;

import java.util.Locale;

/**
 * Created by kunchok on 25/01/2021
 */
public class PrinterUtils {
    public static final int COL1_LEN = -20;
    public static final int COL2_LEN = -20;
    public static final int COL3_LEN = -20;

    public static final int LEN_QTN_TYPE = -10;
    public static final int LEN_QTN_NO = -5;
    public static final int LEN_QTN = -68;
    public static final int LEN_ANSW = 4;

    public static final int PRETRIP_SIGNATURE_BETWEEN_SPACE = 65;
    public static final int PRETRIP_SIGNATURE_LENGTH = 35;

    public static final String PRETRIP_REPORT_LABEL1 = "* A PRE TRIP inspection must to be completed by all Drivers before each trip.";
    public static final String PRETRIP_REPORT_LABEL2 = "* All SAFETY CRITICAL items that are defective or faulty should be marked as \"NO\"";
    public static final String PRETRIP_REPORT_LABEL3 = "* These need to be reported, rectified and signed off by SAB management before the vehicle can depart.";
    public static final String PRETRIP_REPORT_LABEL4 = "* It is incumbent on the Driver to ensure that the condition of the vehicle is accurately captured.";

    public static final String PRETRIP_REPORT_DEFECT_LABEL1 = "Defect Type";
    public static final String PRETRIP_REPORT_DEFECT_LABEL2 = "Description";

    public static final String PRETRIP_REPORT_CRITICAL_LABEL1 = "Critical";
    public static final String PRETRIP_REPORT_CRITICAL_LABEL21 = "Must be corrected before vehicle can be used again. Vehicle not allowed to leave the depot until fixed.";
    public static final String PRETRIP_REPORT_CRITICAL_LABEL22 = "allowed to leave the depot until fixed.";

    public static final String PRETRIP_REPORT_MAJOR_LABEL1 = "Major";
    public static final String PRETRIP_REPORT_MAJOR_LABEL21 = "To be corrected within 14 days from recorded date. Vehicle allowed to leave the depot.";
    public static final String PRETRIP_REPORT_MAJOR_LABEL22 = "to leave the depot.";

    public static final String PRETRIP_REPORT_MINOR_LABEL1 = "Minor";
    public static final String PRETRIP_REPORT_MINOR_LABEL21 = "To be corrected at next service, repairs or refurbishment. Vehicle allowed to leave the depot.";
    public static final String PRETRIP_REPORT_MINOR_LABEL22 = "allowed to leave the depot.";

    public static final int SIGNATURE_NAME_LENGTH = 24;

    public static boolean printFlag = true;
    public static final String DISABLE_PRINT = "9*";

    private static Context context;
    private static IPrintCallBack callback;

    private PrinterUtils() {
    }

    public static void print(String pdfPath, Context initContext, IPrintCallBack callbackObj, Enum<Constants.DocType> type, Constants.CopyType copyType,boolean isLast,boolean reprint) {
        context = initContext;
        callback = callbackObj;

        if (printFlag) { // if 0* is applied dnt even print
            String printerMacAddress = getMacFromDB();
            if (!Utils.isNullOrEmpty(printerMacAddress))
                showDialogAndPrint(printerMacAddress, pdfPath, callbackObj,type,copyType,isLast,reprint);
            else
                callback.onPrintError(new Exception("Printer Mac Adress is Empty. Please maintain it in Menu->Printer Configuration"), type);
        } else {
            callback.onPrintSuccess(type,copyType,isLast,pdfPath,reprint);
        }
    }

    private static void showDialogAndPrint(final String printerMacAddress, final String pdfPath, final IPrintCallBack callbackObj, final Enum<Constants.DocType> type, final Constants.CopyType copyType, final boolean isLast,boolean reprint) {
        final ProgressDialog progressDialog = ProgressDialog.show(context, "", "Printing in progress...");

        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                final PrintThread printerThread = new PrinterUtils().new PrintThread(printerMacAddress, pdfPath, progressDialog, callbackObj,type,copyType,isLast,reprint);

                printerThread.run();

                ((AppCompatActivity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        printerThread.onFinish();
                    }
                });
            }
        });
        t.start();
    }


    private static void confirmAfterPrinting(final Exception exception, final Enum<Constants.DocType> type, final Constants.CopyType copyType, final boolean isLast,String pdfPath,boolean reprint) {
        final Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_LEFT_ICON);
        dialog.setTitle(context.getResources().getString(R.string.print_status));
        dialog.setContentView(R.layout.confirm_print_result);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        LinearLayout okButton = dialog.findViewById(R.id.okayBtn);

        if (!((Activity) context).isFinishing()) {
            dialog.show();
        }

        TextView confirmTextView =  dialog.findViewById(R.id.message);
        TextView printStatus =  dialog.findViewById(R.id.print_status);
        printStatus.setText(context.getResources().getString(R.string.print_status));
        if (exception != null) {
            confirmTextView.setText(exception.getMessage());
            Logger.e("", exception);
        }

        okButton.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (exception != null) {
                    callback.onPrintError(exception,type);
                } else {
                    Logger.i("Print Success");
                    callback.onPrintSuccess(type,copyType,isLast,pdfPath,reprint);
                }
            }
        });
    }

    public static String getMacFromDB() {

        IDataStructure[] iDataStructure;
        try {
            String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_ADDRESS + "' ";
            iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);

            if (iDataStructure == null || iDataStructure.length <= 0) {
                Intent intent = new Intent(LoginParameters.getContext(), PrinterSetupActivity.class);
                LoginParameters.getContext().startActivity(intent);
            } else {
                CUSTOMIZATION_HEADER header = (CUSTOMIZATION_HEADER) iDataStructure[0];

                String printerMacAddress = header.getKEY_VALUE();

                if (!Utils.isNullOrEmpty(printerMacAddress))
                    return printerMacAddress;
                else {
                    Intent intent = new Intent(LoginParameters.getContext(), PrinterSetupActivity.class);
                    LoginParameters.getContext().startActivity(intent);
                }

            }
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, PrinterUtils.class.getName(), "getMacFromDB()", Utils.getExceptionMessage(e));
        }

        return null;
    }

    public static boolean hasMacFromDB() {

        IDataStructure[] iDataStructure;
        try {
            String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_ADDRESS + "' ";
            iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);

            return iDataStructure != null && iDataStructure.length > 0;
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, PrinterUtils.class.getName(), "getMacFromDB()", Utils.getExceptionMessage(e));
       return false;
        }
    }

    public static String padLeft(String inputString, int length,Character c) {
        if (inputString.length() >= length) {
            return inputString;
        }
        StringBuilder sb = new StringBuilder();
        while (sb.length() < length - inputString.length()) {
            sb.append(c);
        }
        sb.append(inputString);

        return sb.toString();
    }

    class PrintThread {
        String printerMacAddress;
        String pdfPath;
        ProgressDialog progressDialog;
        Exception exception;
        IPrintCallBack callback;
        Enum<Constants.DocType> type;
        Constants.CopyType copyType;
        boolean isLast;
        boolean reprint;

        public PrintThread(String address, String path, ProgressDialog dialog, IPrintCallBack callbackObj, Enum<Constants.DocType> type, Constants.CopyType copyType,boolean isLast,boolean reprint) {
            printerMacAddress = address;
            pdfPath = path;
            progressDialog = dialog;
            callback = callbackObj;
            this.type = type;
            this.copyType = copyType;
            this.isLast=isLast;
            this.reprint =reprint;
        }

        public void onFinish() {

            try {
                if(printerMacAddress!=null){
                    Log.d("TAG", "onFinish: CLEARING BUFFER"+printerMacAddress);
                    PrinterFactory.getPrinterImpl(printerMacAddress).clearBuffers();
                }
                if (progressDialog != null && progressDialog.isShowing()) {
                    progressDialog.dismiss();
                    progressDialog = null;
                }
            } catch (Exception e) {
                Logger.e("", e);
            }

            if (exception != null)
                confirmAfterPrinting(exception, this.type,this.copyType,this.isLast,pdfPath,this.reprint);
            else {
                Toast.makeText(context, "Printed Successfully.", Toast.LENGTH_SHORT).show();
                callback.onPrintSuccess( this.type,this.copyType,this.isLast,pdfPath,reprint);

            }
        }

        public void run() {
            try {
                PrinterFactory.getPrinterImpl(printerMacAddress).print(pdfPath, 5000);
            } catch (Exception e) {
                exception = e;
                Logger.log(Logger.LEVEL_ERROR, PrinterUtils.class.getName(), "PrintThread", Utils.getExceptionMessage(e));
            }
        }
    }


    public static void setLocale(Context context) {
        Locale locale = new Locale("en_US");
        Locale.setDefault(locale);
        Resources resources = context.getResources();
        Configuration config = resources.getConfiguration();
        config.setLocale(locale);
        resources.updateConfiguration(config, resources.getDisplayMetrics());
    }
}
