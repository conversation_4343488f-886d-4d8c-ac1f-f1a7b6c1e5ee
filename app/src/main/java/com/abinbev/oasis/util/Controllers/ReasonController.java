package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.List;

public class ReasonController {
    private static ReasonController reasonController = null;
   // private IDataManager iDataManager = null;

    ReasonController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }
    public static ReasonController getInstance() {
        if (reasonController == null) {
            reasonController = new ReasonController();
        }
        return reasonController;
    }

    public List<REASON_HEADER> getReasons(String type) {
        List<REASON_HEADER> reason_headerList = new ArrayList<>();
        String subQuery = " = '" + type + "'";
        if(Constants.REASON_TYPE_DELIVERY.equals(type)){
            subQuery = "IN ('" + type + "','" + Constants.REASON_TYPE_FULL_FBR_ZFDD + "')";
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(REASON_HEADER.TABLE_NAME, REASON_HEADER.FIELD_TYPE+" " +subQuery + " ORDER BY RSN_CODE");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    reason_headerList.add((REASON_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (reason_headerList.size() > 0) {
            return reason_headerList;
        } else {
            return null;
        }
    }
}
