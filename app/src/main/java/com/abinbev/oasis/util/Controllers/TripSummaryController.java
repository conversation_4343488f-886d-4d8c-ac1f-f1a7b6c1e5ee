package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;

import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

public class TripSummaryController {

    private static TripSummaryController tripSummaryController = null;
   // private IDataManager iDataManager = null;


    private VISIT currentVisitRow = null;
    public String NAT_CUSTOMER_TYPE;

    TripSummaryController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static TripSummaryController getInstance() {
        if (tripSummaryController == null) {
            tripSummaryController = new TripSummaryController();
        }
        return tripSummaryController;
    }

    public VISIT getCurrentVisitRow() {
        return currentVisitRow;
    }

    public void setCurrentVisitRow(VISIT currentVisitRow) {
        this.currentVisitRow = currentVisitRow;
    }

    public String getNAT_CUSTOMER_TYPE() {
        return NAT_CUSTOMER_TYPE;
    }

    public void setNAT_CUSTOMER_TYPE(String NAT_CUSTOMER_TYPE) {
        this.NAT_CUSTOMER_TYPE = NAT_CUSTOMER_TYPE;
    }

    public void insertOrUpdateVisit(VISIT header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public List<TRIP_SUMMARY> getTripList(String ship_no) {
        List<TRIP_SUMMARY> tripList = new ArrayList<>();
        String query = "SELECT V.STAT,V.SEQ_NO,C.NAME,C.CUST_NO,C.ADDRESS,C.ACC_MGR,C.ACC_MGR_TEL,C.TIME_WIN_FROM,C.TIME_WIN_TO,V.MILE,V.VISIT_NO,C.IS_NAT_CUST FROM VISIT AS V INNER JOIN CUSTOMER_HEADER AS C ON V.CUST_NO = C.CUST_NO AND V.SHIP_NO = '" + ship_no + "' ORDER BY V.SEQ_NO ASC, V.VISIT_NO DESC";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    int STAT = cursor.getInt(cursor.getColumnIndex("STAT"));
                    int SEQ_NO = cursor.getInt(cursor.getColumnIndex("SEQ_NO"));
                    String NAME = cursor.getString(cursor.getColumnIndex("NAME"));
                    String CUST_NO = cursor.getString(cursor.getColumnIndex("CUST_NO"));
                    String ADDRESS = cursor.getString(cursor.getColumnIndex("ADDRESS"));
                    String ACC_MGR = cursor.getString(cursor.getColumnIndex("ACC_MGR"));
                    String ACC_MGR_TEL = cursor.getString(cursor.getColumnIndex("ACC_MGR_TEL"));
                    String TIME_WIN_FROM = cursor.getString(cursor.getColumnIndex("TIME_WIN_FROM"));
                    String TIME_WIN_TO = cursor.getString(cursor.getColumnIndex("TIME_WIN_TO"));
                    float MILE = cursor.getFloat(cursor.getColumnIndex("MILE"));
                    int VISIT_NO = cursor.getInt(cursor.getColumnIndex("VISIT_NO"));
                    int IS_NAT_CUST = cursor.getInt(cursor.getColumnIndex("IS_NAT_CUST"));
                    TRIP_SUMMARY trip_summary = new TRIP_SUMMARY(STAT, SEQ_NO, NAME, CUST_NO, ADDRESS, ACC_MGR, ACC_MGR_TEL, TIME_WIN_FROM, TIME_WIN_TO, MILE, VISIT_NO, IS_NAT_CUST);
                    tripList.add(trip_summary);
                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
                return null;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return tripList;
    }

    public List<CUSTOMER_HEADER> getAdhocCustomers() {
        List<CUSTOMER_HEADER> customer_headerList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMER_HEADER.TABLE_NAME);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customer_headerList.add((CUSTOMER_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (customer_headerList.size() > 0) {
            Collections.sort(customer_headerList, new Comparator<CUSTOMER_HEADER>() {
                @Override
                public int compare(CUSTOMER_HEADER lhs, CUSTOMER_HEADER rhs) {
                    return lhs.getNAME().compareTo(rhs.getNAME());
                }
            });
            return customer_headerList;
        } else {
            return null;
        }
    }

    public VISIT getVisitForCustomer(String customer_id) {
        List<VISIT> visitList = new ArrayList<>();
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "(" + VISIT.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + VISIT.FIELD_CUST_NO + " = '" + customer_id + "') AND (" + VISIT.FIELD_HHT + " = 'X')";
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(VISIT.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    visitList.add((VISIT) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (visitList.size() > 0) {
            return visitList.get(0);
        } else {
            return null;
        }
    }

    public CUSTOMER_HEADER getCustomer(String custId) {
        List<CUSTOMER_HEADER> customerHeaders = new ArrayList<>();

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMER_HEADER.TABLE_NAME, CUSTOMER_HEADER.FIELD_CUST_NO + " = '" + custId + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customerHeaders.add((CUSTOMER_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (customerHeaders.size() > 0) {
            return customerHeaders.get(0);
        } else {
            return null;
        }
    }

    public int getNextVisitNo() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT MAX(SEQ_NO) FROM VISIT WHERE SHIP_NO = '" + ship_no + "'";
        int max = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                max = cursor.getInt(0);
                if (cursor.isLast()) {
                    cursor.close();
                }
            } else {
                cursor.close();
            }
            cursor.close();

        } catch (DBException e) {
            Logger.e("", e);
        }
        return (int) max + 1;
    }

    public boolean isAllVisitsComplete() {

        String query = "SELECT COUNT(*) FROM VISIT WHERE STAT <> " + Constants.VISIT_STATUS_END;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            cursor.moveToFirst();
            int count = cursor.getInt(0);
            cursor.close();
            if(count == 0){
                return true;
            }else {
                return false;
            }

        } catch (DBException e) {
            Logger.e("", e);
            return false;
        }
    }

    public void setVisitRow(int VISIT_NO) {
        List<VISIT> visitList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(VISIT.TABLE_NAME, VISIT.FIELD_VISIT_NO + " = " + VISIT_NO);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    visitList.add((VISIT) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        if (visitList.size() > 0) {
            TripSummaryController.getInstance().setCurrentVisitRow(visitList.get(0));
        }
    }

    public HashMap<Integer, Integer> get_No_Of_Deliveries() {
        HashMap<Integer, Integer> no_of_deliveries = new HashMap<>();
        String query = "SELECT DELIVERY.VISIT_NO, COUNT(DELIVERY.DELV_NO) AS NO_OF_DELIVERIES FROM DELIVERY INNER JOIN VISIT ON VISIT.VISIT_NO = DELIVERY.VISIT_NO GROUP BY DELIVERY.VISIT_NO";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    int VISIT_NO = cursor.getInt(cursor.getColumnIndex("VISIT_NO"));
                    int NO_OF_DELIVERIES = cursor.getInt(cursor.getColumnIndex("NO_OF_DELIVERIES"));
                    no_of_deliveries.put(VISIT_NO, NO_OF_DELIVERIES);
                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
            } else {
                cursor.close();
                return null;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return no_of_deliveries;
    }

    public void updateSeqNo() {
        long seqNo = 0;
        String query = "SELECT MAX(SEQ_NO) FROM VISIT WHERE STAT = " + Constants.VISIT_STATUS_END;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if(cursor!=null && cursor.getCount()>0) {
                cursor.moveToFirst();
                seqNo = cursor.getString(0)==null?0:cursor.getInt(0);
                cursor.close();
                if(seqNo+1==currentVisitRow.getSEQ_NO()){
                    return;
                }
                //Keep the original Seq no
                long orgSeqNo = currentVisitRow.getSEQ_NO();
                //Update the current row with new avaialble Seq No
                IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(VISIT.TABLE_NAME, "SHIP_NO = '"+DBHelper.getInstance().getShipmentRow().getSHIP_NO()+"' ORDER BY SEQ_NO");
                if (structures != null && structures.length > 0) {
                    for (IDataStructure structure : structures) {
                        VISIT visit = (VISIT) structure;
                        if(visit.getVISIT_NO().equals(currentVisitRow.getVISIT_NO())){
                                visit.setSEQ_NO(seqNo+1);
                        }else if(visit.getSEQ_NO()>seqNo && visit.getSEQ_NO()<orgSeqNo){
                            visit.setSEQ_NO(visit.getSEQ_NO()+1);
                        }
                        insertOrUpdateVisit(visit);
                    }
                }


            }
        } catch (DBException e) {
            Logger.e("", e);
            return;
        }

    }
}
