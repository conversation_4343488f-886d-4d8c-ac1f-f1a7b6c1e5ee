package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;
import android.graphics.Bitmap;

import com.abinbev.oasis.ModelClasses.DELIVERY_ITEM_PRICING;
import com.abinbev.oasis.ModelClasses.STOCK_REPORT;
import com.abinbev.oasis.ModelClasses.TRIP_INSP_PRINT_MODEL;
import com.abinbev.oasis.be.ANSWER_OPTION_HEADER;
import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.DELIVERY_ITM_COND;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.DRIVER_HEADER;
import com.abinbev.oasis.be.IMAGE_HEADER;
import com.abinbev.oasis.be.INVOICE;
import com.abinbev.oasis.be.INVOICE_ITEM;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.NUM_RANGE;
import com.abinbev.oasis.be.PRICE_HEADER;
import com.abinbev.oasis.be.QUESTION_HEADER;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.be.TIME;
import com.abinbev.oasis.be.TRAILER_HEADER;
import com.abinbev.oasis.be.TRIP_INSP;
import com.abinbev.oasis.be.TRL_CUST_MAP_HEADER;
import com.abinbev.oasis.be.TRL_PICK_DRP;
import com.abinbev.oasis.be.TRUCK_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.abinbev.oasis.util.Controllers.DeliveryController.Quantity;

public class DBHelper {

  //  private IDataManager iDataManager = null;
    private static DBHelper dbHelper = null;
    private HashMap<String, List<DELIVERY_ITEM_PRICING>> DeliveryItemPricingDictionary = new HashMap<>();

    DBHelper() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static DBHelper getInstance() {
        if (dbHelper == null) {
            dbHelper = new DBHelper();
        }
        return dbHelper;
    }


    public boolean unplannedCustomer = false;
    public boolean palletPickPrompted = false;


    public boolean isUnplannedCustomer() {
        return unplannedCustomer;
    }

    public void setUnplannedCustomer(boolean unplannedCustomer) {
        this.unplannedCustomer = unplannedCustomer;
    }

    public boolean isPalletPickPrompted() {
        return palletPickPrompted;
    }

    public void setPalletPickPrompted(boolean palletPickPrompted) {
        this.palletPickPrompted = palletPickPrompted;
    }




    public static enum ORDER_TYPE {
        //excluded for FBR/BIT and RCR
        ZFRD,
        ZFPD,
        ZFTD,
        ZFCD,
        ZFSD,
        //excluded for FBR/BIT and RCR
        ZUBR,
        //considered for FBR/BIT and RCR
        ZSD,
        ZFID
    }


    public void insertOrUpdatePerson(DEPOT_HEADER header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public List<DEPOT_HEADER> getDepotList() {
        List<DEPOT_HEADER> depotList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DEPOT_HEADER.TABLE_NAME, DEPOT_HEADER.FIELD_SELECTED + " = \"X\"");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    depotList.add((DEPOT_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return depotList;
    }

    public void insertOrUpdateNumRange(NUM_RANGE header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void updateShipmentStatus(SHIPMENT_HEADER header) {
        try {
            ApplicationManager.getInstance().getDataManager().update(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void insertOrUpdateAttachment(ATTACHMENT header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void insertOrUpdateTime(TIME header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }


    public void clearImages() {
        try {
            ApplicationManager.getInstance().getDataManager().delete(IMAGE_HEADER.TABLE_NAME);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void clearDepot() {
        try {
            ApplicationManager.getInstance().getDataManager().delete(DEPOT_HEADER.TABLE_NAME);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }




    public void clearShipmentAndRelatedBEs() {
        try {
            ApplicationManager.getInstance().getDataManager().delete(SHIPMENT_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TRUCK_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TRAILER_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(REASON_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(PRICE_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(MATERIAL_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(DRIVER_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(CUSTOMER_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(AUTH_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(ANSWER_OPTION_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TRL_CUST_MAP_HEADER.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(QUESTION_HEADER.TABLE_NAME);

            //In-case shipment related tables are not cleared via CASCADE DELETE constraint - try to clear explicitly
            ApplicationManager.getInstance().getDataManager().delete(VISIT.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TRL_PICK_DRP.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TIME.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(STOCK.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(TRIP_INSP.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(NUM_RANGE.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(INVOICE.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(INVOICE_ITEM.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(DELIVERY.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITEM.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITM_COND.TABLE_NAME);
            ApplicationManager.getInstance().getDataManager().delete(ATTACHMENT.TABLE_NAME);
//            IDataStructure[] structures = iDataManager.get(SHIPMENT_HEADER.TABLE_NAME);
        //    Logger.e("something");

        } catch (DBException e) {
            Logger.e("", e);
        }
    }


    public AUTH_HEADER getAuthHeaderByID(String authID) {
        AUTH_HEADER authHeader = null;
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(AUTH_HEADER.TABLE_NAME, AUTH_HEADER.FIELD_USER_ID + " = '" + authID + "'");

            if (structures != null && structures.length > 0) {
                authHeader = ((AUTH_HEADER) structures[0]);
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return authHeader;
    }

    public void updateAttachment(ATTACHMENT attachmentItem) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(attachmentItem);
        } catch (DBException e) {
            Logger.e("", e);
        }

    }



    public int getShipmentStatus() {

        float status = -1;
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(SHIPMENT_HEADER.TABLE_NAME);

            if (structures != null && structures.length > 0) {
                status = ((SHIPMENT_HEADER) structures[0]).getSTAT();
              //  Cursor cursor = iDataManager.executeQuery("SELECT COUNT(*) FROM VISIT WHERE SHIP_NO ='" + ((SHIPMENT_HEADER) structures[0]).getSHIP_NO() + "'");
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT COUNT(*) FROM VISIT WHERE SHIP_NO ='"+DBHelper.getInstance().getShipmentRow().getSHIP_NO()+"'");
                if (cursor == null) {
                    status = -1;
                } else {
                    if (cursor.moveToFirst()) {

                        int res = cursor.getInt(0);
                        if (res == 0) {
                            status = -1;
                        }
                        cursor.close();
                    }
                }
            } else {
                status = -1;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return -1;
        }
        Logger.i("SHIPMENT STATUE = " + status);
        return (int) status;
    }

    public SHIPMENT_HEADER getShipmentRow() {

        SHIPMENT_HEADER shipmentHeaderRow = null;

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(SHIPMENT_HEADER.TABLE_NAME);

            if (structures != null && structures.length > 0) {
                shipmentHeaderRow = ((SHIPMENT_HEADER) structures[0]);
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT COUNT(*) FROM VISIT WHERE SHIP_NO ='" + ((SHIPMENT_HEADER) structures[0]).getSHIP_NO() + "'");
                if (cursor == null) {
                    return null;
                } else {
                    if (cursor.moveToFirst()) {

                        int res = cursor.getInt(0);
                        if (res == 0) {
                            return null;
                        }
                        cursor.close();
                    }
                }
            } else {
                return null;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return shipmentHeaderRow;
    }


    public SHIPMENT_HEADER getCurrentShipment() {
        List<SHIPMENT_HEADER> shipmentList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(SHIPMENT_HEADER.TABLE_NAME);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    shipmentList.add((SHIPMENT_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (shipmentList.size() > 0) {
            return shipmentList.get(0);
        }

        return null;

    }

    public ATTACHMENT getAttachment(int type, String refNo, String signedBy, String imageType, String shipNo) {
        List<ATTACHMENT> attachmentList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(ATTACHMENT.TABLE_NAME, ATTACHMENT.FIELD_SHIP_NO + " = '" + shipNo + "' AND " + ATTACHMENT.FIELD_TYPE + "= " + type + " AND " + ATTACHMENT.FIELD_REF_NO + " = '" + refNo + "' AND " + ATTACHMENT.FIELD_DATA_TYPE + " = '" + imageType + "' AND " + ATTACHMENT.FIELD_SIGNED_BY + " = '" + signedBy + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    attachmentList.add((ATTACHMENT) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (attachmentList.size() > 0) {
            return attachmentList.get(0);
        }
        return null;
    }

    public void createNewTime(String objectId, String timeType, String currentTimeStamp) {
        if (IsTimeCaptureEnabled(timeType)) {
            TIME timeRow;
            SHIPMENT_HEADER shipment_header = getCurrentShipment();
            if (shipment_header != null) {
                timeRow = getTimeRow(shipment_header.getSHIP_NO(), objectId, timeType);
                if (timeRow == null) {
                    try {
                        timeRow = new TIME();
                        timeRow.setFid(shipment_header.getLid());
                        timeRow.setID(objectId);
                        timeRow.setLid(timeRow.getLid());
                        timeRow.setSHIP_NO(shipment_header.getSHIP_NO());
                        timeRow.setTYPE(timeType);
                        timeRow.setTIME(currentTimeStamp);
                        insertOrUpdateTime(timeRow);
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                } else {
                    timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                    insertOrUpdateTime(timeRow);
                }
            }
        }
    }


    public void createNewTime(String objectId, String timeType) {
        if (IsTimeCaptureEnabled(timeType)) {
            TIME timeRow;
            SHIPMENT_HEADER shipment_header = getCurrentShipment();
            if (shipment_header != null) {
                timeRow = getTimeRow(shipment_header.getSHIP_NO(), objectId, timeType);
                if (timeRow == null) {
                    try {
                        timeRow = new TIME();
                        timeRow.setFid(shipment_header.getLid());
                        timeRow.setID(objectId);
                        timeRow.setLid(timeRow.getLid());
                        timeRow.setSHIP_NO(shipment_header.getSHIP_NO());
                        timeRow.setTYPE(timeType);
                        timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                        insertOrUpdateTime(timeRow);
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                } else {
                    timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                    insertOrUpdateTime(timeRow);
                }
            }
        }
    }

    public void createTimeCheckinUser(String type, String userID) {
            TIME timeRow;
            SHIPMENT_HEADER shipment_header = getCurrentShipment();
            if (shipment_header != null) {
                timeRow = getTimeRow(shipment_header.getSHIP_NO(),type,userID);
                if (timeRow == null) {
                    try {
                        timeRow = new TIME();
                        timeRow.setFid(shipment_header.getLid());
                        timeRow.setID(userID);
                        timeRow.setLid(timeRow.getLid());
                        timeRow.setSHIP_NO(shipment_header.getSHIP_NO());
                        timeRow.setTYPE(type);
                        timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                        insertOrUpdateTime(timeRow);
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                } else {
                    timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                    insertOrUpdateTime(timeRow);

            }
        }
    }

    public TIME getTimeRow(String ship_no, String objectId, String timeType) {
        List<TIME> timeList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TIME.TABLE_NAME, TIME.FIELD_SHIP_NO + " = '" + ship_no + "' AND " + TIME.FIELD_ID + " = '" + objectId + "' AND " + TIME.FIELD_TYPE + " = '" + timeType + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    timeList.add((TIME) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (timeList.size() > 0) {
            return timeList.get(0);
        }
        return null;
    }

    public static boolean IsTimeCaptureEnabled(String timeType) {
        boolean ret = false;
        switch (timeType) {
            case Constants.TIME_TYPE_ODOMETER_INITIAL:                 // "DD";  //SAP - DD (Depot Departure) - Initial Odometer
            case Constants.TIME_TYPE_VISIT_START:                      // "CA";  //SAP - CA (Customer Arrival)- Odometer reading capture
            case Constants.TIME_TYPE_CUSTOMER_RCR_UPDATE:              // "CB";  //SAP - CB (Customer Return Update Time) - RCR Capture
            case Constants.TIME_TYPE_CUSTOMER_FBR_BIT_UPDATE:          // "CD";  //SAP - CD (Customer Delivery Update Time) – Capture FBR/BIT
            case Constants.TIME_TYPE_CUSTOMER_PRE_INVOICE:             // "CC";  //SAP - CC (Customer Pre-Invoice Time) – Display Invoice on HH
            case Constants.TIME_TYPE_CUSTOMER_PRINT_INVOICE:           // "CI";  //SAP - CI (Customer Print Invoice Time) – Print Invoice on HH
            case Constants.TIME_TYPE_CUSTOMER_PRINT_INPUT_CHECKLIST:   // "CS";  //SAP - CS (Customer Stock Report Print) – Print Input Checklist
            case Constants.TIME_TYPE_DEPOT_STOCK_INITIAL:              // "DSI";  //SAP - DS (Depot Stock) – Print Final Reports
            case Constants.TIME_TYPE_DEPOT_STOCK_FINAL:                // "DSF";  //SAP - DS (Depot Stock) – Print Final Reports
            case Constants.TIME_TYPE_ODOMETER_FINAL:                   // "DA";  //SAP - DA (Depot Arrival) – Final Odometer
                ret = true;
                break;

            case Constants.TIME_TYPE_VISIT_END:
                ret = OasisCache.getTimeEnableConfig().charAt(1) == '1';
                break;
            case Constants.TIME_TYPE_DELIVERY:
                ret = OasisCache.getTimeEnableConfig().charAt(2) == '1';
                break;
            case Constants.TIME_TYPE_TRAILER:
                ret = OasisCache.getTimeEnableConfig().charAt(3) == '1';
                break;
            case Constants.TIME_TYPE_CHECKIN_START:
                ret = OasisCache.getTimeEnableConfig().charAt(5) == '1';
                break;
            case Constants.TIME_TYPE_CHECKIN_END:
                ret = OasisCache.getTimeEnableConfig().charAt(6) == '1';
                break;
            case Constants.TIME_TYPE_END_TRIP:
                ret = OasisCache.getTimeEnableConfig().charAt(7) == '1';
                break;
            case Constants.TIME_TYPE_SHIP_DNLD_START:
            case Constants.TIME_TYPE_SHIP_DNLD_END:
                ret = OasisCache.getTimeEnableConfig().charAt(8) == '1';
                break;
            case Constants.TIME_TYPE_INV_GEN_START:
            case Constants.TIME_TYPE_INV_GEN_END:
                ret = OasisCache.getTimeEnableConfig().charAt(9) == '1';
                break;
            case Constants.TIME_TYPE_INV_PRN_START:
            case Constants.TIME_TYPE_INV_PRN_END:
                ret = OasisCache.getTimeEnableConfig().charAt(10) == '1';
                break;
            case Constants.TIME_TYPE_INV_PDFGEN_START:
            case Constants.TIME_TYPE_INV_PDFGEN_END:
                ret = OasisCache.getTimeEnableConfig().charAt(11) == '1';
                break;
            case Constants.TIME_TYPE_SHIP_UPLOAD_START:
            case Constants.TIME_TYPE_SHIP_UPLOAD_END:
                ret = OasisCache.getTimeEnableConfig().charAt(12) == '1';
                break;
        }
        return ret;
    }

    public List<Object> getQuestionAndAnswer() {

        List<Object> questionsList = new ArrayList<>();

        try {
            Cursor structures = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT T.INSP_TYPE, Q.Q_ID, Q.QUES, T.ANS_TEXT FROM TRIP_INSP AS T INNER JOIN QUESTION_HEADER AS Q ON T.Q_ID = Q.Q_ID WHERE  (T.INSP_TYPE = 'PRETRIP' OR T.INSP_TYPE = 'PRETRIPFL') AND (T.SHIP_NO = '"+DBHelper.getInstance().getCurrentShipment().getSHIP_NO()+"') ORDER BY Q.Q_ID");


            if (structures != null) {
                while (!structures.isLast()) {
                    Object temp = structures.moveToNext();
                    questionsList.add(temp);

                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return questionsList;
    }

    public List<TRIP_INSP> getQuestions(String currentInspectionType) {

        List<TRIP_INSP> tripInspHeader = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TRIP_INSP.TABLE_NAME, TRIP_INSP.FIELD_INSP_TYPE + " = \"" + currentInspectionType + "\" ORDER BY " + TRIP_INSP.FIELD_SEQ + " ASC ");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    tripInspHeader.add((TRIP_INSP) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return tripInspHeader;
    }

    public TRIP_INSP getSelectedTripInsp(String currentInspectionType, long qId) {

        List<TRIP_INSP> tripInspHeader = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TRIP_INSP.TABLE_NAME, TRIP_INSP.FIELD_Q_ID + " = " + qId + " AND " + TRIP_INSP.FIELD_INSP_TYPE + " = \"" + currentInspectionType + "\" ORDER BY " + TRIP_INSP.FIELD_SEQ + " ASC ");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    tripInspHeader.add((TRIP_INSP) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return tripInspHeader.get(0);
    }

    public QUESTION_HEADER getQuestion(Long questionId) {

        List<QUESTION_HEADER> questionHeader = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(QUESTION_HEADER.TABLE_NAME, QUESTION_HEADER.FIELD_Q_ID + " = \"" + questionId + "\"");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    questionHeader.add((QUESTION_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return questionHeader.get(0);
    }

    public List<ANSWER_OPTION_HEADER> getAnswerOptions(Long questionId) {
        List<ANSWER_OPTION_HEADER> answerOptions = new ArrayList<>();
        try {
            ANSWER_OPTION_HEADER temp = new ANSWER_OPTION_HEADER();
            temp.setQ_ID(questionId);
            temp.setANS_TEXT("");
            temp.setSEQ((long) 0);
            answerOptions.add(temp);
        } catch (DBException e) {
            Logger.e("", e);
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(ANSWER_OPTION_HEADER.TABLE_NAME, ANSWER_OPTION_HEADER.FIELD_Q_ID + " = \"" + questionId + "\"");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    answerOptions.add((ANSWER_OPTION_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return answerOptions;

    }



    public List<String> getCompletedInvoicing(List<String> delvList) {
        List<String> invList = new ArrayList<>();
        String deliveryINStr = "";
        for (String delv : delvList) {
            if (deliveryINStr.isEmpty() || deliveryINStr == null) {
                deliveryINStr = "'" + delv + "'";
            } else {
                deliveryINStr = deliveryINStr + ", '" + delv + "'";
            }
        }
        String query = "SELECT DISTINCT TIME.ID, DELIVERY.DELV_NO FROM TIME INNER JOIN DELIVERY ON TIME.ID = DELIVERY.INV_NO WHERE (DELIVERY.DELV_NO IN (" + deliveryINStr + "))";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                while (cursor.moveToNext()) {
                    invList.add(cursor.getString(1));
                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
                if (!cursor.isClosed()) {
                    cursor.close();
                }
            }else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        return invList;
    }




    public void updateTripInspHeader(TRIP_INSP updatedRow) {
        try {
            ApplicationManager.getInstance().getDataManager().update(updatedRow);
        } catch (DBException e) {
            Logger.e("", e);
        }
        List<TRIP_INSP> tempList = getQuestions(Constants.TRIP_INSP_TYPE_PRETRIP);
    }

    public List<REASON_HEADER> getReasonHeaders(String type) {
        List<REASON_HEADER> reasonHeaders = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(REASON_HEADER.TABLE_NAME, REASON_HEADER.FIELD_TYPE + " = \"" + type + "\" ORDER BY " + REASON_HEADER.FIELD_RSN_CODE);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    reasonHeaders.add((REASON_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return reasonHeaders;
    }

    public boolean hasOnlyReturnsCollectionOrder() {
        boolean returnValue = true;
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT MAT_NO FROM DELIVERY_ITEM WHERE (SHIP_NO = '" + ship_no + "') AND (DELV_NO IN(SELECT DELV_NO FROM DELIVERY WHERE (SHIP_NO = '" + ship_no + "') AND (VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + ") AND (ORD_TYPE = '" + ORDER_TYPE.ZSD + "') AND (HH_CREATED IS NULL OR HH_CREATED = '')))";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    String mat_no = cursor.getString(0);
                    if ((MaterialController.getInstance().getDummyMaterialNos())!=null && !Arrays.asList(MaterialController.getInstance().getDummyMaterialNos()).contains(mat_no)) {
                        returnValue = false;
                        cursor.close();
                        break;
                    }
                    if(MaterialController.getInstance().getDummyMaterialNos()==null){
                        returnValue = false;
                        cursor.close();
                        break;
                    }

                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
            } else {
                cursor.close();
                return returnValue;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return returnValue;
        }

        return returnValue;

    }

    public boolean checkPrintEnable() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        List<DELIVERY> deliveryheaders = DeliveryController.getInstance().getDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), Quantity.ALL);
        if (deliveryheaders == null || deliveryheaders.size() <= 0) {
            return false;
        }
        String deliveries = "";
        //iterate through deliveries
        for (int i = 0; i < deliveryheaders.size(); i++) {
            String delvNo = "'" + deliveryheaders.get(i).getDELV_NO() + "'";
            if (deliveries.isEmpty() || deliveries.equals("")) {
                deliveries = delvNo;
            } else {
                deliveries = deliveries + "," + delvNo;
            }
        }

        if (!deliveries.equals("") && !deliveries.isEmpty()) {
            String query = "SELECT COUNT(*) FROM DELIVERY_ITEM WHERE SHIP_NO = '" + ship_no + "' AND DELV_NO IN (" + deliveries + ") AND HH_CREATED = 'X' AND IS_EMPTY = 'X' AND IS_RETURN = 'X'";
            try {
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
                cursor.moveToFirst();
                int count = cursor.getInt(0);
                cursor.close();
               if(count>0){
                   return true;
               }else {
                   return false;
               }
            } catch (DBException e) {
                Logger.e("", e);
                return false;
            }
        }
        return false;

    }

    public List<AUTH_HEADER> getAuthHeaderList(String type) {
        List<AUTH_HEADER> authHeaders = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(AUTH_HEADER.TABLE_NAME, AUTH_HEADER.FIELD_TYPE + " = \"" + type + "\" ORDER BY " + AUTH_HEADER.FIELD_NAME);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    authHeaders.add((AUTH_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return authHeaders;
    }

    public boolean isForkliftInspectionRequired() {
        int count = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT COUNT(*) FROM TRIP_INSP WHERE " + TRIP_INSP.FIELD_INSP_TYPE + " = '" + Constants.TRIP_INSP_TYPE_PRE_FORKLIFT + "'");
            if (cursor == null) {
                count = 0;
            } else {
                if (cursor.moveToFirst()) {
                    int res = cursor.getInt(0);
                    if (res == 0) {
                        count = 0;
                    } else {
                        count = res;
                    }
                    cursor.close();
                }
            }
        } catch (Exception ex) {
            Logger.e(ex.getMessage());
        }
        return count > 0;

    }

    public boolean PreTripOrPostTripReqAuth(Constants.SCREEN_NAVIGATION_MODE currentScreenNavigationMode) {
        String query = null;
        int count = 0;
        try {
            if (Constants.SCREEN_NAVIGATION_MODE.PostForkLiftInspection == currentScreenNavigationMode) {
                //Check if any of the posttrip answer requires auth
                query = "SELECT COUNT(*) AS COUNT FROM TRIP_INSP AS TR INNER JOIN ANSWER_OPTION_HEADER AS AH ON TR.Q_ID = AH.Q_ID AND TR.ANS_ID = AH.ANS_ID WHERE  (TR.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_POSTTRIP + "') AND (AH.IS_AUTH_REQD = 'X')";
            } else {
                //Check if any of the pretrip answer requires auth
                query = "SELECT COUNT(*) AS COUNT FROM TRIP_INSP AS TR INNER JOIN ANSWER_OPTION_HEADER AS AH ON TR.Q_ID = AH.Q_ID AND TR.ANS_ID = AH.ANS_ID WHERE  (TR.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_PRETRIP + "') AND (AH.IS_AUTH_REQD = 'X')";
            }
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor == null) {
                count = 0;
            } else {
                if (cursor.moveToFirst()) {
                    int res = cursor.getInt(0);
                    if (res == 0) {
                        count = 0;
                    } else {
                        count = res;
                    }
                    cursor.close();
                }
            }
        } catch (Exception ex) {
            Logger.e(ex.getMessage());
        }
        return count > 0;
    }

    public String incrementAndUpdateNumRange(String docType) {
        NUM_RANGE num_range = getNumRange(docType);
        String numRange = "1";
        if (num_range != null) {
            if (num_range.getLAST_USED_NUM().isEmpty() || num_range.getLAST_USED_NUM() == null) {
                num_range.setLAST_USED_NUM("1");
            }
            numRange = String.valueOf(Integer.parseInt(num_range.getLAST_USED_NUM()) + 1);
            num_range.setLAST_USED_NUM(numRange);
            if (!(num_range.getPREFIX().isEmpty() || num_range.getPREFIX() == null)) {
                numRange = Utils.padLeft(numRange, (10 - (num_range.getPREFIX().length() + (numRange.length() - 1))), '0');
            }
            insertOrUpdateNumRange(num_range);
            numRange = num_range.getPREFIX() + numRange;
        }
        return numRange;
    }

    public void decrementAndUpdateNumRange(String docType) {
        NUM_RANGE num_range = getNumRange(docType);
        if (num_range != null) {
            if (!num_range.getLAST_USED_NUM().isEmpty() && num_range.getLAST_USED_NUM() != null) {
                String numRange = String.valueOf(Integer.parseInt(num_range.getLAST_USED_NUM()) - 1);
                num_range.setLAST_USED_NUM(numRange);
                insertOrUpdateNumRange(num_range);
            }
        }
    }

    public NUM_RANGE getNumRange(String docType) {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        List<NUM_RANGE> numRangeList = new ArrayList<>();
        String query = NUM_RANGE.FIELD_SHIP_NO + " = '" + ship_no + "' AND " + NUM_RANGE.FIELD_DOC_TYPE + " = '" + docType + "'";
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(NUM_RANGE.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    numRangeList.add((NUM_RANGE) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (numRangeList.size() > 0) {
            return numRangeList.get(0);
        } else {
            return null;
        }
    }


    public int getPalletInTruckByStock() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String stockQuery = "SELECT SUM(ACT_QTY) AS PAL_STOCK FROM STOCK WHERE (MAT_NO IN(SELECT MAT_NO FROM MATERIAL_HEADER WHERE (TYPE = '" + Constants.VERP + "' OR TYPE = '" + Constants.LEER + "') AND ITM_CAT = '" + Constants.ITEM_CATEGORY_PALLET + "')) AND TYPE = 'E' AND SHIP_NO = '" + ship_no + "'";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(stockQuery);
            if (cursor != null && cursor.getCount() > 0) {
                int column1 = 0;
                cursor.moveToFirst();
                if(!cursor.isNull(0)) {
                    column1 = cursor.getInt(0);
                }
                if (!cursor.isClosed()) {
                    cursor.close();
                }
                return column1;
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
                return 0;
            }

        } catch (DBException e) {
            Logger.e("", e);
            return 0;
        }
    }

    public IMAGE_HEADER getImageForMaterial(String matNo) {
        List<IMAGE_HEADER> imageHeaders = new ArrayList<>();
        String query = IMAGE_HEADER.FIELD_IMG_NAME + " = '" + matNo + "'";

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(IMAGE_HEADER.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    imageHeaders.add((IMAGE_HEADER) structure);
                }
            } else {
                query = IMAGE_HEADER.FIELD_IMG_NAME + " = '00000'";
                IDataStructure[] defaultStructures = ApplicationManager.getInstance().getDataManager().get(IMAGE_HEADER.TABLE_NAME, query);
                if (defaultStructures != null && defaultStructures.length > 0) {
                    for (IDataStructure defaultStructure : defaultStructures) {
                        imageHeaders.add((IMAGE_HEADER) defaultStructure);
                    }
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (imageHeaders.size() > 0) {
            return imageHeaders.get(0);
        } else {
            return null;
        }
    }
    public List<INVOICE> getInvoiceList(String query) {
        List<INVOICE> invoiceList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(INVOICE.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    invoiceList.add((INVOICE) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (invoiceList.size() > 0) {
            return invoiceList;
        } else {
            return null;
        }
    }
    public List<DRIVER_HEADER> getDriverList(String query) {

        List<DRIVER_HEADER> driverHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DRIVER_HEADER.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    driverHeaderList.add((DRIVER_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (driverHeaderList.size() > 0) {
            return driverHeaderList;
        }

        return driverHeaderList;
    }
    public List<String> getCompleteInvoiceByQ(String queryStr) {
        List<String> stringList = new ArrayList<>();
        try {
            Cursor delList = ApplicationManager.getInstance().getDataManager().executeQuery(queryStr);
            if(delList != null && delList.getCount() > 0) {
                while (delList.moveToNext())
                {
                    if (!delList.isNull(1))
                    {
                        stringList.add(delList.getString(1));
                    }
                }
                delList.close();
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return stringList;
    }
    public DEPOT_HEADER getDepot(String depotNo) throws DBException {
        List<DEPOT_HEADER> depotHeader = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DEPOT_HEADER.TABLE_NAME, DEPOT_HEADER.FIELD_DEPOT + " = "+depotNo);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    depotHeader.add((DEPOT_HEADER) structure);
                }
            }
            if (depotHeader.size() > 0) {
                return depotHeader.get(0);
            }
            return null;

        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
    }
    public List<INVOICE_ITEM> getInvoiceItemList(String query) {
        List<INVOICE_ITEM> invoiceList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(INVOICE_ITEM.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    invoiceList.add((INVOICE_ITEM) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (invoiceList.size() > 0) {
            return invoiceList;
        } else {
            return null;
        }
    }
    public void insertOrUpdateInvoice(List<INVOICE> invoiceList) {
        try {
            for(INVOICE invoice : invoiceList) {
                ApplicationManager.getInstance().getDataManager().insert(invoice);
            }

        } catch (DBException e) {
            Logger.e("", e);
        }
    }
    public void insertOrUpdateInvoiceItem(List<INVOICE_ITEM> invoice_itemList) {
        try {
            for(INVOICE_ITEM item : invoice_itemList) {
                ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(item);
            }

        } catch (DBException e) {
            Logger.e("", e);
        }
    }
    public List<REASON_HEADER> getReasons(String type) {
        List<REASON_HEADER> reason_headerList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(REASON_HEADER.TABLE_NAME, REASON_HEADER.FIELD_TYPE + " = '" + type + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    reason_headerList.add((REASON_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (reason_headerList.size() > 0) {
            return reason_headerList;
        } else {
            return null;
        }
    }
    public Bitmap getDriverCustomerMergedBitmap(String driver, String delivery_no, int distanceBetweenSignatures) {
        Bitmap mergedBitmap = null;

        VISIT visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
        if (visitRow == null)
            return null;
        String custNo = visitRow.getCUST_NO()==null ? "" : visitRow.getCUST_NO();
        if (Utils.isNullOrEmpty(custNo))
            return null;
        List<ATTACHMENT> attachmentDataTable = new ArrayList<>();
        String query =  ATTACHMENT.FIELD_SHIP_NO + " = '" + getShipmentRow().getSHIP_NO() + "' AND " + ATTACHMENT.FIELD_TYPE + "= " + Constants.ATTACH_TYPE_INVOICE + " AND " + ATTACHMENT.FIELD_REF_NO + " = '" + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString()+ delivery_no +"' AND " + ATTACHMENT.FIELD_SIGNED_BY + " IN (" + driver + "," + custNo + ")";
        attachmentDataTable =getAttachment(query);
        if (attachmentDataTable == null || attachmentDataTable.size() <= 0)
            return null;

        Bitmap driverSignature = null;
        Bitmap customerSignature = null;

        for (ATTACHMENT attachmentRow: attachmentDataTable)
        {
            String hexData = attachmentRow.getDATA();
            if (!Utils.isNullOrEmpty(hexData))
            {
                if (attachmentRow.getSIGNED_BY().equals(driver))
                {
                    driverSignature = ImageUtil.getBitmapFromBase64(hexData);
                }
                else if (attachmentRow.getSIGNED_BY().equals(custNo))
                {
                    customerSignature = ImageUtil.getBitmapFromBase64(hexData);
                }
            }
        }

        if (driverSignature != null && customerSignature != null)
            mergedBitmap = ImageUtil.merge(driverSignature, customerSignature, distanceBetweenSignatures);

        return mergedBitmap;
    }
    public List<CUSTOMIZATION_HEADER> getInvoiceKeys(String pattern)
    {
        List<CUSTOMIZATION_HEADER> customizationHeaderList = new ArrayList<>();
        String whereClause =CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " like '" + pattern + "%' ORDER BY " + CUSTOMIZATION_HEADER.FIELD_KEY_NAME;

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customizationHeaderList.add((CUSTOMIZATION_HEADER) structure);
                }
            }

            return customizationHeaderList;

        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }


    }

    public List<DELIVERY_ITEM> getDeliveryItems(String deliveryNo, Quantity orderType) {
        List<DELIVERY_ITEM> deliveryItemList = new ArrayList<>();
        String ship_no = "";
        ship_no = DBHelper.getInstance().getShipmentRow().getSHIP_NO();

        String query = "(" + DELIVERY.FIELD_SHIP_NO + " = '" + ship_no + "') AND (" + DELIVERY.FIELD_DELV_NO + " = '" + deliveryNo + "')";
        switch (orderType) {
            case FBRBIT:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' )";
                //query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' ) AND (" + deliveryItemString.IS_EMPTYColumn + " IS NULL OR " + deliveryItemDataTable.IS_EMPTYColumn + " = '' )";
                break;
            case RCR:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NOT NULL AND " + DELIVERY_ITEM.FIELD_RCR_QTY + " > 0)";
                break;
            case UBR:
                //same as FBR/BIT
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_EMPTY + " = '')";
                break;
            case PALLETPICK:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " = 0)";
                break;
            case PALLETDROP:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' ) AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " = 0)";
                break;

        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryItemList.add((DELIVERY_ITEM) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (deliveryItemList.size() > 0) {
            return deliveryItemList;
        } else {
            return null;
        }

    }

    public  void updateDelivery(List<DELIVERY> deliveryList) {
        try {
            for(DELIVERY item : deliveryList) {
                ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(item);
            }

        } catch (DBException e) {
            Logger.e("", e);
        }
    }
    public List<DELIVERY_ITM_COND> getDeliveryItemCon(String query) {
        List<DELIVERY_ITM_COND> deliveryItmCondList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITM_COND.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryItmCondList.add((DELIVERY_ITM_COND) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (deliveryItmCondList.size() > 0) {
            return deliveryItmCondList;
        } else {
            return null;
        }
    }

    private List<ATTACHMENT> getAttachment(String query) {
        List<ATTACHMENT> attachmentList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(ATTACHMENT.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    attachmentList.add((ATTACHMENT) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (attachmentList.size() > 0) {
            return attachmentList;
        }

        return attachmentList;

    }

    public List<TIME> getTimeRowByQuery(String query) {
        List<TIME> timeList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(TIME.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    timeList.add((TIME) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (timeList.size() > 0) {
            return timeList;
        } else {
            return null;
        }
    }
    public List<TRIP_INSP_PRINT_MODEL> getTripQuestionAndAnswer(SHIPMENT_HEADER shipment_header, String inspectionType) {
        String query = null;
        List<TRIP_INSP_PRINT_MODEL> tripInspList = new ArrayList<>();
        switch (inspectionType)
        {
            case Constants.TRIP_INSP_TYPE_PRETRIP:
                query = "SELECT T.INSP_TYPE, Q.Q_ID, Q.QUES, T.ANS_TEXT FROM TRIP_INSP AS T INNER JOIN QUESTION_HEADER AS Q ON T.Q_ID = Q.Q_ID WHERE  (T.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_PRETRIP + "' OR T.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_PRE_FORKLIFT + "') AND (T.SHIP_NO = '" + shipment_header.getSHIP_NO() + "') ORDER BY Q.Q_ID";
                break;

            case Constants.TRIP_INSP_TYPE_POSTTRIP:
                query = "SELECT T.INSP_TYPE, Q.Q_ID, Q.QUES, T.ANS_TEXT FROM TRIP_INSP AS T INNER JOIN QUESTION_HEADER AS Q ON T.Q_ID = Q.Q_ID WHERE  (T.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_POSTTRIP + "' OR T.INSP_TYPE = '" + Constants.TRIP_INSP_TYPE_POST_FORKLIFT + "') AND (T.SHIP_NO = '" + shipment_header.getSHIP_NO() + "') ORDER BY Q.Q_ID";
                break;
        }

        if (Utils.isNullOrEmpty(query))
        {
            return null;
        }
        Cursor cursor = null;
        try {
            cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    TRIP_INSP_PRINT_MODEL trip_insp = new TRIP_INSP_PRINT_MODEL();
                    trip_insp.setInspectionType(cursor.getString(cursor.getColumnIndex("INSP_TYPE")));
                    trip_insp.setQid(cursor.getLong(cursor.getColumnIndex("Q_ID")));
                    trip_insp.setQuestion(cursor.getString(cursor.getColumnIndex("QUES")));
                    trip_insp.setAnswer(cursor.getString(cursor.getColumnIndex("ANS_TEXT")));
                    tripInspList.add(trip_insp);
                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
                return  tripInspList;
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
                return null;

            }
        } catch (DBException e) {
            Logger.e("", e);
            return new ArrayList<>();
        }



    }
    public String[] getDummyMaterialNos() {
        List<String> matNos = new ArrayList<>();
        if (OasisCache.DummyMaterials == null) {
            String query = "SELECT MAT_NO FROM MATERIAL_HEADER WHERE ITM_CAT='" + Constants.MATERIAL_DUMMY_TYPE + "'";
            try {
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
                if (cursor != null && cursor.getCount() > 0) {
                    while (cursor.moveToNext()) {
                        String mat_no = cursor.getString(0);
                        matNos.add(mat_no);
                        if (cursor.isLast()) {
                            cursor.close();
                        }
                    }
                    String[] mStringArray = new String[matNos.size()];
                    mStringArray = matNos.toArray(mStringArray);
                    OasisCache.DummyMaterials = mStringArray;
                } else {
                    cursor.close();
                    return OasisCache.DummyMaterials;
                }
            } catch (DBException e) {
                Logger.e("", e);
                return OasisCache.DummyMaterials;
            }

        }
        return OasisCache.DummyMaterials;
    }

    public Map<String, STOCK_REPORT> getMaterial(String excludeClause) {
        String delvItemQuery = "SELECT di.MAT_NO, di.MAT_DESC, di.QTY, di.BIT_QTY, di.FBR_QTY, di.UBR_QTY, di.RCR_QTY, di.IS_EMPTY, di.IS_RETURN, di.PROMO_TYPE_DESC, v.STAT FROM VISIT AS v INNER JOIN DELIVERY AS dh ON v.VISIT_NO = dh.VISIT_NO INNER JOIN DELIVERY_ITEM AS di ON dh.DELV_NO = di.DELV_NO" + excludeClause + " ORDER BY di.MAT_NO";
        Cursor cursor = null;
        try {
            String MatNo = "";
            String MatDesc = "";
            int OriginalLoad = 0;
            int Offloaded = 0;
            int QTY = 0;
            int RCR = 0;
            int UBR = 0;
            int FBR = 0;
            int BIT = 0;
            int TruckLoad = 0;
            String IS_EMPTY = "";
            String IS_RETURN = "";
            Map<String, STOCK_REPORT> stockHash = new TreeMap<>();
            cursor = ApplicationManager.getInstance().getDataManager().executeQuery(delvItemQuery);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    STOCK_REPORT bean = new STOCK_REPORT();
                    bean.MAT_NO = Utils.isNullOrEmpty(cursor.getString(cursor.getColumnIndex("MAT_NO"))) ? "" : cursor.getString(cursor.getColumnIndex("MAT_NO"));
                    if (stockHash.containsKey(bean.MAT_NO))
                        bean = stockHash.get(bean.MAT_NO);
                    else
                        stockHash.put(bean.MAT_NO, bean);

                    bean.MAT_DESC = Utils.isNullOrEmpty(cursor.getString(cursor.getColumnIndex("MAT_DESC"))) ? "" : cursor.getString(cursor.getColumnIndex("MAT_DESC"));

                    int VisitStatus = cursor.isNull(cursor.getColumnIndex("STAT")) ? 0 : cursor.getInt(cursor.getColumnIndex("STAT"));
                    // #1375 : ignore QTY for OSB ITEM

                    if (!cursor.isNull(cursor.getColumnIndex("PROMO_TYPE_DESC")) && "OSB".equals(cursor.getString(cursor.getColumnIndex("PROMO_TYPE_DESC"))))
                    {
                        QTY = 0;
                    }
                    else {
                        QTY = cursor.isNull(cursor.getColumnIndex("QTY")) ? 0 : (int)cursor.getFloat(cursor.getColumnIndex("QTY"));
                    }
                    BIT = cursor.isNull(cursor.getColumnIndex(("BIT_QTY"))) ? 0 : (int) cursor.getFloat(cursor.getColumnIndex("BIT_QTY"));
                    UBR = cursor.isNull(cursor.getColumnIndex(("UBR_QTY"))) ? 0 : (int) cursor.getFloat(cursor.getColumnIndex(("UBR_QTY")));
                    FBR = cursor.isNull(cursor.getColumnIndex(("FBR_QTY"))) ? 0 : (int) cursor.getFloat(cursor.getColumnIndex(("FBR_QTY")));
                    RCR = cursor.isNull(cursor.getColumnIndex(("RCR_QTY"))) ? 0 : (int) cursor.getFloat(cursor.getColumnIndex(("RCR_QTY")));
                    //IS_EMPTY = string.IsNullOrEmpty(sqlResultSet.GetString(sqlResultSet.GetOrdinal("IS_EMPTY"))) ? "" : sqlResultSet.GetValue(sqlResultSet.GetOrdinal("IS_EMPTY")).ToString();
                    //IS_RETURN = string.IsNullOrEmpty(sqlResultSet.GetString(sqlResultSet.GetOrdinal("IS_RETURN"))) ? "" : sqlResultSet.GetValue(sqlResultSet.GetOrdinal("IS_RETURN")).ToString();

                    bean.BIT = bean.BIT + BIT;
                    bean.RCR = bean.RCR + RCR;
                    bean.UBR = bean.UBR + UBR;
                    bean.FBR = bean.FBR + FBR;

                    Offloaded = QTY - BIT - FBR;
                    if(VisitStatus == Constants.VISIT_STATUS_END_REACHED || VisitStatus == Constants.VISIT_STATUS_END)
                        bean.OFFLOADED = bean.OFFLOADED + Offloaded;

                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
                String StockQuery = "SELECT MAT_NO, MAT_DESC, PLN_QTY, ACT_QTY, TYPE FROM STOCK" + excludeClause + " ORDER BY MAT_NO";
                cursor = ApplicationManager.getInstance().getDataManager().executeQuery(StockQuery);

                while (cursor.moveToNext())
                {
                    MatNo = Utils.isNullOrEmpty(cursor.getString(cursor.getColumnIndex("MAT_NO"))) ? "" : cursor.getString(cursor.getColumnIndex("MAT_NO"));
                    STOCK_REPORT bean = stockHash.containsKey(MatNo) ? stockHash.get(MatNo) : null;
                    if(bean == null) {
                        bean = new STOCK_REPORT();
                        stockHash.put(MatNo, bean);
                        bean.MAT_NO = MatNo;
                        bean.MAT_DESC = Utils.isNullOrEmpty(cursor.getString(cursor.getColumnIndex("MAT_DESC"))) ? "" : cursor.getString(cursor.getColumnIndex("MAT_DESC"));
                    }

                    bean.ORG_LOAD = bean.ORG_LOAD + (cursor.isNull(cursor.getColumnIndex(("PLN_QTY"))) ? 0 : cursor.getDouble(cursor.getColumnIndex("PLN_QTY")));
                    bean.TRUCK_STOCK = bean.TRUCK_STOCK + (cursor.isNull(cursor.getColumnIndex("ACT_QTY")) ? 0 : cursor.getDouble(cursor.getColumnIndex("ACT_QTY")));
                }
                cursor.close();

                return  stockHash;
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
                return null;

            }
        } catch (DBException e) {
            Logger.e("", e);
            return new HashMap<>();
        }
    }

    public CUSTOMER_HEADER getCustomer(String custId) {
        List<CUSTOMER_HEADER> customerHeaders = new ArrayList<>();

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMER_HEADER.TABLE_NAME, CUSTOMER_HEADER.FIELD_CUST_NO + " = '" + custId + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customerHeaders.add((CUSTOMER_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (customerHeaders.size() > 0) {
            return customerHeaders.get(0);
        } else {
            return null;
        }
    }

    public MATERIAL_HEADER getMaterialList(String matNo) {
        List<MATERIAL_HEADER> material_headers = new ArrayList<>();

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(MATERIAL_HEADER.TABLE_NAME, MATERIAL_HEADER.FIELD_MAT_NO + " = '" + matNo + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    material_headers.add((MATERIAL_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (material_headers.size() > 0) {
            return material_headers.get(0);
        } else {
            return null;
        }
    }
    public String getStringKeyDesc(String key) {
        String res = null;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT KEY_DESC FROM CUSTOMIZATION_HEADER WHERE KEY_NAME='" + key + "'");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    res = cursor.getString(0);
                    cursor.close();
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return res;
    }

    public CUSTOMIZATION_HEADER getCustomizationHeader(String keyName){
        List<CUSTOMIZATION_HEADER> customization_headers = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, CUSTOMIZATION_HEADER.FIELD_KEY_NAME+ " like '" + keyName + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customization_headers.add((CUSTOMIZATION_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (customization_headers.size() > 0) {
            return customization_headers.get(0);
        } else {
            return null;
        }
    }

    public  ATTACHMENT getAttachment(int type, String refNo, String signedBy, String imageType)
    {
        String query = ATTACHMENT.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + ATTACHMENT.FIELD_TYPE + "= " + type + " AND " + ATTACHMENT.FIELD_REF_NO + " = '" + refNo + "' AND " + ATTACHMENT.FIELD_DATA_TYPE + " = '" + imageType + "' AND " + ATTACHMENT.FIELD_SIGNED_BY + " = '" + signedBy + "'";
        List<ATTACHMENT> attachmentDataTable = DBHelper.getInstance().getAttachment(query);

        if (attachmentDataTable == null || attachmentDataTable.size() <= 0 || attachmentDataTable.get(0) == null)
            return null;

        return attachmentDataTable.get(0);
    }

    public List<NUM_RANGE> getNumRange(String query, String docType) {
        //String ship_no = getCurrentShipment().getSHIP_NO();
        String ship_no = "11312033";
        List<NUM_RANGE> numRangeList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(NUM_RANGE.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    numRangeList.add((NUM_RANGE) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (numRangeList.size() > 0) {
            return numRangeList;
        } else {
            return null;
        }
    }
    public List<PRICE_HEADER> getPriceHeaderList() {
        List<PRICE_HEADER> priceHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(PRICE_HEADER.TABLE_NAME);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    priceHeaderList.add((PRICE_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (priceHeaderList.size() > 0) {
            return priceHeaderList;
        } else {
            return null;
        }
    }

    public void addAttachment(ATTACHMENT attachmentItem) {
        try {
            ApplicationManager.getInstance().getDataManager().insert(attachmentItem);
        } catch (DBException e) {
            Logger.e("", e);
        }

    }


    public void dataAdjustments() {
        //Clear Delivery Item Conditions  - Due to wrong Design its removed here.
        String query = "DELETE FROM DELIVERY_ITM_COND";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (!cursor.isClosed()) {
                cursor.close();
            }
        } catch (DBException e) {
            Logger.e("", e);
        }

        //Fix to clear QTY for OSB temp items
        DeliveryController.getInstance().osb_DeliveryItemsQTYCorrection();

        //Fix to treat PAL PICKUPS as RCR for SAP - Due to wrong Design its removed here.
        DeliveryController.getInstance().setPalletReturnQuantityRight();

        //Set the Dummy Materials CHECKIN QTY same as ACT_QTY.
        updateDummyMaterialStockAndDeliveryItem();
    }

    private void updateDummyMaterialStockAndDeliveryItem() {
        String[] dummies = MaterialController.getInstance().getDummyMaterialNos();
        if(dummies!=null ) {
            if (dummies.length > 0){
                String whrClause = " IN (";
            int i = 0;
            for (String dummy : dummies) {
                if (i == 0) {
                    whrClause += "'" + dummy + "'";
                } else {
                    whrClause += ",'" + dummy + "'";
                }
                i++;
            }
            whrClause += ")";

            //Set Dummy Materials as RCR at end of shipment.
            try {
                IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X' AND " + DELIVERY_ITEM.FIELD_MAT_NO + whrClause);
                if (structures != null && structures.length > 0) {
                    for (IDataStructure structure : structures) {
                        DELIVERY_ITEM deliveryItem = (DELIVERY_ITEM) structure;
                        deliveryItem.setRCR_QTY(deliveryItem.getQTY());
                        DeliveryController.getInstance().insertOrUpdateDeliveryItem(deliveryItem);
                    }
                }
            } catch (DBException e) {
                Logger.e("", e);
            }

            String deleteQuery = "DELETE FROM STOCK WHERE MAT_NO" + whrClause;
            try {
                Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(deleteQuery);
                if (!cursor.isClosed()) {
                    cursor.close();
                }
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
        }
    }


}
