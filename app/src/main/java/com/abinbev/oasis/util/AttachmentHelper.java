package com.abinbev.oasis.util;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.os.Environment;
import android.util.Base64;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

public class AttachmentHelper {

    public static AttachmentHelper attachmentHelper;

    public static AttachmentHelper getInstance() {
        if (attachmentHelper == null) {
            attachmentHelper = new AttachmentHelper();
        }

        return attachmentHelper;
    }

    public ATTACHMENT getAttachment(int type, String refNo, String signedBy, String imageType) {
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        ATTACHMENT attachment = null;
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(ATTACHMENT.TABLE_NAME, ATTACHMENT.FIELD_SHIP_NO + " = '" + shipRow.getSHIP_NO() + "' AND " + ATTACHMENT.FIELD_TYPE + " = " + type + " AND " + ATTACHMENT.FIELD_REF_NO + " = '" + refNo + "' AND " + ATTACHMENT.FIELD_DATA_TYPE + " = '" + imageType + "' AND " + ATTACHMENT.FIELD_SIGNED_BY + " = '" + signedBy + "'");

            if (structures != null && structures.length > 0) {
                attachment = ((ATTACHMENT) structures[0]);
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return attachment;
    }

    public void saveInvoiceAsAttachment(ATTACHMENT attachmentRow, String invoiceNo, String custNo, File pdfPath, DELIVERY delivery) {
//        ATTACHMENTTableAdapter AttachmentTableAdapter = new ATTACHMENTTableAdapter();
        try {
            String base64Value = "";
            File pdfFile = pdfPath;

            if (!pdfFile.exists())
                return;

            int size = (int) pdfFile.length();
            byte[] bytes = new byte[size];
            BufferedInputStream buf = new BufferedInputStream(new FileInputStream(pdfFile));
            buf.read(bytes, 0, bytes.length);
            buf.close();
            base64Value = Base64.encodeToString(bytes, Base64.NO_WRAP);

            if (Utils.isNullOrEmpty(base64Value))
                return;

            if (attachmentRow == null) {
                SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
                attachmentRow = new ATTACHMENT();
                attachmentRow.setDATA_TYPE(Constants.PDF_TYPE);
                attachmentRow.setSHIP_NO(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
                attachmentRow.setSIGNED_BY(custNo);
                attachmentRow.setFid(shipRow.getLid());
                //If its HH create delivery or ZUBR order/delivery the invoice will not be in SAP at time of dispatch
                if ((delivery.getHH_CREATED() != null && delivery.getHH_CREATED().equalsIgnoreCase("X")) || (delivery.getORD_TYPE() != null && !Utils.isNullOrEmpty(delivery.getORD_TYPE()) && delivery.getORD_TYPE().equals(Constants.ORDER_TYPE.ZUBR)))
                    attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_INVOICE_HH_DELV);
                else
                    attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_INVOICE);
                attachmentRow.setREF_NO(invoiceNo);
                attachmentRow.setDATA(base64Value);
                ApplicationManager.getInstance().getDataManager().insert(attachmentRow);
            } else {
                attachmentRow.setDATA(base64Value);
                ApplicationManager.getInstance().getDataManager().update(attachmentRow);
            }
        } catch (DBException e) {
            Logger.e("", e);
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            Logger.e("", e);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            Logger.e("", e);
        }
    }

    public void storeCustomerPod(Bitmap image) {

        String hexValue = ImageUtil.getBase64FromBitmapInJpeg(image);
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getDeliveryNoFromAdapter());
        ATTACHMENT attachmentRow = null;
        try {
            attachmentRow = new ATTACHMENT();
            attachmentRow.setDATA(hexValue);
            attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE_JPG);
            attachmentRow.setREF_NO(delivery.getINV_NO());
            attachmentRow.setSHIP_NO(shipRow.getSHIP_NO());
            attachmentRow.setSIGNED_BY((visit.getCUST_NO() == null || visit.getCUST_NO().isEmpty()) ? "" : visit.getCUST_NO());
            attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_POD);
            attachmentRow.setFid(shipRow.getLid());
            DBHelper.getInstance().updateAttachment(attachmentRow);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void storeCustomerClaim(Bitmap image) {

        String hexValue = ImageUtil.getBase64FromBitmapInJpeg(image);
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        ATTACHMENT attachmentRow = null;
        DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getDeliveryNoFromAdapter());

        try {
            attachmentRow = new ATTACHMENT();
            attachmentRow.setDATA(hexValue);
            attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE_JPG);
            attachmentRow.setREF_NO(delivery.getINV_NO());
            attachmentRow.setSHIP_NO(shipRow.getSHIP_NO());
            attachmentRow.setSIGNED_BY((visit.getCUST_NO() == null || visit.getCUST_NO().isEmpty()) ? "" : visit.getCUST_NO());
            attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_CLAIM);
            attachmentRow.setFid(shipRow.getLid());
            DBHelper.getInstance().updateAttachment(attachmentRow);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public ATTACHMENT getCustomerPod() {
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getDeliveryNoFromAdapter());
        return AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_POD, delivery.getINV_NO(), visit.getCUST_NO(), Constants.IMAGE_TYPE_JPG);
    }

    public ATTACHMENT getCustomerClaim() {
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getDeliveryNoFromAdapter());
        return AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_CLAIM, delivery.getINV_NO(), visit.getCUST_NO(), Constants.IMAGE_TYPE_JPG);
    }


}
