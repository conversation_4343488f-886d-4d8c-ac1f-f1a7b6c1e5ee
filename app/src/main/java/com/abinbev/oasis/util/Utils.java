package com.abinbev.oasis.util;



import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.abinbev.oasis.activities.SettingActivity;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.google.android.material.textfield.TextInputEditText;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPTable;
import com.unvired.core.FrameworkVersion;
import com.unvired.core.UserSettingsManager;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.login.AuthenticationService;
import com.unvired.login.LoginParameters;
import com.unvired.model.ApplicationVersion;
import com.abinbev.oasis.R;


import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

public class Utils {



    public static Long getPersonNumber(String message) {
        if (message == null || message.isEmpty()) {
            return 0l;
        }

        try {
            return Long.parseLong(message.substring(message.indexOf("=") + 1, message.lastIndexOf(")")));
        } catch (Exception e) {
            Logger.e("", e);
        }
        return 0l;
    }


    public static String get_UTC_DatetimeAsString()
    {
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        final String utcTime = sdf.format(new Date());

        return utcTime;
    }

    public static String GetPassword(String userID)
    {
        Calendar date = Calendar.getInstance();
        int dayOfWeek = (int)date.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek == 0)
            dayOfWeek = 7;
        int year = (int)date.get(Calendar.YEAR);
        int month = (int)date.get(Calendar.MONTH) + 1;
        int day = (int)date.get(Calendar.DAY_OF_MONTH);
        String yyyymmdd = String.valueOf(year) + (month < 10 ? "0" + String.valueOf(month) : String.valueOf(month)) + (day < 10 ? "0" + String.valueOf(day) : String.valueOf(day));

        String buffer = userID + yyyymmdd;
        int buffLen = buffer.length();
        int asciiSum = 0;
        for (int i = 0; i < buffLen; i++)
        {
            asciiSum += buffer.charAt(i);
        }

        year = (int)date.get(Calendar.YEAR) % 2000;
        String date6 = String.valueOf(year) + (month < 10 ? "0" + String.valueOf(month) : String.valueOf(month)) + (day < 10 ? "0" + String.valueOf(day) : String.valueOf(day));

        //  Rotate date6 in right circular depending on the day no of week
        date6 = rotateRightCircular(date6, dayOfWeek);

        //  Rotate asciisum by two places
        String asciiStr = String.valueOf(asciiSum);
        asciiStr = rotateLeftCircular(asciiStr, 2);

        //  Add the numeric value of the rotated strings
        int date6Int = Integer.parseInt(date6);
        int asciiStrInt = Integer.parseInt(asciiStr);

        // Magic number
        int magicNo = date6Int + asciiStrInt;
        String magicString = "";

        if (String.valueOf(magicNo).length() == 5)
        {
            magicNo = magicNo * 9;
            magicString = String.valueOf(magicNo);
        }
        else if (String.valueOf(magicNo).length() == 7)
        {
            magicString = String.valueOf(magicNo);
            magicString = magicString.substring(1, 6);
        }
        else
        {
            magicString = String.valueOf(magicNo);
        }
        // Finally roate magic string by day of week
        magicString = rotateRightCircular(magicString, dayOfWeek);
        return magicString;
    }

    public static String rotateRightCircular(String value, int places)         //409261
    {
        String result = value; //140926
        int strLen = value.length();
        if (places >= strLen)
            places = places % strLen;

        if (places > 0)
        {
            result = "";
            int newStart = strLen - places;
            for (int i = newStart; i < strLen; i++)
                result += value.charAt(i);

            for (int i = 0; i < newStart; i++)
                result += value.charAt(i);
        }
        return result;
    }

    public static String rotateLeftCircular(String value, int places) {
        String result = value;
        int strLen = value.length();
        if (places >= strLen)
            places = places % strLen;

        if (places > 0) {
            result = "";
            int newStart = places;

            for (int i = newStart; i < strLen; i++)
                result += value.charAt(i);

            for (int i = 0; i < newStart; i++)
                result += value.charAt(i);
        }
        return result;
    }
    public static void disableButton(View button, Context context){
        button.setEnabled(false);
        button.setBackground(context.getResources().getDrawable(R.drawable.button_greyed_out));
    }
    public static void enableButton(View button, Context context){
        button.setEnabled(true);
        button.setBackground(context.getResources().getDrawable(R.drawable.button_bg_proceed));
    }

    public static String getEncryptedText(String passwordText)
    {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(passwordText.getBytes());
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < array.length; ++i) {
                sb.append(Integer.toHexString((array[i] & 0xFF) | 0x100).substring(1,3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            Logger.log(Logger.LEVEL_ERROR, "Utils", "getEncryptedText", "Exception while parsing: " + getExceptionMessage(e));
        }
        return null;
    }


    @SuppressLint("SetTextI18n")
    public static void showSettingDialog(final Context context) {

        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setCancelable(false);
        final View dialogView = LayoutInflater.from(context).inflate(R.layout.depot_password_dialog, null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.depot_dialog_close_icon);
        Button okBtn = dialogView.findViewById(R.id.depot_pass_ok_btn);
        Button cancelBtn = dialogView.findViewById(R.id.depot_cancel_btn);
        TextView versionText = dialogView.findViewById(R.id.dialog_depot_version);
        TextView depotName = dialogView.findViewById(R.id.dialog_depot_name);
        TextView shipmentNo = dialogView.findViewById(R.id.dialog_shipment_name);
        TextView driverName = dialogView.findViewById(R.id.dialog_driver_name);
        TextView truckNo = dialogView.findViewById(R.id.dialog_truck_no);
        TextView userName = dialogView.findViewById(R.id.dialog_user_name);
        final TextInputEditText pwdText = dialogView.findViewById(R.id.depot_password);
        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
        List<DEPOT_HEADER> depot_headers = DBHelper.getInstance().getDepotList();
            if (depot_headers != null && !depot_headers.isEmpty()) {
                depotName.setText(depot_headers.get(0).getNAME() + "(" + depot_headers.get(0).getDEPOT() + ")");
            } else {
                depotName.setText("Depot Not Set");
            }
            versionText.setText("Version: "+ FrameworkVersion.getApplicationVersion());
            if (shipment_header != null) {
                shipmentNo.setText("Shipment : " + shipment_header.getSHIP_NO());
                driverName.setText("Driver : " + shipment_header.getDRV1_NAME());
                truckNo.setText("Truck : " + shipment_header.getTRUCK());
            } else {
                shipmentNo.setText("Shipment : Not Available");
                driverName.setText("Driver : Not Available");
                truckNo.setText("Truck : Not Available");
            }
            if(Utils.getUserId()!=null && !Utils.getUserId().isEmpty()){
                userName.setText("user : "+ Utils.getUserId());
            }else{
                userName.setText("user : Not Available");
            }
            okBtn.setOnClickListener(view -> {
                String enteredPassword = pwdText.getText().toString();
                String enterPwd = enteredPassword;
                if (!enteredPassword.isEmpty()) {
                    String password = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_DEPOT_PASSWORD);
                    password = password == null ? Utils.getEncryptedText(Constants.DEPOT_PASSWORD_DEFAULT) : password;
                    enteredPassword = Utils.getEncryptedText(enteredPassword);
                    if (!enteredPassword.equalsIgnoreCase(password)) {
                        if (enterPwd.startsWith("9*")) {
                            Constants.PRN_VIA_BT = true;
                            Constants.PRN_BT_ADDRESS = enteredPassword.split(String.valueOf(new char[]{'*'}))[1];
                        } else if (enterPwd.equals("0"))
                            Constants.PRN_VIA_BT = false;
                        else if (enterPwd.equals("0*"))
                            PrinterUtils.printFlag = false;
                        else if (enterPwd.equals("1*"))
                            PrinterUtils.printFlag = true;
                        else if (enterPwd.equals("*0"))
                            Constants.BYPASS_PRN = false;
                        pwdText.setError("Incorrect Password.");
                        pwdText.setText("");
                        pwdText.setFocusable(true);
                    } else {
                        context.startActivity(new Intent(context, SettingActivity.class));
                        alertDialog.dismiss();
                    }
                } else {
                    pwdText.setError("Enter Password");
                }
            });

            dialogClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    alertDialog.dismiss();
                }
            });
            cancelBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    alertDialog.dismiss();
                }
            });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE  | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        pwdText.requestFocus();

        alertDialog.show();
        }

    public static String padLeft(String original, int padToLength, char padWith) {
        if (original.length() >= padToLength) {
            return original;
        }
        StringBuilder sb = new StringBuilder(padToLength);
        for (int i = original.length(); i < padToLength; ++i) {
            sb.append(padWith);
        }
        sb.append(original);
         return sb.toString();
    }

    public static String getExceptionMessage(Exception e) {
        String msg = "Unknown Exception";
        StringWriter errors = new StringWriter();
        if (e != null) {
            e.printStackTrace(new PrintWriter(errors));
            msg = errors.toString();
        }
        return msg;
    }

    public static boolean isNullOrEmpty(String string) {
        if (string != null && !string.isEmpty()) {
            return false;
        }
        return true;
    }

    public static byte[] decodeToByte(String data, int aDefault) {
        try {
            return android.util.Base64.decode(data, aDefault);
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, PrinterUtils.class.getName(), "decodeToImage", getExceptionMessage(e));
            return null;
        }
    }

    public static Font getFont(float size) {
        String[][] FONTS = {{"assets/ReformaGroteskMediumC.ttf", BaseFont.WINANSI}};
        BaseFont base = null;
        try {
            base = BaseFont.createFont(FONTS[0][0], BaseFont.CP1252, true);
        } catch (DocumentException | IOException e) {
            Logger.log(Logger.LEVEL_ERROR, Utils.class.getName() + "", "getFont", Utils.getExceptionMessage(e) + "");
        }
        return new Font(base, size, Font.NORMAL);
    }
    public static String getCurrentTimeStamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss",Locale.ENGLISH).format(new Date());

    }
    public static String getStringFromDate(Date date,String format){
        SimpleDateFormat dateFormat = new SimpleDateFormat(format,Locale.ENGLISH);
        return dateFormat.format(date);
    }
    public static Date getDateFromString(String date, String format){
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            Logger.e("", e);
            Logger.log(Logger.LEVEL_ERROR, "Utils", "getDateFromString", "Exception while parsing: " + getExceptionMessage(e));
            return new Date();
        }
    }

    public static BaseColor getColor() {
      return  new BaseColor(255, 255, 255);
    }
    public static double round(double value, int places) {

        if(Double.isInfinite((Double) value) || Double.isNaN((Double) value))
            return 0.0;
        if (places < 0) throw new IllegalArgumentException();

//        BigDecimal bd = BigDecimal.valueOf(value);
//        bd = bd.setScale(places, RoundingMode.FLOOR);
        DecimalFormat df2 = null;
        if(places==2){
            df2 = new DecimalFormat("#.##");
        }else if(places==3){
            df2 = new DecimalFormat("#.###");
        }

        df2.setRoundingMode(RoundingMode.HALF_EVEN);
        value = Double.parseDouble(df2.format(value));
        return value;

//        double roundedNumber;
//        if(value>=0){
//            roundedNumber=(value*(Math.pow(10,(places+1)))+5)/10;
//            DecimalFormat df = new DecimalFormat("#.##");
//            df.setRoundingMode(RoundingMode.FLOOR);
//            roundedNumber= Double.parseDouble(df.format(roundedNumber));
//        }else {
//            roundedNumber=(value*(Math.pow(10,(places+1)))-5)/10;
//            roundedNumber=(int) roundedNumber;
//        }
//
//        roundedNumber=roundedNumber*(Math.pow(10,-places));
//
//        return roundedNumber;



    }


    public static double round(float value1, int places) {
        double value = (double)value1;
        if(Double.isInfinite((Double) value) || Double.isNaN((Double) value))
            return 0.0;
        if (places < 0) throw new IllegalArgumentException();

//        BigDecimal bd = BigDecimal.valueOf(value);
//        bd = bd.setScale(places, RoundingMode.FLOOR);

        DecimalFormat df2 = new DecimalFormat("#.##");
        df2.setRoundingMode(RoundingMode.HALF_EVEN);
        value = Double.parseDouble(df2.format(value));
        return value;

//        double roundedNumber;
//        if(value>=0){
//            roundedNumber=(value*(Math.pow(10,(places+1)))+5)/10;
//            DecimalFormat df = new DecimalFormat("#.##");
//            df.setRoundingMode(RoundingMode.FLOOR);
//            roundedNumber= Double.parseDouble(df.format(roundedNumber));
//        }else {
//            roundedNumber=(value*(Math.pow(10,(places+1)))-5)/10;
//            roundedNumber=(int) roundedNumber;
//        }
//
//        roundedNumber=roundedNumber*(Math.pow(10,-places));
//
//        return roundedNumber;



    }



    public static double roundFloor(Number value, int places) {
        Double d = value.doubleValue();
        if(Double.isInfinite((Double) value) || Double.isNaN((Double) value))
            return 0.0;
        if (places < 0) throw new IllegalArgumentException();

//        BigDecimal bd = BigDecimal.valueOf(value);
//        bd = bd.setScale(places, RoundingMode.FLOOR);
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.FLOOR);

        return Double.parseDouble((df.format(d)));



    }

    public static String getUserId() {

        String userIdValue = null;

        UserSettingsManager userSettingsManager = UserSettingsManager.getInstance();
        try {

            userIdValue = userSettingsManager.getUnviredUserId();
            if (userIdValue != null)
                return userIdValue;
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, "Utils", "getUserId", "DBException while getUnviredUserId: " + getExceptionMessage(e));
        }

        return userIdValue;
    }

    public static boolean isInternetConnected(Activity activity) {
        ConnectivityManager cm = (ConnectivityManager) activity.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo netInfo = cm.getActiveNetworkInfo();
        if (netInfo != null && netInfo.isConnected()) {
            return true;
        } else {
            return false;
        }
    }

    public static String formatDouble(double value, int zeroCount) {
       return String.format(Locale.ENGLISH,"%.2f",Utils.round(value, zeroCount));
    }

    public static void deletePdfFiles(String filePath) {
        File dir = new File(filePath).getParentFile();
        if (dir!=null && dir.isDirectory())
        {
            String[] children = dir.list();
            if(children!=null && children.length>0){
                for (String child : children) {
                    new File(dir, child).delete();
                }
            }
        }
    }

    public static String truncateString(String prodDesc,int length) {
        prodDesc = prodDesc.substring(0, Math.min(prodDesc.length(), length));
        return prodDesc;
    }

    public static PdfPTable getSpaceSeparatorTable(Font font) {
        PdfPTable space = new PdfPTable(1);
        space.setWidthPercentage(100);
        space.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        space.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        space.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        space.getDefaultCell().setPadding(0);
        space.getDefaultCell().setExtraParagraphSpace(0);
        //space.getDefaultCell()
        space.addCell(new Paragraph("--------------------------------------------------------------------------------------------------------------------------------------------------------------", font));
        return space;
    }
    public static InputStream getLogoImage(DEPOT_HEADER currentDepot, Context context){
        InputStream inStream = null;
        try{
            /*String imageName = "";
             switch (currentDepot.getDEPOT()){
               case "9030":{
                    imageName = "img/9030.png";
                    break;
                }
                case "9050":{
                    imageName = "img/9050.png";
                    break;
                }
                case "9060":{
                    imageName = "img/9060.png";
                    break;
                }
                case "9080":{
                    imageName = "img/9080.png";
                    break;
                }
                case "9090":{
                    imageName = "img/9090.png";
                    break;
                }
                case "9110":{
                    imageName = "img/9110.png";
                    break;
                }
                case "9130":{
                    imageName = "img/9130.png";
                    break;
                }
                case "9140":{
                    imageName = "img/9140.png";
                    break;
                }
                default:{
                    imageName = "img/SAB.png";
                    break;
                }
            }*/

            inStream = context.getAssets().open("img/" + currentDepot.getDEPOT().trim() + ".png");


        }catch (Exception e){
            //means its SAB Logo
        }
        try {
            if (inStream == null) {
                inStream = context.getAssets().open("img/SAB.png");
            }
        }catch (Exception e){
            Logger.e("", e);
        }
        return inStream;
    }

    public static boolean isAttributeEnabled(CUSTOMER_HEADER customerHeader, Constants.AttributeType attributeType){
        if (customerHeader == null) {
            return false;
        }
        String attributes = customerHeader.getATTRIBUTES() == null ? "" : customerHeader.getATTRIBUTES().toUpperCase();
        List<String> attributesArr = Arrays.asList(attributes.split(","));
        switch (attributeType){
            case SPLITBILLENABLED :
                return checkAttribute(attributesArr, 0);
            case PROFORMAENABLED :
                return checkAttribute(attributesArr, 1);
            case PODCLAIMENABLED :
                return checkAttribute(attributesArr, 2);
            case BOTTLESENABLED:
                return checkAttribute(attributesArr, 3);
            case OSB2INVENABLED:
                return checkAttribute(attributesArr, 4);
            default:
                return false;
        }
    }

    private static boolean checkAttribute(List<String> attributesArr, int index) {
        return index < attributesArr.size() && "X".equals(attributesArr.get(index));
    }
}

