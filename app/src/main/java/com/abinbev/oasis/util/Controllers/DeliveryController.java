package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;
import android.util.Log;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.DELIVERY_ITM_COND;
import com.abinbev.oasis.be.INVOICE;
import com.abinbev.oasis.be.INVOICE_ITEM;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TRL_PICK_DRP;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class DeliveryController {

    private static DeliveryController deliveryController = null;
    //private IDataManager iDataManager = null;

    public void insertOrUpdateVisit(VISIT header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void processUnProcessedUBR() {
        List<DELIVERY> deliveryList = getDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), Quantity.UBR);
        if (deliveryList != null && deliveryList.size() > 0) {
            for (DELIVERY delivery : deliveryList) {
                List<DELIVERY_ITEM> delivery_itemList = getDeliveryItems(delivery.getDELV_NO(), Quantity.UBR);
                if (delivery_itemList != null && delivery_itemList.size() > 0) {
                    for (DELIVERY_ITEM delivery_item : delivery_itemList) {
                        if ((delivery_item.getSLS_DEAL() == null || !delivery_item.getSLS_DEAL().equals("X")) && delivery_item.getUBR_QTY() == 0) {
                            delivery_item.setUBR_QTY(delivery_item.getQTY());
                            insertOrUpdateDeliveryItem(delivery_item);
                        }
                    }
                }
            }
        }
    }

    public void deleteDelivery(DELIVERY delivery) {
        try {
            ApplicationManager.getInstance().getDataManager().delete(delivery);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void osb_DeliveryItemsQTYCorrection() {
        //set the QTY of all OST delivery items to 0
        try {
            ApplicationManager.getInstance().getDataManager().execute("UPDATE DELIVERY_ITEM SET QTY = 0 WHERE PROMO_TYPE_DESC = 'OSB'");
            //Delete all OSB items which were not touched for BIT or FBR
            ApplicationManager.getInstance().getDataManager().execute("DELETE FROM DELIVERY_ITEM WHERE PROMO_TYPE_DESC = 'OSB' AND (QTY = 0 OR QTY IS NULL) AND (BIT_QTY = 0 OR BIT_QTY IS NULL) AND (FBR_QTY = 0 OR FBR_QTY IS NULL)");
        } catch (DBException e) {
            Logger.e("", e);
        }


    }

    public void setPalletReturnQuantityRight() {
        List<DELIVERY_ITEM> delivery_itemList = new ArrayList<>();
        String query = "(" + DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "') AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " = 0) AND (" + DELIVERY_ITEM.FIELD_HH_CREATED + " = 'X')";
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    DELIVERY_ITEM deliveryItem = (DELIVERY_ITEM) structure;
                    deliveryItem.setRCR_QTY(deliveryItem.getQTY());
                    deliveryItem.setQTY((double) 0);
                    DeliveryController.getInstance().insertOrUpdateDeliveryItem(deliveryItem);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }

    }

    public DELIVERY getDeliveryByNo(String delivery) {
        List<DELIVERY> deliveryList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, DELIVERY_ITEM.FIELD_DELV_NO + " = '" + delivery + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryList.add((DELIVERY) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (deliveryList.size() > 0) {
            return deliveryList.get(0);
        } else {
            return null;
        }
    }


    public enum Quantity {
        FBRBIT,
        RCR,
        UBR,
        PALLETPICK,
        PALLETDROP,
        ALL
    }

    public String currentDeliveryNo;

    public String getCurrentDeliveryNo() {
        return currentDeliveryNo;
    }

    public void setCurrentDeliveryNo(String currentDeliveryNo) {
        this.currentDeliveryNo = currentDeliveryNo;
    }

    public String deliveryNoFromAdapter;

    public String getDeliveryNoFromAdapter() {
        return deliveryNoFromAdapter;
    }

    public void setDeliveryNoFromAdapter(String deliveryNoFromAdapter) {
        this.deliveryNoFromAdapter = deliveryNoFromAdapter;
    }

    public static String EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP = "'" + Constants.ORDER_TYPE.ZFCD + "', '" + Constants.ORDER_TYPE.ZFPD + "', '" + Constants.ORDER_TYPE.ZFRD + "', '" + Constants.ORDER_TYPE.ZFSD + "', '" + Constants.ORDER_TYPE.ZFTD + "', '" + Constants.ORDER_TYPE.ZUBR + "'";
    public static String EXCLUSIONTYPES_FOR_PALLET_PICKUP = "'" + Constants.ORDER_TYPE.ZFCD + "', '" + Constants.ORDER_TYPE.ZFPD + "', '" + Constants.ORDER_TYPE.ZFRD + "', '" + Constants.ORDER_TYPE.ZFSD + "', '" + Constants.ORDER_TYPE.ZFTD + "', '" + Constants.ORDER_TYPE.ZUBR + "'";

    DeliveryController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static DeliveryController getInstance() {
        if (deliveryController == null) {
            deliveryController = new DeliveryController();
        }

        return deliveryController;
    }


    public void deleteHHCreatedDeliveryItems(String deliveryNo) {

        String whrClause = "IS_EMPTY = 'X' AND HH_CREATED = 'X' AND DELV_NO = '" + deliveryNo + "'";
        try {
            ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITEM.TABLE_NAME, whrClause);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }


    public Long getHighestItemNoOfDeliveryItems(String delv_no) {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";

        String query = "SELECT MAX(ITM_NO) FROM DELIVERY_ITEM WHERE SHIP_NO = '" + ship_no + "' AND DELV_NO = '" + delv_no + "'";
        int max = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                max = cursor.getInt(0);
                if (cursor.isLast()) {
                    cursor.close();
                }
            } else {
                cursor.close();
            }
            cursor.close();

        } catch (DBException e) {
            Logger.e("", e);
        }
        return (long) ((int) max + OasisCache.getDELV_ITMNO_INCREMENT());
    }

    public double getPalletInTruckByDelivery() {

        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";

        String palletPickUp = "SELECT SUM(QTY) FROM DELIVERY_ITEM WHERE (DELV_NO IN ( SELECT DELV_NO FROM DELIVERY WHERE (SHIP_NO = '" + ship_no + "') AND (VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + ") AND (ORD_TYPE NOT IN (" + EXCLUSIONTYPES_FOR_PALLET_PICKUP + ")) AND (IS_FBR IS NULL OR IS_FBR = '')) AND (IS_EMPTY = 'X') AND (IS_RETURN = 'X') AND (RCR_QTY IS NULL OR RCR_QTY = 0))";
        String palletDrop = "SELECT SUM(QTY) FROM DELIVERY_ITEM WHERE (DELV_NO IN(SELECT DELV_NO FROM DELIVERY WHERE (SHIP_NO = '" + ship_no + "') AND (VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + ") AND (ORD_TYPE NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (IS_FBR IS NULL OR IS_FBR = '')) AND (IS_EMPTY = 'X') AND (IS_RETURN IS NULL OR IS_RETURN = '') AND (RCR_QTY IS NULL OR RCR_QTY = 0)) AND MAT_NO IN (SELECT MAT_NO FROM MATERIAL_HEADER WHERE ITM_CAT = '" + Constants.ITEM_CATEGORY_PALLET + "')";
        double palletPickUpQty = 0;
        double palletDropQty = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(palletPickUp);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                palletPickUpQty = cursor.getInt(0);
                if (cursor.isLast()) {
                    cursor.close();
                }
            } else {
                cursor.close();
            }
            cursor.close();

        } catch (DBException e) {
            Logger.e("", e);
        }

        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(palletDrop);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                palletDropQty = cursor.getInt(0);
                if (cursor.isLast()) {
                    cursor.close();
                }
            } else {
                cursor.close();
            }
            cursor.close();

        } catch (DBException e) {
            Logger.e("", e);
        }
        double palletAvailable = palletPickUpQty - palletDropQty;
        return palletAvailable;
    }


    public List<String> getDeliveryNos(Long visitNo, Quantity orderType) {
        List<String> delvList = new ArrayList<>();
        String queryStr = "SELECT DELV_NO FROM DELIVERY WHERE VISIT_NO = " + visitNo;
        try {
            Cursor delList = ApplicationManager.getInstance().getDataManager().executeQuery(queryStr);
            if (delList != null && delList.getCount() > 0) {
                while (delList.moveToNext()) {
                    if (!delList.isNull(0)) {
                        delvList.add(delList.getString(0));
                    }
                }
                delList.close();
            }
            if (!delList.isClosed()) {
                delList.close();
            }
        } catch (DBException e) {
            Logger.e("getDeliveryNos()", e);
        }

        return delvList;
    }

    public List<DELIVERY> getOpenDeliveryHeaders(long visitNo, Quantity orderType) {
        List<DELIVERY> deliveryList = new ArrayList<>();
        List<String> delvList = getDeliveryNos(visitNo, Quantity.ALL);
        List<String> completedDelvList = null;
        if (delvList != null) {
            completedDelvList = DBHelper.getInstance().getCompletedInvoicing(delvList);
        }
        String deliveryINStr = "";
        for (String delvNo : delvList) {
            if (completedDelvList != null && !completedDelvList.contains(delvNo)) {
                if (deliveryINStr.isEmpty()) {
                    deliveryINStr = "'" + delvNo + "'";
                } else {
                    deliveryINStr = deliveryINStr + ", '" + delvNo + "'";
                }
            }
        }
        String query = "";
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        if (visitNo == -1) {
            query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "' )";
        } else {
            if (!deliveryINStr.isEmpty()) {
                query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + DELIVERY.FIELD_VISIT_NO + " = " + visitNo + ") AND (DELV_NO IN (" + deliveryINStr + "))";
            } else {
                query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + DELIVERY.FIELD_VISIT_NO + " = " + visitNo + ")";
            }
        }
        switch (orderType) {
            case FBRBIT:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' ) AND (" + DELIVERY.FIELD_HH_CREATED + " IS NULL OR " + DELIVERY.FIELD_HH_CREATED + " = '')";
                break;
            case RCR:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
            case UBR:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " = '" + DBHelper.ORDER_TYPE.ZUBR + "') AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )  AND (" + DELIVERY.FIELD_HH_CREATED + " IS NULL OR " + DELIVERY.FIELD_HH_CREATED + " = '')";
                break;
            case PALLETPICK:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_PALLET_PICKUP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
            case PALLETDROP:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryList.add((DELIVERY) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (deliveryList.size() > 0) {
            return deliveryList;
        } else {
            return null;
        }
    }

    public double getSAPChargesValue(String delvNo) {
        String query = "COND_TYPE_ID = '" + Constants.ZFDD + "' AND DELV_NO = '" + delvNo + "' AND ITM_NO = 0";
        List<DELIVERY_ITM_COND> deliveryItmCondDataTable = DBHelper.getInstance().getDeliveryItemCon(query);

        if (deliveryItmCondDataTable != null && deliveryItmCondDataTable.size() > 0) {
            return deliveryItmCondDataTable.get(0).getVAL();
        }
        return 0;
    }

    public void deleteCurrentVisitDeliveries(VISIT visit) {
        if (visit != null) {
            SHIPMENT_HEADER currentShipment = DBHelper.getInstance().getCurrentShipment();
            String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
            //String ship_no = "11312033";
            List<DELIVERY> deliveryList = getDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), Quantity.ALL);
            if (deliveryList != null && deliveryList.size() > 0) {
                String deliveries = "";
                String invoices = "";
                for (int i = 0; i < deliveryList.size(); i++) {
                    String delvNo = "'" + deliveryList.get(i).getDELV_NO() + "'";
                    if (deliveries.isEmpty() || deliveries == null) {
                        deliveries = delvNo;
                    } else {
                        deliveries = deliveries + "," + delvNo;
                    }

                    if (deliveryList.get(i).getINV_NO() != null && !deliveryList.get(i).getINV_NO().isEmpty()) {
                        String invoiceNo = "'" + deliveryList.get(i).getINV_NO() + "'";
                        if (invoices.isEmpty() || invoices == null) {
                            invoices = invoiceNo;
                        } else {
                            invoices = invoices + "," + invoiceNo;
                        }
                    }
                }
                String delDeliveryItemQuery = "SHIP_NO = '" + ship_no + "' AND HH_CREATED = 'X' AND DELV_NO IN (" + deliveries + ")";
                String delDeliveryHeaderQuery = "SHIP_NO = '" + ship_no + "' AND HH_CREATED = 'X' AND VISIT_NO = '" + visit.getVISIT_NO() + "'";

                try {
                    ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITEM.TABLE_NAME, delDeliveryItemQuery);
                    ApplicationManager.getInstance().getDataManager().delete(DELIVERY.TABLE_NAME, delDeliveryHeaderQuery);
                } catch (DBException e) {
                    Logger.e("", e);
                }

                DBHelper.getInstance().decrementAndUpdateNumRange(Constants.NUMRANGE_DOC_TYPE_DELIVERY);
                if (invoices != null && !invoices.isEmpty()) {
                    String query = "SELECT * FROM INVOICE WHERE SHIP_NO = '" + ship_no + "' AND INV_NO IN (" + invoices + ")";
                    try {
                        Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
                        int count = cursor.getCount();
                        if (count > 0) {
                            String delInvoiceQuery = "SHIP_NO = '" + ship_no + "' AND INV_NO IN (" + invoices + ")";
                            ApplicationManager.getInstance().getDataManager().delete(INVOICE.TABLE_NAME, delInvoiceQuery);
                            DBHelper.getInstance().decrementAndUpdateNumRange(Constants.NUMRANGE_DOC_TYPE_INVOICE);
                        }
                        String deleteInvoiceItem = "SHIP_NO = '" + ship_no + "' AND INV_NO IN (" + invoices + ")";
                        String deleteAttachment = "SHIP_NO = '" + ship_no + "' AND REF_NO IN ('" + visit.getVISIT_NO() + "'," + invoices + ")";
                        ApplicationManager.getInstance().getDataManager().delete(INVOICE_ITEM.TABLE_NAME, deleteInvoiceItem);
                        ApplicationManager.getInstance().getDataManager().delete(ATTACHMENT.TABLE_NAME, deleteAttachment);
                        cursor.close();
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                }
            }
            String deleteVisit = "SHIP_NO = '" + ship_no + "' AND HHT = 'X' AND VISIT_NO = '" + visit.getVISIT_NO() + "'";
            try {
                ApplicationManager.getInstance().getDataManager().delete(VISIT.TABLE_NAME, deleteVisit);
            } catch (DBException e) {
                Logger.e("", e);
            }

            //trailer delete
            List<TRL_PICK_DRP> trailerPickList = TrailerController.getInstance().getPickedOrDroppedTrailersForCustomer(visit.getCUST_NO(), Constants.TRAILERPICK);
            List<TRL_PICK_DRP> trailerDropList = TrailerController.getInstance().getPickedOrDroppedTrailersForCustomer(visit.getCUST_NO(), Constants.TRAILERDROP);

            String trailer1 = currentShipment.getTRL1_P() == null ? "" : currentShipment.getTRL1_P();
            String trailer2 = currentShipment.getTRL2_P() == null ? "" : currentShipment.getTRL2_P();
            if (trailerPickList != null && trailerPickList.size() > 0) {
                //undo pick
                for (TRL_PICK_DRP item : trailerPickList) {
                    if (item.getTRL_TYPE() != null) {
                        if (item.getTRL_TYPE() == 1) {
                            currentShipment.setTRL1_P("");
                        } else if (item.getTRL_TYPE() == 2) {
                            currentShipment.setTRL2_P("");
                        }
                    }
                }
            }

            if (trailerDropList != null && trailerDropList.size() > 0) {
                //undo drop
                for (TRL_PICK_DRP item : trailerDropList) {
                    if (item.getTRL_TYPE() != null) {
                        if (item.getTRL_TYPE() == 1) {
                            currentShipment.setTRL1_P(item.getTRL_NO());
                        } else if (item.getTRL_TYPE() == 2) {
                            currentShipment.setTRL2_P(item.getTRL_NO());
                        }
                    }
                }
            }

            String deleteTrlPickDrop = "SHIP_NO = '" + ship_no + "' AND CUST_NO = '" + visit.getCUST_NO() + "'";
            try {
                ApplicationManager.getInstance().getDataManager().delete(TRL_PICK_DRP.TABLE_NAME, deleteTrlPickDrop);
                DBHelper.getInstance().updateShipmentStatus(currentShipment);
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
    }


    public List<DELIVERY> getDeliveryHeaders(Long visitNo, Quantity orderType) {
        List<DELIVERY> deliveryList = new ArrayList<>();
        String query = "";
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        if (visitNo == -1) {
            query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "' )";
        } else {
            query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + DELIVERY.FIELD_VISIT_NO + " = " + visitNo + ")";
        }

        switch (orderType) {
            case FBRBIT:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' ) AND (" + DELIVERY.FIELD_HH_CREATED + " IS NULL OR " + DELIVERY.FIELD_HH_CREATED + " = '')";
                break;
            case RCR:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
            case UBR:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " = '" + DBHelper.ORDER_TYPE.ZUBR + "') AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )  AND (" + DELIVERY.FIELD_HH_CREATED + " IS NULL OR " + DELIVERY.FIELD_HH_CREATED + " = '')";
                break;
            case PALLETPICK:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_PALLET_PICKUP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
            case PALLETDROP:
                query = query + " AND (" + DELIVERY.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '' )";
                break;
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryList.add((DELIVERY) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (deliveryList.size() > 0) {
            return deliveryList;
        } else {
            return null;
        }
    }

    public String getFirstReturnsCollectionOrderDeliveryNo() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT MAT_NO, DELV_NO FROM DELIVERY_ITEM WHERE (SHIP_NO = '" + ship_no + "') AND (DELV_NO IN(SELECT DELV_NO FROM DELIVERY WHERE (SHIP_NO = '" + ship_no + "') AND (VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + ") AND (ORD_TYPE = '" + DBHelper.ORDER_TYPE.ZSD + "') AND (HH_CREATED IS NULL OR HH_CREATED = '')))";
        List<String> deliveries = new ArrayList<>();
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    String mat_no = cursor.getString(0);
                    if (MaterialController.getInstance().getDummyMaterialNos() != null && Arrays.asList(MaterialController.getInstance().getDummyMaterialNos()).contains(mat_no)) {
                        if (!deliveries.contains(cursor.getString(1))) {
                            deliveries.add(cursor.getString(1));
                        }
                    }
                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }

        if (deliveries.size() > 0) {
            Collections.sort(deliveries, new Comparator<String>() {
                @Override
                public int compare(String lhs, String rhs) {
                    return lhs.compareTo(rhs);
                }
            });
            return deliveries.get(0);
        } else {
            return null;
        }
    }

    public List<DELIVERY> getOpenFullFBRDeliveryHeaders(long visit_no) {

        List<DELIVERY> deliveryList = new ArrayList<>();
        List<String> delvList = getDeliveryNos(visit_no, Quantity.ALL);
        List<String> completedDelvList = DBHelper.getInstance().getCompletedInvoicing(delvList);
        String deliveryINStr = "";
        for (String delvNo : delvList) {
            if (!completedDelvList.contains(delvNo)) {
                if (deliveryINStr == null || deliveryINStr.isEmpty()) {
                    deliveryINStr = "'" + delvNo + "'";
                } else {
                    deliveryINStr = deliveryINStr + ", '" + delvNo + "'";
                }
            }
        }
        String query = "";
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        if (visit_no == -1) {
            query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "' )";
        } else {
            if (deliveryINStr != null && !deliveryINStr.isEmpty())
                query = " (" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + DELIVERY.FIELD_VISIT_NO + " = " + visit_no + ") AND (" + DELIVERY.FIELD_ORD_TYPE + " <> '" + Constants.ORDER_TYPE.ZUBR + "') AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '') AND (DELV_NO IN (" + deliveryINStr + "))";
            else
                query = " (" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "') AND (" + DELIVERY.FIELD_VISIT_NO + " = " + visit_no + ") AND (" + DELIVERY.FIELD_ORD_TYPE + " <> '" + Constants.ORDER_TYPE.ZUBR + "') AND (" + DELIVERY.FIELD_IS_FBR + " IS NULL OR " + DELIVERY.FIELD_IS_FBR + " = '')";
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryList.add((DELIVERY) structure);
                }
            }

            // fix: https://support.unvired.com/issues/1979
            // Logic to remove info collection ie delivery with dummy material : start
            // select delivery items of info collection type
            List<String> deliveries = new ArrayList<>();
            for (DELIVERY delv : deliveryList) {
//          LOOK FOR DELIVERY ITEMS WHICH ARE NOT COPIED THROUGH SPLIT BILL PROCESS.
                String queryForDummy = "SELECT MAT_NO, DELV_NO FROM DELIVERY_ITEM WHERE DELV_NO = '" + delv.getDELV_NO() + "' AND PROMO_TYPE_DESC != 'OSB'";
                try {
                    Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(queryForDummy);
                    // INFO COLLECTION DELIVERY WE EXPECT ONLY ONE ITEM (DUMMY MATERIAL)
                    if (cursor != null && cursor.getCount() == 1) {
                        while (cursor.moveToNext()) {
                            String mat_no = cursor.getString(0);
                            if (MaterialController.getInstance().getDummyMaterialNos() != null && Arrays.asList(MaterialController.getInstance().getDummyMaterialNos()).contains(mat_no)) {
                                if (!deliveries.contains(cursor.getString(1))) {
                                    deliveries.add(cursor.getString(1));
                                }
                            } else {

                            }
                            if (cursor.isLast()) {
                                cursor.close();
                            }
                        }
                    } else {
                        if (!cursor.isClosed()) {
                            cursor.close();
                        }
                    }
                } catch (DBException e) {
                    Logger.e("", e);
                }

                // sort based delivery that have info collection items


            }
            if (deliveries != null && deliveries.size() > 0) {
                for (int x = (deliveries.size() - 1); x >= 0; x--) {
                    for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                        if (deliveryList.get(i).getDELV_NO().equals(deliveries.get(x))) {
                            deliveryList.remove(i);
                        }
                    }
                }
            }

            // Logic to remove info collection ie delivery with dummy material : end


        } catch (DBException e) {
            Logger.e("", e);
        }

        if (deliveryList.size() > 0) {
            return deliveryList;
        }
        return null;
    }

    public List<DELIVERY_ITEM> getDelvItems() {
        List<DELIVERY_ITEM> deliveryItemList = new ArrayList<>();
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + ship_no + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryItemList.add((DELIVERY_ITEM) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (deliveryItemList.size() > 0) {
            return deliveryItemList;
        } else {
            return null;
        }
    }

    public List<DELIVERY_ITEM> getDeliveryItems(String deliveryNo, Quantity orderType, String matNo) {
        if (matNo != null && orderType == Quantity.PALLETPICK) {
            List<DELIVERY_ITEM> deliveryItemList = new ArrayList<>();
            String query = "(" + DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "') AND (" + DELIVERY_ITEM.FIELD_DELV_NO + " = '" + deliveryNo + "')";
            query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_MAT_NO + " = '" + matNo + "')";
            try {
                IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, query);
                if (structures != null && structures.length > 0) {
                    for (IDataStructure structure : structures) {
                        deliveryItemList.add((DELIVERY_ITEM) structure);
                    }
                }
            } catch (DBException e) {
                Logger.e("", e);
                return null;
            }

            if (deliveryItemList.size() > 0) {
                return deliveryItemList;
            } else {
                return null;
            }
        } else {
            return getDeliveryItem(deliveryNo, orderType);
        }
    }

    public List<DELIVERY_ITEM> getDeliveryItems(String deliveryNo, Quantity orderType) {
        List<DELIVERY_ITEM> deliveryItemList = new ArrayList<>();
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "(" + DELIVERY.FIELD_SHIP_NO + " = '" + ship_no + "') AND (" + DELIVERY.FIELD_DELV_NO + " = '" + deliveryNo + "')";
        switch (orderType) {
            case FBRBIT:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' )";
                //query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' ) AND (" + deliveryItemDataTable.IS_EMPTYColumn + " IS NULL OR " + deliveryItemDataTable.IS_EMPTYColumn + " = '' )";
                break;
            case RCR:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NOT NULL AND " + DELIVERY_ITEM.FIELD_RCR_QTY + " > 0)";
                break;
            case UBR:
                //same as FBR/BIT
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_EMPTY + " = '')";
                break;
            case PALLETPICK:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " = 0)";
                break;
            case PALLETDROP:
                query = query + " AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '' ) AND (" + DELIVERY_ITEM.FIELD_IS_EMPTY + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " = 0)";
                break;
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryItemList.add((DELIVERY_ITEM) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (deliveryItemList.size() > 0) {
            Collections.sort(deliveryItemList, new Comparator<DELIVERY_ITEM>() {
                @Override
                public int compare(DELIVERY_ITEM lhs, DELIVERY_ITEM rhs) {
                    return lhs.getMAT_NO().compareTo(rhs.getMAT_NO());
                }
            });
            return deliveryItemList;
        } else {
            return null;
        }

    }

    public void updateDeliveryItemsForFullFBR(String deliveryNo, String reason) {
        List<DELIVERY_ITEM> deliveryItemList = getDeliveryItems(deliveryNo, Quantity.FBRBIT);
        if (deliveryItemList == null || deliveryItemList.size() <= 0) {
            return;
        } else {
            for (DELIVERY_ITEM delivery_item : deliveryItemList) {
                if (delivery_item.getQTY() == 0 || delivery_item.getQTY() == null) {
                    delivery_item.setFBR_QTY((double) 0);
                } else {
                    delivery_item.setFBR_QTY(delivery_item.getQTY());
                }
                delivery_item.setBIT_QTY((double) 0);
                delivery_item.setFBR_RSN(reason);
                insertOrUpdateDeliveryItem(delivery_item);
            }
        }
    }

    public List<DELIVERY> getOpenDeliveryHeaders(Long visitNo, Quantity orderType) throws DBException {
        List<DELIVERY> deliveryHeasers = new ArrayList<>();
        List<String> delvList = getDeliveryNos(visitNo, Quantity.ALL);
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();

        List<String> completedDelvList = InvoiceController.getInstance().getCompletedInvoicing(delvList);

        String deliveryINStr = "";
        for (String delvNo : delvList) {
            if (!completedDelvList.contains(delvNo)) {
                if (deliveryINStr == null || (deliveryINStr).isEmpty())
                    deliveryINStr = "'" + delvNo + "'";
                else
                    deliveryINStr = deliveryINStr + ", '" + delvNo + "'";
            }
        }

        DELIVERY deliveryHeaderDataTable = null;
        try {
            deliveryHeaderDataTable = new DELIVERY();
        } catch (DBException e) {
            Logger.e("", e);
        }
        String query = "";
        if (visitNo == -1) {
            query = " (" + deliveryHeaderDataTable.FIELD_SHIP_NO + " ='" + shipRow.getSHIP_NO() + "' )";
        } else {
            if ((deliveryINStr != null && !deliveryINStr.isEmpty()))
                query = " (" + deliveryHeaderDataTable.FIELD_SHIP_NO + " ='" + shipRow.getSHIP_NO() + "') AND (" + deliveryHeaderDataTable.FIELD_VISIT_NO + " = " + visitNo + ") AND (DELV_NO IN (" + deliveryINStr + "))";
            else
                query = " (" + deliveryHeaderDataTable.FIELD_SHIP_NO + " ='" + shipRow.getSHIP_NO() + "') AND (" + deliveryHeaderDataTable.FIELD_VISIT_NO + " = " + visitNo + ")";
        }

        switch (orderType) {
            case FBRBIT:
                query = query + " AND (" + deliveryHeaderDataTable.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + deliveryHeaderDataTable.FIELD_IS_FBR + " IS NULL OR " + deliveryHeaderDataTable.FIELD_IS_FBR + " = '' ) AND (" + deliveryHeaderDataTable.FIELD_HH_CREATED + " IS NULL OR " + deliveryHeaderDataTable.FIELD_HH_CREATED + " = '')";
                break;
            case RCR:
                query = query + " AND (" + deliveryHeaderDataTable.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + deliveryHeaderDataTable.FIELD_IS_FBR + " IS NULL OR " + deliveryHeaderDataTable.FIELD_IS_FBR + " = '' )";
                break;
            case UBR:
                query = query + " AND (" + deliveryHeaderDataTable.FIELD_ORD_TYPE + " = '" + Constants.ORDER_TYPE.ZUBR + "') AND (" + deliveryHeaderDataTable.FIELD_IS_FBR + " IS NULL OR " + deliveryHeaderDataTable.FIELD_IS_FBR + " = '' )  AND (" + deliveryHeaderDataTable.FIELD_HH_CREATED + " IS NULL OR " + deliveryHeaderDataTable.FIELD_HH_CREATED + " = '')";
                break;
            case PALLETPICK:
                query = query + " AND (" + deliveryHeaderDataTable.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_PALLET_PICKUP + ")) AND (" + deliveryHeaderDataTable.FIELD_IS_FBR + " IS NULL OR " + deliveryHeaderDataTable.FIELD_IS_FBR + " = '' )";
                break;
            case PALLETDROP:
                query = query + " AND (" + deliveryHeaderDataTable.FIELD_ORD_TYPE + " NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")) AND (" + deliveryHeaderDataTable.FIELD_IS_FBR + " IS NULL OR " + deliveryHeaderDataTable.FIELD_IS_FBR + " = '' )";
                break;
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, query);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryHeasers.add((DELIVERY) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return deliveryHeasers;
    }


    public DELIVERY createAndSaveDeliveryRow() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        try {
            DELIVERY delivery = new DELIVERY();
            delivery.setLid(delivery.getLid());
            delivery.setHH_CREATED("X");
            delivery.setVISIT_NO(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());
            delivery.setSHIP_NO(ship_no);
            delivery.setFid(DBHelper.getInstance().getCurrentShipment().getLid());
            delivery.setDELV_NO(DBHelper.getInstance().incrementAndUpdateNumRange(Constants.NUMRANGE_DOC_TYPE_DELIVERY));
            delivery.setORD_TYPE(DBHelper.ORDER_TYPE.ZSD.toString());
            DeliveryController.getInstance().insertDelivery(delivery);
            return delivery;
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

    }

    public void insertDelivery(DELIVERY delivery) {
        try {
            ApplicationManager.getInstance().getDataManager().insert(delivery);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void updateDelivery(DELIVERY delivery) {
        try {
            ApplicationManager.getInstance().getDataManager().update(delivery);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void insertOrUpdateDeliveryItem(DELIVERY_ITEM deliveryItem) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(deliveryItem);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }


    public boolean isFullFBREnabled() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT COUNT(*) FROM DELIVERY WHERE SHIP_NO = '" + ship_no + "' AND VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + " AND ORD_TYPE <> '" + DBHelper.ORDER_TYPE.ZUBR + "' AND (IS_FBR IS NULL OR IS_FBR = '') AND (HH_CREATED IS NULL OR HH_CREATED = '')";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            cursor.moveToFirst();
            int count = Integer.parseInt(cursor.getString(cursor.getColumnIndex("COUNT(*)")));
            cursor.close();
            if (count > 0) {
                return true;
            } else {
                return false;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return false;
        }
    }

    public boolean isRCREnabled() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT COUNT(*) FROM DELIVERY WHERE SHIP_NO = '" + ship_no + "' AND VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + " AND (IS_FBR IS NULL OR IS_FBR = '') AND ORD_TYPE NOT IN (" + EXCLUSIONTYPES_FOR_ZSD_AND_PALLETDROP + ")";

        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            cursor.moveToFirst();
            int count = Integer.parseInt(cursor.getString(cursor.getColumnIndex("COUNT(*)")));
            cursor.close();

            // If no deliveries are available, return false
            if (count <= 0) {
                return false;
            }

            // Check if customer has OSB2INVENABLED attribute
            CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(
                    TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());
            boolean osb2InvEnabled = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.OSB2INVENABLED);

            // For OSB2INVENABLED customers, check if there's a Returns Collection Delivery
            if (osb2InvEnabled) {
                String emptyOrdDelvNo = getFirstReturnsCollectionOrderDeliveryNo();
                if (emptyOrdDelvNo == null || emptyOrdDelvNo.isEmpty()) {
                    // No Returns Collection Delivery available, disable RCR
                    return false;
                }
            }

            // All checks passed, enable RCR
            return true;

        } catch (DBException e) {
            Logger.e("", e);
            return false;
        }
    }

    public boolean isUBREnabled() {
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        String query = "SELECT COUNT(*) FROM DELIVERY WHERE SHIP_NO = '" + ship_no + "' AND VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + " AND ORD_TYPE = '" + DBHelper.ORDER_TYPE.ZUBR + "'";

        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            cursor.moveToFirst();
            int count = Integer.parseInt(cursor.getString(cursor.getColumnIndex("COUNT(*)")));
            cursor.close();
            if (count > 0) {
                return true;
            } else {
                return false;
            }
        } catch (DBException e) {
            Logger.e("", e);
            return false;
        }
    }

    public int getCurrentVisitCount(SHIPMENT_HEADER shipment_header) {
        int res = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT COUNT(*) FROM VISIT WHERE SHIP_NO = '" + shipment_header.getSHIP_NO() + "'");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    res = Integer.parseInt(cursor.getString(0));
                    cursor.close();
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return res;
    }

    public int getEmptyStockInTruck(String delv_no) {
        String query = "SELECT SUM(DELIVERY_ITEM.RCR_QTY) AS RCR_QTY, SUM(DELIVERY_ITEM.UBR_QTY) AS UBR_QTY, SUM(DELIVERY_ITEM.FBR_QTY) AS FBR_QTY, SUM(DELIVERY_ITEM.BIT_QTY) AS BIT_QTY FROM  DELIVERY_ITEM WHERE DELV_NO <> '" + delv_no + "'";
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                int column1 = 0;
                int column2 = 0;
                int column3 = 0;
                int column4 = 0;
                while (cursor.moveToNext()) {

                    column1 = cursor.getString(cursor.getColumnIndex("RCR_QTY")) == null || cursor.getString(cursor.getColumnIndex("RCR_QTY")).isEmpty() ? 0 : (int) cursor.getDouble(cursor.getColumnIndex("RCR_QTY"));
                    column2 = cursor.getString(cursor.getColumnIndex("UBR_QTY")) == null || cursor.getString(cursor.getColumnIndex("UBR_QTY")).isEmpty() ? 0 : (int) cursor.getDouble(cursor.getColumnIndex("UBR_QTY"));
                    column3 = cursor.getString(cursor.getColumnIndex("FBR_QTY")) == null || cursor.getString(cursor.getColumnIndex("FBR_QTY")).isEmpty() ? 0 : (int) cursor.getDouble(cursor.getColumnIndex("FBR_QTY"));
                    column4 = cursor.getString(cursor.getColumnIndex("BIT_QTY")) == null || cursor.getString(cursor.getColumnIndex("BIT_QTY")).isEmpty() ? 0 : (int) cursor.getDouble(cursor.getColumnIndex("BIT_QTY"));

                    if (cursor.isLast()) {
                        cursor.close();
                    }
                }
                return column1 + column2 + column3 + column4;
            } else {
                if (!cursor.isClosed()) {
                    cursor.close();
                }

            }
        } catch (DBException e) {
            Logger.e("", e);
        }

        return 0;
    }


    public void clearAllQuantities(List<DELIVERY> deliveryList, Quantity quantity) {
        if (deliveryList != null && deliveryList.size() > 0) {
            String deliveryNos = "";
            for (DELIVERY delivery : deliveryList) {
                if (deliveryNos.isEmpty() || deliveryNos.equals("")) {
                    deliveryNos = "'" + delivery.getDELV_NO() + "'";
                } else {
                    deliveryNos = deliveryNos + "," + "'" + delivery.getDELV_NO() + "'";
                }
            }
            if (!deliveryNos.equals("") && !deliveryNos.isEmpty()) {
                if (quantity.equals(Quantity.RCR)) {

                    // String query = "DELETE FROM DELIVERY_ITEM WHERE DELV_NO IN (" + deliveryNos + ") AND IS_RETURN = 'X' AND IS_EMPTY = 'X' AND HH_CREATED = 'X'";
                    String query = "DELV_NO IN (" + deliveryNos + ") AND IS_RETURN = 'X' AND IS_EMPTY = 'X' AND HH_CREATED = 'X'";
//
//                    try {
//                        Cursor cursor = iDataManager.executeQuery(query);
//                        if(!cursor.isClosed()){
//                            cursor.close();
//                        }
//                    } catch (DBException e) {
//                        Logger.e("", e);
//                    }
                    try {
                        ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITEM.TABLE_NAME, query);
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                }
                String query1 = "";
                switch (quantity) {
                    case FBRBIT:
                        query1 = "(" + DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getCurrentShipment().getSHIP_NO() + "') AND (" + DELIVERY_ITEM.FIELD_DELV_NO + " IN(" + deliveryNos + ")) AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '') AND (" + DELIVERY_ITEM.FIELD_FBR_QTY + " IS NOT NULL OR " + DELIVERY_ITEM.FIELD_FBR_QTY + " <> '' OR " + DELIVERY_ITEM.FIELD_FBR_QTY + " <> 0) AND (" + DELIVERY_ITEM.FIELD_BIT_QTY + " IS NOT NULL OR " + DELIVERY_ITEM.FIELD_BIT_QTY + " <> '' OR " + DELIVERY_ITEM.FIELD_BIT_QTY + " <> 0)";
                        break;
                    case RCR:
                        query1 = "(" + DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getCurrentShipment().getSHIP_NO() + "') AND (" + DELIVERY_ITEM.FIELD_DELV_NO + " IN(" + deliveryNos + ")) AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + " = 'X') AND (" + DELIVERY_ITEM.FIELD_RCR_QTY + " IS NOT NULL OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " <> '' OR " + DELIVERY_ITEM.FIELD_RCR_QTY + " <> 0)";
                        break;
                    case UBR:
                        query1 = "(" + DELIVERY_ITEM.FIELD_SHIP_NO + " = '" + DBHelper.getInstance().getCurrentShipment().getSHIP_NO() + "') AND (" + DELIVERY_ITEM.FIELD_DELV_NO + " IN(" + deliveryNos + ")) AND (" + DELIVERY_ITEM.FIELD_IS_RETURN + "IS NULL OR " + DELIVERY_ITEM.FIELD_IS_RETURN + " = '') AND (" + DELIVERY_ITEM.FIELD_UBR_QTY + " IS NOT NULL OR " + DELIVERY_ITEM.FIELD_UBR_QTY + " <> '' OR " + DELIVERY_ITEM.FIELD_UBR_QTY + " <> 0)";
                        break;
                }

                List<DELIVERY_ITEM> delivery_itemList = new ArrayList<>();
                try {
                    IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, query1);
                    if (structures != null && structures.length > 0) {
                        for (IDataStructure structure : structures) {
                            delivery_itemList.add((DELIVERY_ITEM) structure);
                        }

                        if (delivery_itemList.size() > 0) {
                            for (DELIVERY_ITEM delivery_item : delivery_itemList) {
                                switch (quantity) {
                                    case FBRBIT:
                                        if (delivery_item.getFBR_QTY() != null) {
                                            delivery_item.setFBR_QTY((double) 0);
                                        }
                                        if (delivery_item.getFBR_RSN() != null) {
                                            delivery_item.setFBR_RSN("");
                                        }
                                        if (delivery_item.getFBR_RSN_POS() != null) {
                                            delivery_item.setFBR_RSN_POS((long) 0);
                                        }
                                        if (delivery_item.getBIT_QTY() != null) {
                                            delivery_item.setBIT_QTY((double) 0);
                                        }
                                        if (delivery_item.getBIT_RSN() != null) {
                                            delivery_item.setBIT_RSN("");
                                        }
                                        break;

                                    case RCR:
                                        if (delivery_item.getRCR_QTY() != null) {
                                            delivery_item.setRCR_QTY((double) 0);
                                        }
                                        break;

                                    case UBR:
                                        if (delivery_item.getUBR_QTY() != null) {
                                            delivery_item.setUBR_QTY((double) 0);
                                        }
                                        break;
                                }
                            }

                            for (DELIVERY_ITEM deliveryItem : delivery_itemList) {
                                insertOrUpdateDeliveryItem(deliveryItem);
                            }
                        }
                    }
                } catch (DBException e) {
                    Logger.e("", e);
                }


            }
        }
    }


    public void deleteHHCreatedEmptyItemsIfExist(Quantity type) {
        String whrClause = "";
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //String ship_no = "11312033";
        switch (type) {
            case RCR:
                //whrClause = "WHERE IS_RETURN = 'X' AND IS_EMPTY = 'X' AND HH_CREATED = 'X' AND (RCR_QTY IS NULL OR RCR_QTY <= 0) AND (QTY IS NULL OR QTY <= 0)";
                //break;
            case PALLETPICK:
                whrClause = "SHIP_NO = '" + ship_no + "'AND IS_RETURN = 'X' AND IS_EMPTY = 'X' AND HH_CREATED = 'X' AND (RCR_QTY IS NULL OR RCR_QTY <= 0) AND (QTY IS NULL OR QTY <= 0)";
                break;
            case PALLETDROP:
                whrClause = "SHIP_NO = '" + ship_no + "'AND (IS_RETURN IS NULL OR IS_RETURN = '') AND IS_EMPTY = 'X' AND HH_CREATED = 'X' AND (RCR_QTY IS NULL OR RCR_QTY <= 0) AND (QTY IS NULL OR QTY <= 0)";
                break;
        }

        try {
            ApplicationManager.getInstance().getDataManager().delete(DELIVERY_ITEM.TABLE_NAME, whrClause);
        } catch (DBException e) {
            Logger.e("", e);
        }

    }

    public List<DELIVERY_ITEM> getDeliveryItem(String delv_no, Quantity orderType) {

        return DBHelper.getInstance().getDeliveryItems(delv_no, orderType);
    }

    public static void updateDelivery(List<DELIVERY> deliveryDataTable) {
        try {
            DBHelper.getInstance().updateDelivery(deliveryDataTable);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static Double getMWSTDeliveryItemConditionRate() {
        String query = "COND_TYPE_ID = '" + Constants.MWST + "'";
        List<DELIVERY_ITM_COND> deliveryItmCondDataTable = DBHelper.getInstance().getDeliveryItemCon(query);

        if (deliveryItmCondDataTable != null && deliveryItmCondDataTable.size() > 0)
            return (deliveryItmCondDataTable.get(0).getRATE());

        return 0d;
    }

    public List<DELIVERY_ITM_COND> getDeliveryItmConditions(String delv_no) {

        String query = "";
        if (Utils.isNullOrEmpty(delv_no)) {
            query = DELIVERY_ITM_COND.FIELD_SHIP_NO + " ='" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "'";
        } else {
            query = DELIVERY_ITM_COND.FIELD_SHIP_NO + " ='" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND " + DELIVERY_ITM_COND.FIELD_DELV_NO + " = '" + delv_no + "'";
        }

        List<DELIVERY_ITM_COND> deliveryItmCondList = DBHelper.getInstance().getDeliveryItemCon(query);
        return deliveryItmCondList;
    }

    public void updateVisit(VISIT v) {
        try {
            ApplicationManager.getInstance().getDataManager().update(v);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public boolean isProFormaMandatory() throws DBException {
        CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());
        return Utils.isAttributeEnabled(customer_header, Constants.AttributeType.PROFORMAENABLED);
    }

    public boolean isPODClaimScanEnabled() throws DBException {
        CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());
        return Utils.isAttributeEnabled(customer_header, Constants.AttributeType.PODCLAIMENABLED);
    }

    public boolean enableEndVisitButton() {
        String query = "SELECT COUNT(*) FROM DELIVERY WHERE VISIT_NO = " + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + " AND ORD_TYPE <> 'ZUBR' AND IS_FBR <> 'X' AND BLANK_TYPE <> 'X'";
        int unScanedDelvCount = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(query);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                unScanedDelvCount = cursor.getInt(0);
                cursor.close();
                return unScanedDelvCount > 0;

            } else {
                cursor.close();
            }
            cursor.close();

        } catch (DBException e) {
            Logger.e("", e);
        }
        return false;
    }

}
