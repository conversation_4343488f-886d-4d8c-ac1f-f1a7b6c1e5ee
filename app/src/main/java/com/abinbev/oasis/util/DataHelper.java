package com.abinbev.oasis.util;

import android.database.Cursor;
import android.graphics.Bitmap;
import android.util.Log;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.DELIVERY_ITM_COND;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TRIP_INSP;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Hashtable;
import java.util.List;

public class DataHelper {
    public TRIP_INSP tripInspectionHeader;
    public static DataHelper dataHelper;
    public String authManagerId, authManagerName = "";
    public SHIPMENT_HEADER selectedShipmentHeader = null;

    public static DataHelper getInstance() {
        if (dataHelper == null) {
            dataHelper = new DataHelper();
        }

        return dataHelper;
    }

    public void setTripInspectionToUpdate(TRIP_INSP tripInspHeader) {
        tripInspectionHeader = tripInspHeader;
    }

    public TRIP_INSP getTripInspectionToUpdate() {
        return tripInspectionHeader;
    }

    public void setAuthDetails(String AUTH_MGR_ID, String AUTH_MGR_NAME) {
        authManagerId = AUTH_MGR_ID;
        authManagerName = AUTH_MGR_NAME;
    }

    public String getAuthManagerID() {
        return authManagerId;
    }

    public String getAuthManagerName() {
        return authManagerName;
    }

    public void setShipmentHeader(SHIPMENT_HEADER shipmentHeader) {
        this.selectedShipmentHeader = shipmentHeader;

    }

    public SHIPMENT_HEADER getShipmentHeader() {
        return selectedShipmentHeader;
    }

    public void storeSignature(Bitmap sign, int attachmentType, String authManagerId) {

        String hexValue = ImageUtil.getBase64FromBitmap(sign);
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        ATTACHMENT attachmentRow = null;
        attachmentRow = AttachmentHelper.getInstance().getAttachment(attachmentType, shipRow.getSHIP_NO(), authManagerId, Constants.IMAGE_TYPE);

        if (attachmentRow == null)
        {
            try {
                attachmentRow = new ATTACHMENT();
                attachmentRow.setDATA(hexValue);
                attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE);
                attachmentRow.setREF_NO(shipRow.getSHIP_NO());
                attachmentRow.setSHIP_NO(shipRow.getSHIP_NO());
                attachmentRow.setSIGNED_BY(authManagerId.isEmpty() ? "000000" : authManagerId);
                attachmentRow.setTYPE((long) attachmentType);
                attachmentRow.setFid(shipRow.getLid());
                DBHelper.getInstance().addAttachment(attachmentRow);
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
        else
        {
            attachmentRow.setDATA(hexValue);
            DBHelper.getInstance().updateAttachment(attachmentRow);
        }

    }

    public void storeCustomerSignature(Bitmap sign) {

        String hexValue = ImageUtil.getBase64FromBitmap(sign);
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        ATTACHMENT attachmentRow = null;
        attachmentRow = AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_INVOICE, visit.getVISIT_NO().toString() + DeliveryController.getInstance().getCurrentDeliveryNo().toString(), visit.getCUST_NO(), Constants.IMAGE_TYPE);

        if (attachmentRow == null)
        {
            try {
                attachmentRow = new ATTACHMENT();
                attachmentRow.setDATA(hexValue);
                attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE);
                attachmentRow.setREF_NO(visit.getVISIT_NO().toString() + DeliveryController.getInstance().getCurrentDeliveryNo().toString());
                attachmentRow.setSHIP_NO(shipRow.getSHIP_NO());
                attachmentRow.setSIGNED_BY((visit.getCUST_NO() == null || visit.getCUST_NO().isEmpty()) ? "" : visit.getCUST_NO());
                attachmentRow.setTYPE((long)Constants.ATTACH_TYPE_INVOICE);
                attachmentRow.setFid(shipRow.getLid());
                DBHelper.getInstance().updateAttachment(attachmentRow);
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
        else
        {
            attachmentRow.setDATA(hexValue);
            DBHelper.getInstance().updateAttachment(attachmentRow);
        }

    }

    public void OSB_addDeliveryItemsOnEmptyCollectionDelivery() {

            //1. Read all Visits and see who has OSB attribute
        Cursor sqlResultSet = null;
        try {
            sqlResultSet = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT v.VISIT_NO, v.CUST_NO, d.DELV_NO FROM VISIT AS v INNER JOIN CUSTOMER_HEADER AS c ON v.CUST_NO = c.CUST_NO INNER JOIN DELIVERY AS d ON v.VISIT_NO = d.VISIT_NO WHERE (c.ATTRIBUTES LIKE 'X,%' AND (d.IS_FBR IS NULL OR d.IS_FBR = '') AND d.ORD_TYPE = 'ZSD')");
        } catch (DBException e) {
            Logger.e("", e);
        }
        Hashtable<Integer, List<VisitDeliveryPojo>> VisitDeliveryHash = new <Integer, List<VisitDeliveryPojo>>Hashtable();
        if(sqlResultSet != null) {
            try
            {

                    while (!sqlResultSet.isLast())
                    {
                        Object temp = sqlResultSet.moveToNext();
                        VisitDeliveryPojo pojo = new VisitDeliveryPojo();
                        int ordinal = sqlResultSet.getColumnIndex("VISIT_NO");
                        if (ordinal > -1) {
                            int visitNo = (sqlResultSet.isNull(ordinal)) ? 0 : sqlResultSet.getInt(ordinal);
                            pojo.setVisitno(visitNo);
                        }

                        ordinal = sqlResultSet.getColumnIndex("DELV_NO");
                        if (ordinal > -1) {
                            String delvno = (sqlResultSet.getString(ordinal).isEmpty()) ? "" : sqlResultSet.getString(ordinal);
                            pojo.setDelvno(delvno);
                        }

                        if(!pojo.getDelvno().isEmpty()) {
                            pojo.setEmptyColDelv(IsEmptyCollectionDelivery(pojo.getDelvno()));
                        }

                        if (!VisitDeliveryHash.containsKey(pojo.getVisitno()))
                        {
                            List<VisitDeliveryPojo> deliveryList = new ArrayList<>();
                            deliveryList.add(pojo);
                            VisitDeliveryHash.put(pojo.getVisitno(), deliveryList);
                        }
                        else
                        {
                            ((List<VisitDeliveryPojo>)VisitDeliveryHash.get(pojo.getVisitno())).add(pojo);
                        }
                    }

            }
            catch (Exception e)
            {
            }
            finally
            {
                if(sqlResultSet != null )
                    sqlResultSet.close();
            }


            //2. Copy the full materials from OSB customers delivery to Empty Collection Delivery
            for ( Integer key : VisitDeliveryHash.keySet()) {
                List<VisitDeliveryPojo> pojoList = VisitDeliveryHash.get(key);
                List<VisitDeliveryPojo> nonEmptyCollectionOrders = new ArrayList<>();
                for(int i = 0; i < pojoList.size(); i++) {
                    if(!pojoList.get(i).getEmptyColDelv()) {
                        nonEmptyCollectionOrders.add(pojoList.get(i));
                    }
                }

                List<VisitDeliveryPojo> emptyCollectionOrders = new ArrayList<>();
                for(int i = 0; i < pojoList.size(); i++) {
                    if(pojoList.get(i).getEmptyColDelv()) {
                        emptyCollectionOrders.add(pojoList.get(i));
                    }
                }

                VisitDeliveryPojo emptyCollectionOrder = null;
                if (emptyCollectionOrders != null && emptyCollectionOrders.size()>0)
                {
                    Collections.sort(emptyCollectionOrders, new Comparator<VisitDeliveryPojo>() {
                        @Override
                        public int compare(VisitDeliveryPojo lhs, VisitDeliveryPojo rhs) {
                            return lhs.getDelvno().compareTo(rhs.getDelvno());
                        }
                    });
                    emptyCollectionOrder = emptyCollectionOrders.get(0);
                }

                //If there are the required deliveries present only proceed
                if (nonEmptyCollectionOrders != null && nonEmptyCollectionOrders.size() > 0 && emptyCollectionOrder != null)
                {
                    String deliveryINStr = "";
                    for (VisitDeliveryPojo pojo : nonEmptyCollectionOrders) {

                        if (deliveryINStr.isEmpty())
                            deliveryINStr = "'" + pojo.getDelvno() + "'";
                        else
                            deliveryINStr = deliveryINStr + ", '" + pojo.getDelvno() + "'";
                    }

                    //DELIVERY_ITEM Records
                    //------------------------------------
                    List<DELIVERY_ITEM> deliveryItems = new ArrayList<>();
                    try {
                        IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITEM.TABLE_NAME, DELIVERY_ITEM.FIELD_DELV_NO + " IN (" + deliveryINStr + ")");
                        if (structures != null && structures.length > 0) {
                            for (IDataStructure structure : structures) {
                                deliveryItems.add((DELIVERY_ITEM) structure);
                            }
                        }
                    } catch (DBException e) {
                        Logger.e("", e);
                    }

                    //New Delivery Item to be added into Empty Collection Delivery
                    int nextItemNo = getHighestItemNoOfDeliveryItems(emptyCollectionOrder.getDelvno());
//                    oasisDataSet.DELIVERY_ITEMDataTable newDeliveryItemDataTable = new oasisDataSet.DELIVERY_ITEMDataTable();
                    for (DELIVERY_ITEM deliveryItem : deliveryItems) {
                        //Persistence.Oasis.oasisDataSet.DELIVERY_ITEMRow deliveryItemRow = newDeliveryItemDataTable.Where(del => del.MAT_NO.Equals(deliveryItem.MAT_NO)).First();
                        //if (deliveryItemRow == null)
                        //{
                        //    deliveryItemRow = newDeliveryItemDataTable.NewDELIVERY_ITEMRow();
                        //}
                        {
                            try {
                                DELIVERY_ITEM deliveryItemRow = new DELIVERY_ITEM();
                                deliveryItemRow.setFid(deliveryItem.getFid());
                                deliveryItemRow.setDELV_NO(emptyCollectionOrder.getDelvno());
                                deliveryItemRow.setMAT_NO(deliveryItem.getMAT_NO());
                                deliveryItemRow.setMAT_DESC(deliveryItem.getMAT_DESC());
                                deliveryItemRow.setITM_NO((long) nextItemNo);

                                deliveryItemRow.setSLS_UOM(deliveryItem.getSLS_UOM() == null ? "" : deliveryItem.getSLS_UOM());
                                //deliveryItemRow.QTY = deliveryItemRow.QTY + deliveryItem.QTY;
                                deliveryItemRow.setQTY(deliveryItem.getQTY());
                                deliveryItemRow.setPROMO_TYPE(deliveryItem.getPROMO_TYPE() == null ? "" : deliveryItem.getPROMO_TYPE());
                                deliveryItemRow.setSLS_DEAL_DESC(deliveryItem.getSLS_DEAL_DESC() == null ? "" : deliveryItem.getSLS_DEAL_DESC());
                                deliveryItemRow.setPROMO_NO(deliveryItem.getPROMO_NO() == null ? "" : deliveryItem.getPROMO_NO());
                                deliveryItemRow.setPROMO_DESC(deliveryItem.getPROMO_DESC() == null ? "" : deliveryItem.getPROMO_DESC());
                                deliveryItemRow.setPROMO_TYPE_DESC("OSB");
                                deliveryItemRow.setSLS_DEAL(deliveryItem.getSLS_DEAL() == null ? "" : deliveryItem.getSLS_DEAL());
                                deliveryItemRow.setSLS_DEAL_DT(deliveryItem.getSLS_DEAL_DT() == null ? "" : deliveryItem.getSLS_DEAL_DT());
                                deliveryItemRow.setSHIP_NO(deliveryItem.getSHIP_NO());


                                //DELIVERY_ITM_COND records
                                //------------------------------------

                                List<DELIVERY_ITM_COND> deliveryItemsCond = new ArrayList<>();
                                try {
                                    IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DELIVERY_ITM_COND.TABLE_NAME, DELIVERY_ITM_COND.FIELD_DELV_NO + " ='" + deliveryItem.getDELV_NO() + "' AND " + DELIVERY_ITM_COND.FIELD_ITM_NO + " ='" + deliveryItem.getITM_NO() + "'");
                                    if (structures != null && structures.length > 0) {
                                        for (IDataStructure structure : structures) {
                                            deliveryItemsCond.add((DELIVERY_ITM_COND) structure);
                                        }
                                    }
                                } catch (DBException e) {
                                    Logger.e("", e);
                                }
                                for (DELIVERY_ITM_COND deliveryCondItem : deliveryItemsCond) {

                                    DELIVERY_ITM_COND deliveryItemCondRow = null;
                                    try {
                                        deliveryItemCondRow = new DELIVERY_ITM_COND();
                                        deliveryItemCondRow.setFid(deliveryCondItem.getFid());
                                        deliveryItemCondRow.setDELV_NO(emptyCollectionOrder.getDelvno());
                                        deliveryItemCondRow.setCOND_TYPE_ID(deliveryCondItem.getCOND_TYPE_ID());
                                        deliveryItemCondRow.setCOND_UNIT(deliveryCondItem.getCOND_UNIT());
                                        deliveryItemCondRow.setRATE(deliveryCondItem.getRATE());
                                        deliveryItemCondRow.setSHIP_NO(deliveryCondItem.getSHIP_NO());
                                        deliveryItemCondRow.setVAL(deliveryCondItem.getVAL());
                                        deliveryItemCondRow.setITM_NO((long) nextItemNo);
                                        ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(deliveryItemCondRow);


                                    } catch (DBException e) {
                                        Logger.e("", e);
                                    }
                                }
                                ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(deliveryItemRow);
                                nextItemNo = nextItemNo + OasisCache.getDELV_ITMNO_INCREMENT();
                            } catch (DBException e) {
                                Logger.e("", e);
                            }
                        }
                    }
                }
            }
        }
    }

    private boolean IsEmptyCollectionDelivery(String DelvNo)
    {
        int count = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT COUNT(*) AS COUNT FROM DELIVERY_ITEM AS d INNER JOIN MATERIAL_HEADER AS m ON d.MAT_NO = m.MAT_NO WHERE (d.DELV_NO = '" + DelvNo + "') AND (m.ITM_CAT = '" + Constants.MATERIAL_DUMMY_TYPE + "')");
            if(cursor == null ) {
                return true;
            } else {
                if (cursor.moveToFirst()) {
                    count = cursor.getInt(0);
                    cursor.close();
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
        }
        return count > 0;
    }

    public int getHighestItemNoOfDeliveryItems(String deliveryNo)
    {
        SHIPMENT_HEADER shipRow = DBHelper.getInstance().getShipmentRow();
        String query = "SELECT MAX(ITM_NO) FROM DELIVERY_ITEM WHERE SHIP_NO = '" + shipRow.getSHIP_NO() + "' AND DELV_NO = '" + deliveryNo + "'";
        long max = 0;
        try {
            max = aggregate(query);
        } catch (Exception e) {
            Logger.e("", e);
        }
        return (int)max + OasisCache.getDELV_ITMNO_INCREMENT();
    }


    public long aggregate(String FullQueryText) throws Exception {
        long count = 0;
        if (FullQueryText.toLowerCase().contains("update") || FullQueryText.toLowerCase().contains("delete") || FullQueryText.toLowerCase().contains("insert"))
            throw new Exception("UPDATE or DELETE OR INSERT not allowed.");

        if (!((FullQueryText.toLowerCase().contains("sum")) || FullQueryText.toLowerCase().contains("min") || FullQueryText.toLowerCase().contains("max")))
            throw new Exception("Invalid SUM query please check the query ");
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery(FullQueryText);
            if (cursor == null) {
                return -1;
            } else {
                if (cursor.moveToFirst()) {
                    count = cursor.getInt(0);
                    cursor.close();
                }
            }
        } catch (Exception e) {
            Logger.e("", e);
        }
        return count;
    }
}

class VisitDeliveryPojo
{
    private int visitno;
    private String delvno;
    private boolean isEmptyColDelv;

    public void setVisitno(int visitno) {
        this.visitno = visitno;
    }

    public int getVisitno() {
        return visitno;
    }

    public void setDelvno(String delvno) {
        this.delvno = delvno;
    }

    public String getDelvno() {
        return delvno;
    }

    public void setEmptyColDelv(boolean emptyColDelv) {
        isEmptyColDelv = emptyColDelv;
    }

    public boolean getEmptyColDelv() {
        return isEmptyColDelv;
    }

}


