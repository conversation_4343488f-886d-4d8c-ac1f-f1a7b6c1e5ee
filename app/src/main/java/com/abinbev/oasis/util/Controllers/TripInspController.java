package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.REASON_HEADER;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.List;

public class TripInspController {
    private static TripInspController tripInspController = null;
    //private IDataManager iDataManager = null;
    TripInspController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static TripInspController getInstance() {
        if (tripInspController == null) {
            tripInspController = new TripInspController();
        }

        return tripInspController;
    }

    public List<AUTH_HEADER> getUsernames(String type){
        List<AUTH_HEADER> authHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(AUTH_HEADER.TABLE_NAME, AUTH_HEADER.FIELD_TYPE + " = '" + type + "'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    authHeaderList.add((AUTH_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }

        if (authHeaderList.size() > 0) {
            return authHeaderList;
        } else {
            return null;
        }
    }
}
