package com.abinbev.oasis.util.Loader;

import android.content.Context;
import android.view.View;

/**
 * Created by <PERSON> | maxim.d<PERSON>bar<PERSON><EMAIL>
 * on 13.01.15 at 14:17
 */
class AnimatedView extends View {

    private int target;

    public AnimatedView(Context context) {
        super(context);
    }

    public float getXFactor() {
        return getX() / target;
    }

    public void setXFactor(float xFactor) {
        setX(target * xFactor);
    }

    public void setTarget(int target) {
        this.target = target;
    }

    public int getTarget() {
        return target;
    }
}
