package com.abinbev.oasis.util;


/**
 * Created by k<PERSON><PERSON> on 28/01/2021
 */
public class SAPInvItem {
    public String prodCode;
    public int lenProdCode = -6;
    public String prodDesc;
    public int lenProdDesc = -24;
    public String returnCode;
    public int lenReturnCode = 4;
    public Double delQty =0d;
    public int lenDelQty = 5;
    public Double unitPrice = 0d;
    public int lenUnitPrice = 8;
    public Double beerValue = 0d;
    public int lenBeerValue = 10;
    public Double depositValue = 0d;
    public int lenDepositValue = 10;
    public Double unitDeposit= 0d;
    public int lenUnitDeposit = 6;
    public Double totalValue = 0d;

    public Double totalValueOfNonSAB = 0d;
    public int lenTotalValue = 11;
    public boolean isZPROLine;
    public int itemNo;
    public boolean isZPROSubLine;

    public SAPInvItem() {
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public int getLenProdCode() {
        return lenProdCode;
    }

    public void setLenProdCode(int lenProdCode) {
        this.lenProdCode = lenProdCode;
    }

    public String getProdDesc() {
        return prodDesc;
    }

    public void setProdDesc(String prodDesc) {
        this.prodDesc = prodDesc;
    }

    public int getLenProdDesc() {
        return lenProdDesc;
    }

    public void setLenProdDesc(int lenProdDesc) {
        this.lenProdDesc = lenProdDesc;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public int getLenReturnCode() {
        return lenReturnCode;
    }

    public void setLenReturnCode(int lenReturnCode) {
        this.lenReturnCode = lenReturnCode;
    }

    public Double getDelQty() {
        return delQty;
    }

    public void setDelQty(Double delQty) {
        this.delQty = delQty;
    }

    public int getLenDelQty() {
        return lenDelQty;
    }

    public void setLenDelQty(int lenDelQty) {
        this.lenDelQty = lenDelQty;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public int getLenUnitPrice() {
        return lenUnitPrice;
    }

    public void setLenUnitPrice(int lenUnitPrice) {
        this.lenUnitPrice = lenUnitPrice;
    }

    public Double getBeerValue() {
        return beerValue;
    }

    public void setBeerValue(Double beerValue) {
        this.beerValue = beerValue;
    }

    public int getLenBeerValue() {
        return lenBeerValue;
    }

    public void setLenBeerValue(int lenBeerValue) {
        this.lenBeerValue = lenBeerValue;
    }

    public Double getDepositValue() {
        return depositValue;
    }

    public void setDepositValue(Double depositValue) {
        this.depositValue = depositValue;
    }

    public int getLenDepositValue() {
        return lenDepositValue;
    }

    public void setLenDepositValue(int lenDepositValue) {
        this.lenDepositValue = lenDepositValue;
    }

    public Double getUnitDeposit() {
        return unitDeposit;
    }

    public void setUnitDeposit(Double unitDeposit) {
        this.unitDeposit = unitDeposit;
    }

    public int getLenUnitDeposit() {
        return lenUnitDeposit;
    }

    public void setLenUnitDeposit(int lenUnitDeposit) {
        this.lenUnitDeposit = lenUnitDeposit;
    }

    public Double getTotalValue() {
        return totalValue;
    }

    public void setTotalValue(Double totalValue) {
        this.totalValue = totalValue;
    }

    public int getLenTotalValue() {
        return lenTotalValue;
    }

    public void setTotalValueOfNonSAB(Double totalValueOfNonSAB) {
        this.totalValueOfNonSAB = totalValueOfNonSAB;
    }

    public double getTotalValueOfNonSAB() {
        return totalValueOfNonSAB;
    }

    public void setLenTotalValue(int lenTotalValue) {
        this.lenTotalValue = lenTotalValue;
    }

    public boolean isZPROLine() {
        return isZPROLine;
    }

    public void setZPROLine(boolean ZPROLine) {
        isZPROLine = ZPROLine;
    }

    public int getItemNo() {
        return itemNo;
    }

    public void setItemNo(int itemNo) {
        this.itemNo = itemNo;
    }

    public boolean isZPROSubLine() {
        return isZPROSubLine;
    }

    public void setZPROSubLine(boolean ZPROSubLine) {
        isZPROSubLine = ZPROSubLine;
    }
}
