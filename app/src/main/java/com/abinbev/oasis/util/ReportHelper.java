package com.abinbev.oasis.util;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.text.Paragraph;
import com.unvired.logger.Logger;
import com.unvired.pdf.writer.PDFDocument;

import java.io.ByteArrayOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.EnumMap;
import java.util.Map;

@SuppressLint({ "SimpleDateFormat", "DefaultLocale", "RtlHardcoded" })
public class ReportHelper {

	private static ReportHelper reportHelper = null;

	private String pdfPath;

	private PDFDocument pdfDocument = null;

	private Bitmap customerSign;

	private Bitmap driverSign;

	public static final String TRIM_MARK = "..";

	private static final int TRIM_MARK_LENGTH = TRIM_MARK.length();

	private static final int WHITE = 0xFFFFFFFF;
	private static final int BLACK = 0xFF000000;

	public static ReportHelper getInstance() {

		if (reportHelper == null) {

			reportHelper = new ReportHelper();
		}

		return reportHelper;
	}

	public PDFDocument getPDFDocument() {

		return pdfDocument;
	}

	public void setPDFDocument(PDFDocument pdfDocumentVal) {

		pdfDocument = pdfDocumentVal;
	}

	public void setPdfPath(String pdfPathVal) {

		pdfPath = pdfPathVal;
	}

	public String getPdfPath() {

		return pdfPath;
	}

	public static String getCurrentDate(String format) {

		return new SimpleDateFormat(format).format(new Date());
	}

	public static boolean isEmptyString(final String checkString) {

		return checkString != null ? (checkString.trim().length() != 0 ? false : true) : true;
	}

	public static String trim(String string, int length) {

		// try to remove padding zeros first
		String newString = removeLeadingZeroes(string);

		int newLength = newString.length();
		if (newLength > length) {
			if (length > TRIM_MARK_LENGTH) {
				int headLength = (length - TRIM_MARK_LENGTH) / 2;
				int tailLength = length - TRIM_MARK_LENGTH - headLength;
				StringBuffer buffer = new StringBuffer(length);
				buffer.append(newString.substring(0, headLength));
				buffer.append(TRIM_MARK);
				buffer.append(newString.substring(newLength - tailLength));
				newString = buffer.toString();
			}
		}
		return newString;
	}

	public static String removeLeadingZeroes(String aString) {

		aString = aString.substring(1);
		if ((aString == null))
			return "";
		int zeroIndex = 1;
		while ((aString.length() > zeroIndex) && ('0' == aString.charAt(zeroIndex))) {
			zeroIndex++;
		}
		return aString.substring(zeroIndex);
	}

	@SuppressWarnings("deprecation")
	public static String getFormattedTime(String time, String format) throws ParseException {
		// for testing
		if (time.contains("GMT"))
			return new SimpleDateFormat(format).format((new Date(time)));
		else
			return new SimpleDateFormat(format).format((new SimpleDateFormat(Constants.NORMAL_TIME_FORMAT_24).parse(time)));
	}

	public static String getDateInRequiredFormat(String date, String format) throws ParseException {

		return new SimpleDateFormat(format).format((new SimpleDateFormat(Constants.DATE_FORMAT_FROM_SERVER).parse(date)));

	}

	public static String getFixedWidthString(Object inObj, int reqWidth, TextAlignment alignment) {

		if (inObj == null)
			return String.format("%-" + reqWidth + "s", "");
		String result = null;
		String newString = null;

		if (inObj instanceof Double)
			newString = String.valueOf(((Double) inObj).doubleValue());
		else if (inObj instanceof Long)
			newString = String.valueOf(((Long) inObj).longValue());
		else if (inObj instanceof Integer)
			newString = String.valueOf(((Integer) inObj).intValue());
		else if (inObj instanceof Float)
			newString = String.valueOf(((Float) inObj).floatValue());
		else
			newString = inObj.toString();

		if (newString.length() == 0)
			result = String.format("%-" + reqWidth + "s", newString);
		else if (newString.length() > reqWidth)
			result = newString.substring(0, reqWidth);
		else if (newString.length() < reqWidth) {
			if (alignment == TextAlignment.CENTER)
				result = center(newString, reqWidth);
			else if (alignment == TextAlignment.LEFT)
				result = String.format("%-" + reqWidth + "s", newString);
			else
				result = String.format("%" + reqWidth + "s", newString);
		} else
			result = newString;

		return result;
	}

	public static String getFixedWidthString(double doubleType, int reqWidth, TextAlignment alignment, int precision) {

		String result = null;
		String newString = null;

		newString = String.format("%." + precision + "f", Double.valueOf(doubleType));

		if (newString.length() == 0)
			result = String.format("%-" + reqWidth + "s", newString);
		else if (newString.length() > reqWidth)
			result = newString.substring(0, reqWidth);
		else if (newString.length() < reqWidth) {
			if (alignment == TextAlignment.CENTER)

				result = center(newString, reqWidth);
			else if (alignment == TextAlignment.LEFT)
				result = String.format("%-" + reqWidth + "s", newString);
			else
				result = String.format("%" + reqWidth + "s", newString);
		} else
			result = newString;

		return result;
	}

	public static String getFixedWidthString(long longType, int reqWidth, TextAlignment alignment) {

		String result = null;
		String newString = null;

		newString = String.format("%d", Long.valueOf(longType));

		if (newString.length() == 0)
			result = String.format("%-" + reqWidth + "s", newString);
		else if (newString.length() > reqWidth)
			result = newString.substring(0, reqWidth);
		else if (newString.length() < reqWidth) {
			if (alignment == TextAlignment.CENTER)
				result = center(newString, reqWidth);
			else if (alignment == TextAlignment.LEFT)
				result = String.format("%-" + reqWidth + "s", newString);
			else
				result = String.format("%" + reqWidth + "s", newString);
		} else
			result = newString;

		return result;
	}

	public static String center(String s, int length) {

		if (s.length() > length) {
			return s.substring(0, length);
		} else if (s.length() == length) {
			return s;
		} else {
			int leftPadding = (length - s.length()) / 2;
			StringBuilder leftBuilder = new StringBuilder();
			for (int i = 0; i < leftPadding; i++) {
				leftBuilder.append(" ");
			}

			int rightPadding = length - s.length() - leftPadding;
			StringBuilder rightBuilder = new StringBuilder();
			for (int i = 0; i < rightPadding; i++)
				rightBuilder.append(" ");

			return leftBuilder.toString() + s + rightBuilder.toString();
		}
	}

	public static String getTimeDiff(String date1, String time1, String date2, String time2) throws ParseException {

		if (date1 != null && date2 != null && time1 != null && time2 != null) {

			Date tempDate1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date1 + " " + time1);
			Date tempDate2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date2 + " " + time2);

			// Get msec from each, and subtract.
			long diff = tempDate2.getTime() - tempDate1.getTime();
			double diffInHours = diff / ((double) 1000 * 60 * 60);
			double diffInMinutes = (diffInHours - (int) diffInHours) * 60;

			return String.format("%02d:%02d", Integer.valueOf((Math.abs((int) diffInHours))), Integer.valueOf((Math.abs((int) diffInMinutes))));

		}
		return "00:00";
	}

	public enum TextAlignment {

		LEFT(0),

		CENTER(1),

		RIGHT(2);

		private int value;

		TextAlignment(int val) {
			value = val;
		}

		int getValue() {
			return value;
		}
	}

	public static Date getDateInFormat(String date, String format) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		return formatter.parse(date);
	}

	public final static int dateDiffUnit(int aCalendarUnit, Date aDate1, Date aDate2) {
		int unit = dateDiff(aCalendarUnit, aDate1, aDate2);
		// Now provide only the value that corresponds to the unit in question
		switch (aCalendarUnit) {
		case Calendar.DAY_OF_WEEK_IN_MONTH: // 8
			return unit % 7;
		case Calendar.DAY_OF_WEEK: // 7
			return unit % 7;
		case Calendar.DAY_OF_YEAR: // 6
			// Have to assume here that all years have a max of 365 days
			// (ignore
			// leap years)
			return unit % 365;
		case Calendar.DAY_OF_MONTH: // 5
			// case Calendar.DATE : // 5
			// Have to assume here that all months have a max of 31 days
			return unit % 31;
		case Calendar.WEEK_OF_MONTH: // 4
			// Have to assume here that all months have a max of 5 weeks
			return unit % 5;
		case Calendar.WEEK_OF_YEAR: // 3
			return unit % 52;
		case Calendar.MONTH: // 2
			return unit % 12;
		case Calendar.YEAR: // 1
			return unit;
		case Calendar.HOUR: // 10
		case Calendar.HOUR_OF_DAY: // 11
			return unit % 24;
		case Calendar.MINUTE: // 12
			return unit % 60;
		case Calendar.SECOND: // 13
			return unit % 60;
		default:
			return unit;
		}
	}

	public final static int dateDiff(int aCalendarUnit, Date aDate1, Date aDate2) {
		int x = 1000;
		if (aCalendarUnit == Calendar.DAY_OF_MONTH) {
			x = x * 60 * 60 * 24;
		} else if (aCalendarUnit == Calendar.HOUR_OF_DAY) {
			x = x * 60 * 60;
		} else if (aCalendarUnit == Calendar.MINUTE) {
			x = x * 60;
		} else
			return 1;
		return (int) ((aDate2.getTime() - aDate1.getTime()) / (x));
		// seconds in 1 day
	}

	public void saveCustomerAttachmentDetails(Bitmap customerSign) {
		this.customerSign = customerSign;

	}

	public void saveDriverAttachmentDetails(Bitmap driverSign) {

		this.driverSign = driverSign;
	}

	public byte[] getCustomerAttachmentDetails() {

		byte[] byteArr = null;
		if (customerSign != null) {

			Bitmap immagex = customerSign;
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			immagex.compress(Bitmap.CompressFormat.PNG, 100, baos);
			byteArr = baos.toByteArray();
		}
		return byteArr;
	}

	public byte[] getDriverAttachmentDetails() {

		byte[] byteArr = null;

		if (driverSign != null) {

			Bitmap immagex = driverSign;
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			immagex.compress(Bitmap.CompressFormat.PNG, 100, baos);
			byteArr = baos.toByteArray();

		}
		return byteArr;
	}

	public void removeAttachedDetails() {
		customerSign = null;
		driverSign = null;
	}

	public void clear() {
		reportHelper = null;
		pdfPath = null;
		pdfDocument = null;
	}

	public static Bitmap encodeAsBitmap(String contents, BarcodeFormat format, int img_width, int img_height) throws WriterException {
		String contentsToEncode = contents;
		if (contentsToEncode == null) {
			return null;
		}
		Map<EncodeHintType, Object> hints = null;
		String encoding = guessAppropriateEncoding(contentsToEncode);
		if (encoding != null) {
			hints = new EnumMap<EncodeHintType, Object>(EncodeHintType.class);
			hints.put(EncodeHintType.CHARACTER_SET, encoding);
		}
		MultiFormatWriter writer = new MultiFormatWriter();
		BitMatrix result;
		try {
			result = writer.encode(contentsToEncode, format, img_width, img_height, hints);
		} catch (IllegalArgumentException iae) {
			// Unsupported format
			Logger.log(Logger.LEVEL_ERROR, ReportHelper.class.getClass().getName(), "encodeAsBitmap", Utils.getExceptionMessage(iae));
			return null;
		}

		// Find the bounding box of the actual barcode (without the quiet zone)
		int[] cropBorders = findBarcodeBorders(result);

		int croppedWidth = cropBorders[1] - cropBorders[0];
		int croppedHeight = cropBorders[3] - cropBorders[2];

		int[] pixels = new int[croppedWidth * croppedHeight];
		for (int y = cropBorders[2]; y < cropBorders[3]; y++) {
			int offset = (y - cropBorders[2]) * croppedWidth;
			for (int x = cropBorders[0]; x < cropBorders[1]; x++) {
				pixels[offset + (x - cropBorders[0])] = result.get(x, y) ? BLACK : WHITE;
			}
		}

		Bitmap bitmap = Bitmap.createBitmap(croppedWidth, croppedHeight, Bitmap.Config.ARGB_8888);
		bitmap.setPixels(pixels, 0, croppedWidth, 0, 0, croppedWidth, croppedHeight);
		return bitmap;
	}

	private static int[] findBarcodeBorders(BitMatrix bitMatrix) {
		int[] borders = new int[]{bitMatrix.getWidth(), 0, bitMatrix.getHeight(), 0}; // left, right, top, bottom

		// Find left and right boundaries
		for (int x = 0; x < bitMatrix.getWidth(); x++) {
			for (int y = 0; y < bitMatrix.getHeight(); y++) {
				if (bitMatrix.get(x, y)) {
					if (x < borders[0]) borders[0] = x; // left boundary
					if (x > borders[1]) borders[1] = x; // right boundary
					if (y < borders[2]) borders[2] = y; // top boundary
					if (y > borders[3]) borders[3] = y; // bottom boundary
				}
			}
		}

		// Add 1 pixel of buffer around the barcode (optional)
		borders[0] = Math.max(0, borders[0] - 1);
		borders[1] = Math.min(bitMatrix.getWidth(), borders[1] + 1);
		borders[2] = Math.max(0, borders[2] - 1);
		borders[3] = Math.min(bitMatrix.getHeight(), borders[3] + 1);

		return borders;
	}
	public static String guessAppropriateEncoding(CharSequence contents) {
		// Very crude at the moment
		for (int i = 0; i < contents.length(); i++) {
			if (contents.charAt(i) > 0xFF) {
				return "UTF-8";
			}
		}
		return null;
	}

}