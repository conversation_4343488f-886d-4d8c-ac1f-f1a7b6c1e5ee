package com.abinbev.oasis.util;

import com.unvired.model.AttachmentItem;
import com.unvired.model.InfoMessage;
import com.unvired.model.OutObject;
import com.unvired.sync.SyncEngine;
import com.unvired.sync.notifier.NotificationListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

public class AppNotificationListener implements NotificationListener {
	
	private static AppNotificationListener notifier = null;
	
	private List<NotificationListener> notificationListeners;
	
	public static AppNotificationListener getInstance() {
		if (notifier == null) {
			notifier = new AppNotificationListener();
		}
		
		return notifier;
	}
	
	public AppNotificationListener() {
		notificationListeners = new ArrayList<>();
		SyncEngine.getInstance().registerNotificationListener(this);
	}
	
	public void addNotificationListener(NotificationListener notificationListener) {
		notificationListeners.add(notificationListener);
	}
	
	public void removeNotificationListener(NotificationListener notificationListener) {
		notificationListeners.remove(notificationListener);
	}
	
	@Override
	public void notifyDataChange(Vector vector) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDataChange(vector);
		}
	}
	
	@Override
	public void notifyDataReceiveCompletion() {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDataReceiveCompletion();
		}
	}
	
	@Override
	public void notifyDataSend(OutObject outObject) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDataSend(outObject);
		}
	}
	
	@Override
	public void notifyException(Exception e) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyException(e);
		}
	}
	
	@Override
	public void notifyServerMessages(Vector vector) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyServerMessages(vector);
		}
	}
	
	@Override
	public void notifyDemoMode(String s) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDemoMode(s);
		}
	}
	
	@Override
	public void notifyAttachmentDownloadSuccess(AttachmentItem attachmentItem) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyAttachmentDownloadSuccess(attachmentItem);
		}
	}
	
	@Override
	public void notifyAttachmentDownloadFailure(AttachmentItem attachmentItem, String s) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyAttachmentDownloadFailure(attachmentItem, s);
		}
	}
	
	@Override
	public void notifyApplicationReset() {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyApplicationReset();
		}
	}
	
	@Override
	public void notifyOutBoxItemDiscarded(InfoMessage infoMessage) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyOutBoxItemDiscarded(infoMessage);
		}
	}
	
	@Override
	public void notifyAttachmentOutBoxItemDiscarded(InfoMessage infoMessage) {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyAttachmentOutBoxItemDiscarded(infoMessage);
		}
	}
	
	@Override
	public void notifyDataSendAbort() {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDataSendAbort();
		}
	}
	
	@Override
	public void notifyAttachmentSendAbort() {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyAttachmentSendAbort();
		}
	}
	
	@Override
	public void notifyDataRetreiverAbort() {
		for (NotificationListener listener : notificationListeners) {
			listener.notifyDataRetreiverAbort();
		}
	}

	@Override
	public void notifyIncomingDataProcessingFinished() {

	}

	@Override
	public void notifyJWTTokenUpdated(String s) {

	}


//
//	@Override
//	public void notifyIncomingDataProcessingFinished() {
//		for (NotificationListener listener : notificationListeners) {
//			listener.notifyIncomingDataProcessingFinished();
//		}
//	}
}

