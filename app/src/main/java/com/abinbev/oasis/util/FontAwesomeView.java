package com.abinbev.oasis.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.R;

public class FontAwesomeView extends androidx.appcompat.widget.AppCompatTextView {

    public enum FontAwesomeStyle {SOLID, REGULAR};

    public FontAwesomeView(@NonNull Context context) {
        super(context);
        initSolid();
    }

    public FontAwesomeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        readAttr(context,attrs);
    }

    public FontAwesomeView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        readAttr(context,attrs);
        initRegular();
    }

    private void initRegular() {
        //Font name should not contain "/".
        Typeface tf = Typeface.createFromAsset(getContext().getAssets(),
                "fa-regular-400.ttf");
        setTypeface(tf);
    }

    private void initSolid() {
        //Font name should not contain "/".
        Typeface tf = Typeface.createFromAsset(getContext().getAssets(),
                "fa-solid-900.ttf");
        setTypeface(tf);
    }

    private void readAttr(Context context, AttributeSet attrs) {
        @SuppressLint("CustomViewStyleable") TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.FontAwesomeRegular);
        FontAwesomeStyle fontAwesomeStyle = FontAwesomeStyle.values()[(typedArray.getInt(R.styleable.FontAwesomeRegular_fontAwesomeStyle,0))] ;
        if (fontAwesomeStyle != null) {
            if(fontAwesomeStyle.equals(FontAwesomeStyle.REGULAR)){
                initRegular();
            }else if(fontAwesomeStyle.equals(FontAwesomeStyle.SOLID)){
                initSolid();
            }
        }
        typedArray.recycle();
    }
}
