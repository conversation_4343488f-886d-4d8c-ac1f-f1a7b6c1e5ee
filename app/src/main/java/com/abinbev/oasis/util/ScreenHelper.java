package com.abinbev.oasis.util;

enum MessageType
{
    Error, Info, Warning
};


public class ScreenHelper {

    private static boolean UnPlannedCustomer = false;
    private static boolean FromCustomerSignature = false;
    private static Constants.SCREEN_NAVIGATION_MODE CurrentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;

    public static void setUnPlannedCustomer(boolean value) {
        UnPlannedCustomer = value;
    }

    public static boolean getUnPlannedCustomer() {
        return UnPlannedCustomer;
    }

    public static void setFromCustomerSignature(boolean value) {
        FromCustomerSignature = value;
    }

    public static boolean getFromCustomerSignature() {
        return FromCustomerSignature;
    }

    public static void setCurrentScreensNavigationMode(Constants.SCREEN_NAVIGATION_MODE value) {
        CurrentScreensNavigationMode = value;
    }

    public static Constants.SCREEN_NAVIGATION_MODE getCurrentScreensNavigationMode() {
        return CurrentScreensNavigationMode;
    }
}
