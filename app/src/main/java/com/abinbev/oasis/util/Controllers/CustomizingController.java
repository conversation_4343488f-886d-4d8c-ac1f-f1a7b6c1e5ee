package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;

import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TIME;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.List;

public class CustomizingController {

    private static CustomizingController customizingController = null;
   // private IDataManager iDataManager = null;

    CustomizingController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static CustomizingController getInstance() {
        if (customizingController == null) {
            customizingController = new CustomizingController();
        }
        return customizingController;
    }

    public int getIntKeyValue(String key) {
        int res = 0;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT KEY_VALUE FROM CUSTOMIZATION_HEADER WHERE KEY_NAME='" + key + "'");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    res = Integer.parseInt(cursor.getString(0));
                    cursor.close();
                }
            }
            if (!cursor.isClosed()) {
                cursor.close();
            }
        } catch (DBException e) {

            Logger.e("", e);
        }
        return res;
    }


    public String getStringKeyValue(String key) {
        String res = null;
        try {
            Cursor cursor = ApplicationManager.getInstance().getDataManager().executeQuery("SELECT KEY_VALUE FROM CUSTOMIZATION_HEADER WHERE KEY_NAME='" + key + "'");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    res = cursor.getString(0);
                    cursor.close();
                }

                if (!cursor.isClosed()) {
                    cursor.close();
                }
            }

        } catch (DBException e) {
            Logger.e("", e);
        }
        return res;
    }

    public void addOrUpdateCustomizingRow(String keyCustomerRating, String value) {
        List<CUSTOMIZATION_HEADER> customizationHeaderList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, CUSTOMIZATION_HEADER.FIELD_KEY_NAME+" = '"+keyCustomerRating+"'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    customizationHeaderList.add((CUSTOMIZATION_HEADER) structure);
                }
            }
            CUSTOMIZATION_HEADER customization_header =new CUSTOMIZATION_HEADER();
            if(customizationHeaderList.size()<=0 || customizationHeaderList.get(0)==null){

                    customization_header.setLid(customization_header.getLid());
                    customization_header.setKEY_NAME(keyCustomerRating);
                    customizationHeaderList.add(customization_header);
            }else {
                customization_header=customizationHeaderList.get(0);
            }
            customization_header.setKEY_VALUE(value);
            for (CUSTOMIZATION_HEADER customizationHeader : customizationHeaderList){
                insertOrUpdateCustomizationHeader(customizationHeader);
            }
        } catch (DBException e) {
            Logger.e("", e);
            return;
        }

    }


    public void insertOrUpdateCustomizationHeader(CUSTOMIZATION_HEADER header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);

        }
    }

    public static void addOrUpdateCustomizingRating(String key, String value)
    {
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + key + "'");
            CUSTOMIZATION_HEADER newCustomizationHeader;
            if (structures == null || structures.length <= 0) {
                newCustomizationHeader = new CUSTOMIZATION_HEADER();
                newCustomizationHeader.setKEY_NAME(key);
            } else {
                newCustomizationHeader =((CUSTOMIZATION_HEADER) structures[0]);
            }
            newCustomizationHeader.setKEY_VALUE(value);
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(newCustomizationHeader);


        } catch (DBException e) {
            Logger.e("", e);
        }
    }
}
