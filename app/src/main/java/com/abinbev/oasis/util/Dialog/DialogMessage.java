package com.abinbev.oasis.util.Dialog;

import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.unvired.oasis.R;

public class DialogMessage {
    final private Dialog dialog;
    final private LinearLayout container;
    final private TextView titleTV, subTitleTv, tvOK, tvNeutral, tvCancel;
    final private LinearLayout okButtonTv, cancelButtonTV, neutralButtonTv;
    public DialogMessage(Activity activity) {
        dialog = new Dialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCancelable(false);
        dialog.setContentView(R.layout.dialog_message);
        container = dialog.findViewById(R.id.container);
        titleTV = dialog.findViewById(R.id.tv1);
        subTitleTv = dialog.findViewById(R.id.tv2);
        okButtonTv = dialog.findViewById(R.id.btnDialogOk);
        cancelButtonTV = dialog.findViewById(R.id.btnDialogCancel);
        neutralButtonTv = dialog.findViewById(R.id.btnDialogNeutral);
        tvOK = dialog.findViewById(R.id.tvok);
        tvNeutral = dialog.findViewById(R.id.tvNeutral);
        tvCancel = dialog.findViewById(R.id.tvCan);
    }

    public void showDialogWithPositiveBtn(String title, String subTitle, String positiveBtnLable, final DialogClickListner dialogClickListner){
        if(title.equals("") || title==null){
            titleTV.setVisibility(View.GONE);
        }else {
            titleTV.setText(title);
        }
        if(subTitle.equals("") || title==null){
            subTitleTv.setVisibility(View.GONE);
        }else {
            subTitleTv.setText(subTitle);
        }
        cancelButtonTV.setVisibility(View.GONE);
        neutralButtonTv.setVisibility(View.GONE);
        tvOK.setText(positiveBtnLable);
        okButtonTv.setOnClickListener(v -> dialogClickListner.onPositiveClick(dialog));
        dialog.show();

    }

    public void showDialogWithPositiveAndNegativeBtn(String title, String subTitle, String positiveBtnLable,String negativeBtnLable, final DialogClickListner dialogClickListner){
        if(title.equals("") || title==null){
            titleTV.setVisibility(View.GONE);
        }else {
            titleTV.setText(title);
        }
        if(subTitle.equals("") || title==null){
            subTitleTv.setVisibility(View.GONE);
        }else {
            subTitleTv.setText(subTitle);
        }
        neutralButtonTv.setVisibility(View.GONE);
        tvOK.setText(positiveBtnLable);
        tvCancel.setText(negativeBtnLable);
        okButtonTv.setOnClickListener(v -> dialogClickListner.onPositiveClick(dialog));
        cancelButtonTV.setOnClickListener(v -> dialogClickListner.onNegativeClick(dialog));
        dialog.show();
    }

    public void showDialogWithPositiveNegativeAndNeutralBtn(String title, String subTitle, String positiveBtnLable,String negativeBtnLable,String neutralBtnlable, final DialogClickListner dialogClickListner){
        if(title.equals("") || title==null){
            titleTV.setVisibility(View.GONE);
        }else {
            titleTV.setText(title);
        }
        if(subTitle.equals("") || title==null){
            subTitleTv.setVisibility(View.GONE);
        }else {
            subTitleTv.setText(subTitle);
        }

        tvOK.setText(positiveBtnLable);
        tvCancel.setText(negativeBtnLable);
        tvNeutral.setText(neutralBtnlable);
        okButtonTv.setOnClickListener(v -> dialogClickListner.onPositiveClick(dialog));
        cancelButtonTV.setOnClickListener(v -> dialogClickListner.onNegativeClick(dialog));
        neutralButtonTv.setOnClickListener(v -> dialogClickListner.onNeutralClick(dialog));
        dialog.show();
    }

    public void showDialogWithPositive(String title, String subTitle, String positiveBtnLable, final DialogClickListner dialogClickListner){
        if(title.equals("") || title==null){
            titleTV.setVisibility(View.GONE);
        }else {
            titleTV.setText(title);
        }
        neutralButtonTv.setVisibility(View.GONE);
        cancelButtonTV.setVisibility(View.GONE);
        subTitleTv.setText(subTitle);
        tvOK.setText(positiveBtnLable);
        okButtonTv.setOnClickListener(v -> dialogClickListner.onPositiveClick(dialog));
        dialog.show();
    }
}
