package com.abinbev.oasis.util;

import android.app.Activity;
import android.app.Dialog;
import android.database.Cursor;

import com.abinbev.oasis.be.INPUT_GET_SHIPMENTS_HEADER;
import com.abinbev.oasis.be.INPUT_GET_SHIPMENT_HEADER;
import com.abinbev.oasis.be.INPUT_RESET_SHIPMENT_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.unvired.database.DBException;
import com.unvired.exception.ApplicationException;
import com.unvired.fcm.UnviredFCMMessagingService;
import com.unvired.logger.Logger;
import com.unvired.sync.SyncConstants;
import com.unvired.sync.SyncEngine;
import com.unvired.sync.out.ISyncAppCallback;

public class PAHelper {

    public static void getDepotList(ISyncAppCallback callback) {
        DBHelper.getInstance().clearDepot();
        try {
            SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.PULL, null, "", Constants.PA_GET_DEPOT_LIST, true, callback);
        } catch (ApplicationException e) {
            Logger.e("", e);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static void getImages(ISyncAppCallback callback) {
        DBHelper.getInstance().clearImages();
        try {
            SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.PULL, null, "", Constants.PA_GET_IMAGES, true, callback);
            DepotController.getInstance().addDefaultImage();
        } catch (ApplicationException e) {
            Logger.e("", e);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static void getShipmentsList(INPUT_GET_SHIPMENTS_HEADER header, ISyncAppCallback callback) {
        DBHelper.getInstance().clearShipmentAndRelatedBEs();
        try {
            SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.QUERY, header, "", Constants.PA_GET_SHIPMENT_LIST, false, callback);
        } catch (ApplicationException e) {
            Logger.e("", e);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static void getShipment(INPUT_GET_SHIPMENT_HEADER header, ISyncAppCallback callback) {
        DBHelper.getInstance().clearShipmentAndRelatedBEs();
        try {
            SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.QUERY, header, "", Constants.PA_GET_SHIPMENT, true, callback);
        } catch (ApplicationException e) {
            Logger.e("", e);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static void resetShipmentFromServer(INPUT_RESET_SHIPMENT_HEADER header, ISyncAppCallback callback) {
        try {
            SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, "", Constants.PA_RESET_SHIPMENT, false, callback);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    public static void uploadShipment(Activity activity){
        //upload to SAP
        String shipNo = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        //Special Data edits  - Due to wrong Design its removed here.
        DBHelper.getInstance().dataAdjustments();
        DBHelper.getInstance().createNewTime(shipNo, Constants.TIME_TYPE_SHIP_UPLOAD_START);
        if(!Utils.isInternetConnected(activity)){
            String responseText = "Server not Reachable. Please make sure the device is docked correctly and network connectivity is available.";
            String responseCode = Constants.FAILURE;
            DialogMessage dialogMessage = new DialogMessage(activity);
            dialogMessage.showDialogWithPositive("", responseText, "OK", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }
                @Override
                public void onNegativeClick(Dialog dialogMessage) { }
                @Override
                public void onNeutralClick(Dialog dialogMessage) { }
            });
            Logger.log(Logger.LEVEL_ERROR, activity.getLocalClassName(), responseText, null);
        }
        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
        try {
            SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.QUERY,shipment_header,null,Constants.PA_UPLOAD_SHIPMENT,shipment_header.getBEName(),shipment_header.getLid(),false);
        } catch (ApplicationException e) {
            Logger.log(Logger.LEVEL_ERROR, activity.getLocalClassName(),"PA_UPLOAD_SHIPMENT : Exception " , e.getMessage());
            Logger.e("", e);
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, activity.getLocalClassName(), "PA_UPLOAD_SHIPMENT : Exception ", e.getMessage());
            Logger.e("", e);
        }

        DBHelper.getInstance().clearShipmentAndRelatedBEs();
    }

    private static void clearShipmentAndRelatedBEs() {

    }

}
