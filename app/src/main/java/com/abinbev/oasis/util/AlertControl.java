package com.abinbev.oasis.util;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;

import com.abinbev.oasis.activities.PreTripInspectionQuestionsActivity;
import com.abinbev.oasis.util.Loader.Loader;
import com.unvired.oasis.R;

public class AlertControl {

    public static void showInfo(final Context context, String msg) {
        new AlertDialog.Builder(context)
                .setCancelable(false)
                .setMessage(msg)
                .setPositiveButton(context.getString(R.string.ok), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        //do nothing
                    }
                }).create().show();
    }
}
