package com.abinbev.oasis.util.Controllers;

import android.database.Cursor;

import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.IMAGE_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.List;

public class DepotController {

    private static DepotController depotController = null;
    //private IDataManager iDataManager = null;

    DepotController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static DepotController getInstance() {
        if (depotController == null) {
            depotController = new DepotController();
        }
        return depotController;
    }

    public DEPOT_HEADER getSelectedDepotRow(){
        List<DEPOT_HEADER> depotHeaders = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DEPOT_HEADER.TABLE_NAME, DEPOT_HEADER.FIELD_SELECTED+" = 'X'");
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    depotHeaders.add((DEPOT_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if (depotHeaders.size() > 0) {
            return depotHeaders.get(0);
        } else {
            return null;
        }
    }

    public List<DEPOT_HEADER> getDepotList() {
        List<DEPOT_HEADER> depotList = new ArrayList<>();

        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DEPOT_HEADER.TABLE_NAME);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    depotList.add((DEPOT_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        return depotList;



    }

    public DEPOT_HEADER getDepot(String depot) {
        List<DEPOT_HEADER> depotList = new ArrayList<>();
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(DEPOT_HEADER.TABLE_NAME,DEPOT_HEADER.FIELD_DEPOT+" = '"+depot+"'");

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    depotList.add((DEPOT_HEADER) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(depotList.size()>0){
            return depotList.get(0);
        }else {
            return null;
        }

    }

    public void addDefaultImage()
    {
        try {
            IMAGE_HEADER imageHeaderRow = new IMAGE_HEADER();
            imageHeaderRow.setIMG_NAME("00000");
            imageHeaderRow.setIMG_DESC("default");
            imageHeaderRow.setIMG(Constants.DEFAULT_IMAGE);
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(imageHeaderRow);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }
}
