package com.abinbev.oasis.util;

import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Dictionary;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OasisCache {
    public static Map<String, Integer> CasesPerPallet;
    private static Dictionary<String, String> ImagesForMaterials;
    public static String[] DummyMaterials = null;
    public static String[] PalletMaterials = null;
    private static int _POLLING_INTERVAL = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_POLLING_INTERVAL);

    private static String _PRN_SERIAL_PORT = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_PRN_SERIAL_PORT);
    private static String _PRN_SERIAL_PORT_LOCAL = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_PRN_SERIAL_PORT_LOCAL);
    private static int _PRN_BAUD_RATE = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_PRN_BAUD_RATE);
    private static int _PRN_BIT_RATE = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_PRN_BIT_RATE);
    private static String _PRN_BT_PAIR_CODE = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_PRN_BT_PAIR_CODE);

    private static String _TIME_ENABLE_CONFIG = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_TIME_ENABLE_CONFIG);
    private static String _GPS_ENABLE_CONFIG = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_GPS_ENABLE_CONFIG);
    private static int _GPS_READ_TIMEOUT = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_GPS_READ_TIMEOUT);

    private static String _CUSTOMER_RATING = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_CUSTOMER_RATING);
    //private static String _CUSTOMER_GRVNUMBER = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_CUSTOMER_GRVNUMBER);

    private static String _ACC_QUERIES = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_ACC_QUERIES);
    private static String _ACC_CLERK = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_ACC_CLERK);

    private static String _DEF_PAL_MAT = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_DEF_PAL_MAT);

    private static int _DELV_ITMNO_INCREMENT = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_DELV_ITMNO_INCREMENT);

    private static int _PAL_LIMIT = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_PAL_LIMIT);
    private static String ACC_QUERIES = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_ACC_QUERIES);
    private static String ACC_CLERK =CustomizingController.getInstance().getStringKeyValue(Constants.KEY_ACC_CLERK);
//

    private static byte[] _SABLOGO_Bytes = null;


    public static String getTimeEnableConfig() {
        if(_TIME_ENABLE_CONFIG == null){
            _TIME_ENABLE_CONFIG=CustomizingController.getInstance().getStringKeyValue(Constants.KEY_TIME_ENABLE_CONFIG);
        }
        if(_TIME_ENABLE_CONFIG == null){
            _TIME_ENABLE_CONFIG=Constants.TIME_ENABLE_CONFIG_DEFAULT;
        }
        return _TIME_ENABLE_CONFIG;
    }

    public static void setTimeEnableConfig(String TimeEnableConfig) {
        _TIME_ENABLE_CONFIG = TimeEnableConfig;
    }

    public static int getDELV_ITMNO_INCREMENT() {
        if (_DELV_ITMNO_INCREMENT == 0)
            _DELV_ITMNO_INCREMENT = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_DELV_ITMNO_INCREMENT);
        if (_DELV_ITMNO_INCREMENT == 0)
            _DELV_ITMNO_INCREMENT = Constants.DELV_ITMNO_INCREMENT_DEFAULT;
        return _DELV_ITMNO_INCREMENT;
    }


    public static int getPalLimit() {
        if(_PAL_LIMIT==0)
            _PAL_LIMIT = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_PAL_LIMIT);
        if(_PAL_LIMIT==0)
            _PAL_LIMIT = Constants.PAL_LIMIT_DEFAULT;
        return _PAL_LIMIT;
    }

    public static void setPalLimit(int value) {
        _PAL_LIMIT = value;
    }

    public static void setDELV_ITMNO_INCREMENT(int value) {
        _DELV_ITMNO_INCREMENT = value;
    }

    public static String getAccQueries() {
        return ACC_QUERIES;
    }

    public static void setAccQueries(String accQueries) {
        ACC_QUERIES = accQueries;
    }

    public static String getAccClerk() {
        return ACC_CLERK;
    }

    public static void setAccClerk(String accClerk) {
        ACC_CLERK = accClerk;
    }

    public static String getCustomerRating() {
        if(_CUSTOMER_RATING == null){
            _CUSTOMER_RATING=CustomizingController.getInstance().getStringKeyValue(Constants.KEY_TIME_ENABLE_CONFIG);
        }
        if(_CUSTOMER_RATING == null){
            _CUSTOMER_RATING=Constants.KEY_CUSTOMER_RATING;
        }
        return _CUSTOMER_RATING;
    }

    public static void setCustomerRating(String value) {
        _CUSTOMER_RATING = value;
    }


    public static void cacheCasesPerPallet(){
        List<MATERIAL_HEADER> materialHeaderList = MaterialController.getInstance().getAllMaterialHeaders();
        if(materialHeaderList==null || materialHeaderList.size()<=0){
            CasesPerPallet=null;
            return;
        }

        if(CasesPerPallet!=null){
            CasesPerPallet.clear();
        }else {
            CasesPerPallet = new HashMap<>();
        }

        for (MATERIAL_HEADER material_header : materialHeaderList){
            CasesPerPallet.put(material_header.getMAT_NO(),material_header.getCS_PER_PAL().intValue());
        }

    }
}
