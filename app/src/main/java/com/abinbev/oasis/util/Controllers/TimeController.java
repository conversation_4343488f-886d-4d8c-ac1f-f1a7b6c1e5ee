package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TIME;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import static com.abinbev.oasis.util.Controllers.DBHelper.IsTimeCaptureEnabled;


/**
 * Created by kunchok on 29/01/2021
 */
public class TimeController {

    public static void createNewTime(String objectId, String timeType) {
        if (IsTimeCaptureEnabled(timeType)) {
            TIME timeRow;
            SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
            if (shipment_header != null) {
                timeRow = getTimeRow(shipment_header.getSHIP_NO(), objectId, timeType);
                if (timeRow == null) {
                    try {
                        timeRow = new TIME();
                        timeRow.setFid(shipment_header.getLid());
                        timeRow.setID(objectId);
                        timeRow.setLid(timeRow.getLid());
                        timeRow.setSHIP_NO(shipment_header.getSHIP_NO());
                        timeRow.setTYPE(timeType);
                        timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                        DBHelper.getInstance().insertOrUpdateTime(timeRow);
                    } catch (DBException e) {
                        Logger.e("", e);
                    }
                } else {
                    timeRow.setTIME(Utils.get_UTC_DatetimeAsString());
                    DBHelper.getInstance().insertOrUpdateTime(timeRow);
                }
            }
        }
    }

    private static TIME getTimeRow(String ship_no, String objectId, String timeType) {
        List<TIME> timeDataTable = new ArrayList<>();
        String query =  TIME.FIELD_SHIP_NO + " = '" + ship_no + "' AND " + TIME.FIELD_ID + " = '"+objectId+"' AND " + TIME.FIELD_TYPE + " = '"+timeType+"'";
        timeDataTable = DBHelper.getInstance().getTimeRowByQuery(query);

        if (timeDataTable != null && timeDataTable.size() > 0)
            return timeDataTable.get(0);
        else
            return null;
    }
}
