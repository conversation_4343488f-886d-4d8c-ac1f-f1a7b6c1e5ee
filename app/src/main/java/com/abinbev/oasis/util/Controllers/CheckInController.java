package com.abinbev.oasis.util.Controllers;

import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.util.Constants;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CheckInController {
    private static CheckInController checkInController = null;
  //  private IDataManager iDataManager = null;

    CheckInController() {
//        try {
//            iDataManager = ApplicationManager.getInstance().getDataManager();
//        } catch (DBException e) {
//            Logger.e("", e);
//        }
    }

    public static CheckInController getInstance() {
        if (checkInController == null) {
            checkInController = new CheckInController();
        }
        return checkInController;
    }

    public List<STOCK> getStockMaterials(String type) {
        List<STOCK> stockList = new ArrayList<>();
        String whrClause = "";
        if(type.isEmpty() || type==null){
            whrClause = "SHIP_NO = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "'";
        }else if(type.equals(Constants.RCR_TYPE)){
            whrClause = "SHIP_NO = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND TYPE = '" + type + "'";
        }else {
            whrClause = "SHIP_NO = '" + DBHelper.getInstance().getShipmentRow().getSHIP_NO() + "' AND TYPE <> '" + Constants.RCR_TYPE + "'";
        }
        try {
            IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(STOCK.TABLE_NAME,whrClause);

            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    stockList.add((STOCK) structure);
                }
            }
        } catch (DBException e) {
            Logger.e("", e);
            return null;
        }
        if(stockList.size()>0){
            return stockList;
        }else {
            return null;
        }
    }

    public void updateStock(STOCK header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public void insertOrUpdateStock(STOCK header) {
        try {
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(header);
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    public boolean checkDiscrepancy(List<STOCK> stockList) {

        if(stockList!=null && stockList.size()>0  ) {
            for (STOCK stock : stockList) {
                if (isDummyMaterial(stock.getMAT_NO())) {
                    continue;
                }
                if ((stock.getACT_QTY() == null ? 0 : stock.getACT_QTY()) != (stock.getCHCK_IN_QTY() == null ? 0 : stock.getCHCK_IN_QTY())) {
                    return false;
                }
//                if(stock.getCHCK_IN_QTY()!=null && stock.getCHCK_IN_QTY()>0){
//                    return false;
//                }
            }
        }else {
            return false;
        }
        return true;
    }

    public boolean isDummyMaterial(String mat_no) {
        String [] matNos = MaterialController.getInstance().getDummyMaterialNos();
        return matNos!=null && Arrays.asList(matNos).contains(mat_no);
    }
}
