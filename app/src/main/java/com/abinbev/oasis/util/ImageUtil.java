package com.abinbev.oasis.util;

import static android.graphics.BitmapFactory.decodeFile;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;

import com.itextpdf.text.Image;
import com.unvired.logger.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

public class ImageUtil {


    public static Bitmap getBitmapFromBase64(String base64Str) throws IllegalArgumentException {
        byte[] decodedBytes = Base64.decode(base64Str.substring(base64Str.indexOf(",") + 1), Base64.NO_WRAP);
        return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
    }

    public static String getBase64FromBitmap(Bitmap bitmap) {
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        bitmap = getResizedBitmap(bitmap,300);
//        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
//        return Base64.encodeToString(outputStream.toByteArray(), Base64.DEFAULT);

        Bitmap immagex = (bitmap);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        immagex.compress(Bitmap.CompressFormat.PNG, 100, baos);
        byte[] b = baos.toByteArray();
        String imageEncoded = Base64.encodeToString(b, Base64.NO_WRAP);
        return imageEncoded;
    }

    public static String getBase64FromBitmapInJpeg(Bitmap bitmap) {
        Bitmap immagex = (bitmap);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        immagex.compress(Bitmap.CompressFormat.JPEG, 100, baos);
        byte[] b = baos.toByteArray();
        String imageEncoded = Base64.encodeToString(b, Base64.NO_WRAP);
        return imageEncoded;
    }

    public static Bitmap compressBitmapTOJpeg(Bitmap bitmap) {
        Bitmap immagex = (bitmap);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        immagex.compress(Bitmap.CompressFormat.JPEG, 100, baos);
        byte[] b = baos.toByteArray();
        return BitmapFactory.decodeByteArray(b, 0, b.length);
//        return  getBitmapFromBase64(imageEncoded);
    }

    public static String getFileUri() {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        String imageFileName = timeStamp + ".jpg";

        File storageDir = Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_PICTURES);

        String pictureImagePath = storageDir.getAbsolutePath() + "/" + imageFileName;


        return  pictureImagePath;
    }

    public static String saveCapturedImage(Bitmap bitmap) {
        FileOutputStream fout = null;
        File file = null;
        byte[] b;
        try {
            File dir = new File(String.valueOf(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)));
            if (!dir.exists()) {
                dir.mkdirs();
            }
            file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), String.valueOf(System.currentTimeMillis()) + ".jpg");
            if (!file.exists()) {
                file.createNewFile();
            }
            fout = new FileOutputStream(file);
            if(bitmap!=null){
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fout);
            }

            fout.flush();
            fout.close();
        } catch (FileNotFoundException e) {
            Logger.e("", e);
        } catch (IOException e) {
            Logger.e("", e);
        }

        return file.getAbsolutePath();
    }

    public static Bitmap compressImageAndSave(String path, Context context) {

        Bitmap compressed = compress(path);
        FileOutputStream fout1 = null;
        File file1 = null;
        byte[] b1;
        try {
            File dir = new File(context.getFilesDir() + File.separator + "PODsAndClaims");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            file1 = new File(context.getFilesDir() + File.separator + "PODsAndClaims", String.valueOf(System.currentTimeMillis()) + ".jpg");
            if (!file1.exists()) {
                file1.createNewFile();
            }
            fout1 = new FileOutputStream(file1);
            compressed.compress(Bitmap.CompressFormat.JPEG, 95, fout1);
            fout1.flush();
            fout1.close();
        } catch (FileNotFoundException e) {
            Logger.e("", e);
        } catch (IOException e) {
            Logger.e("", e);
        }
        return decodeFile(file1.getAbsolutePath());
    }

    public static Bitmap getResizedBitmap(Bitmap image, int maxSize) {
        int width = image.getWidth();
        int height = image.getHeight();

        float bitmapRatio = (float) width / (float) height;
        if (bitmapRatio > 1) {
            width = maxSize;
            height = (int) (width / bitmapRatio);
        } else {
            height = maxSize;
            width = (int) (height * bitmapRatio);
        }
        return Bitmap.createScaledBitmap(image, width / 2, height / 2, true);
    }

    public static Bitmap getScaledBitmapBasedOnScreenSize(Bitmap bitmap, Activity activity) {
        DisplayMetrics display = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(display);
        int screenWidth = display.widthPixels;
        int screenHeight = display.heightPixels;
        return Bitmap.createScaledBitmap(bitmap, screenWidth / 2, screenHeight / 2, false);
    }

    public static Bitmap trimBitmap(Bitmap bmp) {
        int imgHeight = bmp.getHeight();
        int imgWidth = bmp.getWidth();


        //TRIM WIDTH - LEFT
        int startWidth = bmp.getWidth();
//        for(int x = 0; x < imgWidth; x++) {
//            if (startWidth == 0) {
//                for (int y = 0; y < imgHeight; y++) {
//                    if (bmp.getPixel(x, y) != Color.TRANSPARENT) {
//                        startWidth = x;
//                        break;
//                    }
//                }
//            } else break;
//        }


        //TRIM WIDTH - RIGHT
        int endWidth = bmp.getWidth();
//        for(int x = imgWidth - 1; x >= 0; x--) {
//            if (endWidth == 0) {
//                for (int y = 0; y < imgHeight; y++) {
//                    if (bmp.getPixel(x, y) != Color.TRANSPARENT) {
//                        endWidth = x;
//                        break;
//                    }
//                }
//            } else break;
//        }


        //TRIM HEIGHT - TOP
        int startHeight = 0;
//        for(int y = 0; y < imgHeight; y++) {
//            if (startHeight == 0) {
//                for (int x = 0; x < imgWidth; x++) {
//                    if (bmp.getPixel(x, y) != Color.TRANSPARENT) {
//                        startHeight = y;
//                        break;
//                    }
//                }
//            } else break;
//        }


        //TRIM HEIGHT - BOTTOM
        int endHeight = 0;
        for (int y = imgHeight - 1; y >= 0; y--) {
            if (endHeight == 0) {
                for (int x = 0; x < imgWidth; x++) {
                    if (bmp.getPixel(x, y) != Color.TRANSPARENT) {
                        endHeight = y;
                        break;
                    }
                }
            } else break;
        }


        return Bitmap.createBitmap(
                bmp,
                startWidth,
                startHeight,
                endWidth - startWidth,
                endHeight - startHeight
        );

    }

    public static Bitmap merge(Bitmap driver, Bitmap customer, int spacing) {
        Bitmap cs;
        int width, height;

        if (driver.getWidth() > customer.getWidth()) {
            width = driver.getWidth() + customer.getWidth();
        } else {
            width = customer.getWidth() + customer.getWidth();
        }
        height = driver.getHeight();

        cs = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);

        Canvas comboImage = new Canvas(cs);
        float space = 60f;
        comboImage.drawBitmap(driver, 0f, 0f, null);
        comboImage.drawBitmap(customer, driver.getWidth() + space, 0f, null);

        return cs;
    }

    public static byte[] getByteFromBitmap(Bitmap combinedSignatureImage) {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        combinedSignatureImage.compress(Bitmap.CompressFormat.PNG, 100, stream);
        byte[] byteArray = stream.toByteArray();
        combinedSignatureImage.recycle();
        return byteArray;
    }

    public static Bitmap getBWBitmap(Bitmap selectedImageBitmap) {

        int width = selectedImageBitmap.getWidth();
        int height = selectedImageBitmap.getHeight();
        // create output bitmap
        Bitmap bmOut = Bitmap.createBitmap(width, height, selectedImageBitmap.getConfig());
        // color information
        int A, R, G, B;
        int pixel;
        for (int x = 0; x < width; ++x) {
            for (int y = 0; y < height; ++y) {
                // get pixel color
                pixel = selectedImageBitmap.getPixel(x, y);
                A = Color.alpha(pixel);
                R = Color.red(pixel);
                G = Color.green(pixel);
                B = Color.blue(pixel);
                int gray = (int) (0.2989 * R + 0.5870 * G + 0.1140 * B);
                // use 128 as threshold, above -> white, below -> black
                if (gray > 128) {
                    gray = 255;
                } else {
                    gray = 0;
                }
                // set new pixel color to output bitmap
                bmOut.setPixel(x, y, Color.argb(A, gray, gray, gray));
            }
        }
        return bmOut;
    }

    public static Bitmap toGrayscale(Bitmap srcImage) {

        Bitmap bmpGrayscale = Bitmap.createBitmap(srcImage.getWidth(), srcImage.getHeight(), Bitmap.Config.ARGB_8888);

        Canvas canvas = new Canvas(bmpGrayscale);
        Paint paint = new Paint();

        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0);
        paint.setColorFilter(new ColorMatrixColorFilter(cm));
        canvas.drawBitmap(srcImage, 0, 0, paint);

        return bmpGrayscale;
    }

    public static Bitmap createContrast(Bitmap src, double value) {
        // image size
        int width = src.getWidth();
        int height = src.getHeight();
        // create output bitmap
        Bitmap bmOut = Bitmap.createBitmap(width, height, src.getConfig());
        // color information
        int A, R, G, B;
        int pixel;
        // get contrast value
        double contrast = Math.pow((100 + value) / 100, 2);

        // scan through all pixels
        for (int x = 0; x < width; ++x) {
            for (int y = 0; y < height; ++y) {
                // get pixel color
                pixel = src.getPixel(x, y);
                A = Color.alpha(pixel);
                // apply filter contrast for every channel R, G, B
                R = Color.red(pixel);
                R = (int) (((((R / 255.0) - 0.5) * contrast) + 0.5) * 255.0);
                if (R < 0) {
                    R = 0;
                } else if (R > 255) {
                    R = 255;
                }

                G = Color.red(pixel);
                G = (int) (((((G / 255.0) - 0.5) * contrast) + 0.5) * 255.0);
                if (G < 0) {
                    G = 0;
                } else if (G > 255) {
                    G = 255;
                }

                B = Color.red(pixel);
                B = (int) (((((B / 255.0) - 0.5) * contrast) + 0.5) * 255.0);
                if (B < 0) {
                    B = 0;
                } else if (B > 255) {
                    B = 255;
                }

                // set new pixel color to output bitmap
                bmOut.setPixel(x, y, Color.argb(A, R, G, B));
            }
        }

        return bmOut;
    }


    public static Bitmap compress(String filePath) {

        BitmapFactory.Options o = new BitmapFactory.Options();
        o.inJustDecodeBounds = true;
        o.inScaled = false;
        BitmapFactory.decodeFile(filePath, o);

        // The new size we want to scale to
        final int REQUIRED_SIZE = 2048;

        // Find the correct scale value. It should be the power of 2.
        int width_tmp = o.outWidth, height_tmp = o.outHeight;
        int scale = 1;
        while (true) {
            if (width_tmp < REQUIRED_SIZE && height_tmp < REQUIRED_SIZE)
                break;
            width_tmp /= 2;
            height_tmp /= 2;
            scale *= 2;
        }

        // Decode with inSampleSize
        BitmapFactory.Options o2 = new BitmapFactory.Options();
        o2.inSampleSize = scale;
        Bitmap image = BitmapFactory.decodeFile(filePath, o2);

        ExifInterface exif;
        try {
            exif = new ExifInterface(filePath);
            int exifOrientation = exif.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL);

            int rotate = 0;
            switch (exifOrientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    rotate = 90;
                    break;

                case ExifInterface.ORIENTATION_ROTATE_180:
                    rotate = 180;
                    break;

                case ExifInterface.ORIENTATION_ROTATE_270:
                    rotate = 270;
                    break;
            }

            if (rotate != 0) {
                int w = image.getWidth();
                int h = image.getHeight();

                // Setting pre rotate
                Matrix mtx = new Matrix();
                mtx.preRotate(rotate);

                // Rotating Bitmap & convert to ARGB_8888, required by tess
                image = Bitmap.createBitmap(image, 0, 0, w, h, mtx, false);

            }
        } catch (IOException e) {
            return null;
        }
        return image.copy(Bitmap.Config.ARGB_8888, true);
    }

    public static Bitmap compressedImage(Bitmap bitImage){
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitImage.compress(Bitmap.CompressFormat.JPEG, 100, baos);// 100baos
        int options = 100;
        while (baos.toByteArray().length / 1024 > 100) { // 100kb,
            baos.reset();// baosbaos
            bitImage.compress(Bitmap.CompressFormat.JPEG, options, baos);// options%baos
            options -= 10;// 10
        }
        ByteArrayInputStream isBm = new ByteArrayInputStream(
                baos.toByteArray());// baosByteArrayInputStream
        Bitmap bitmap = BitmapFactory.decodeStream(isBm, null, null);// ByteArrayInputStream
        return bitmap;
    }

    public static Bitmap drawBitmapImages(ArrayList<Bitmap> bitmapArrayList){
        int width = 0;
        int height = 0;
        Bitmap result = null;

        try{
            for(Bitmap bitmap : bitmapArrayList) {
                width = Math.max(width, bitmap.getWidth());
                height += bitmap.getHeight();
            }

            result = Bitmap.createBitmap(width,height,Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(result);
            canvas.drawColor(Color.TRANSPARENT);

            int top = 0;
            // Draw each bitmap on the canvas
            for (Bitmap bitmap : bitmapArrayList) {
                int left = (width - bitmap.getWidth()) / 2; // Center horizontally
                canvas.drawBitmap(bitmap, left, top, null);
                top += bitmap.getHeight(); // Move top position down for next bitmap
            }

        }catch (Exception e){
            Logger.e("", e);
        }

        return result;
    }

    public static Bitmap decodeUri(Context c, Uri uri, final int requiredSize)
            throws FileNotFoundException {
        BitmapFactory.Options o = new BitmapFactory.Options();
        o.inJustDecodeBounds = true;
        BitmapFactory.decodeStream(c.getContentResolver().openInputStream(uri), null, o);

        int width_tmp = o.outWidth
                , height_tmp = o.outHeight;
        int scale = 1;

        while(true) {
            if(width_tmp / 2 < requiredSize || height_tmp / 2 < requiredSize)
                break;
            width_tmp /= 2;
            height_tmp /= 2;
            scale *= 2;
        }

        BitmapFactory.Options o2 = new BitmapFactory.Options();
        o2.inSampleSize = scale;
        return BitmapFactory.decodeStream(c.getContentResolver().openInputStream(uri), null, o2);
    }

    public static Uri getImageUri(Context inContext, Bitmap inImage) {
        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
        inImage.compress(Bitmap.CompressFormat.JPEG, 100, bytes);
        String path = MediaStore.Images.Media.insertImage(
                inContext.getContentResolver(), inImage, "IMG_" + System.currentTimeMillis(), null
        );
        return Uri.parse(path);
    }

    public static Bitmap getBitmapFromUri(Context context, Uri uri) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(uri);
            return BitmapFactory.decodeStream(inputStream);
        } catch (IOException e) {
            Logger.e("", e);
            return null;
        }
    }


}
