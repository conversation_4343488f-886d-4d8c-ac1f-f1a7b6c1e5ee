package com.abinbev.oasis.util;

import com.google.android.material.snackbar.Snackbar;
import com.abinbev.oasis.R;

import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

public class SnackBarToast {

    public static void showMessage(View mView, String message) {
//        final Snackbar snackbar =Snackbar.make(mView, message, Snackbar.LENGTH_LONG);
//                snackbar.setAction("Ok", new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        snackbar.dismiss();
//                    }
//                }).show();

        Toast toast = Toast.makeText(mView.getContext(), message, Toast.LENGTH_SHORT);
        View customView = toast.getView();
        customView.getBackground().setColorFilter(mView.getContext().getResources().getColor(R.color.toast_color), PorterDuff.Mode.SRC_IN);
        TextView text = customView.findViewById(android.R.id.message);
        text.setTextColor(mView.getContext().getResources().getColor(R.color.white));
        text.setTypeface(null, Typeface.BOLD);
        toast.setGravity(Gravity.CENTER, 0, -300);
        toast.show();
        Handler handler = new Handler();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                toast.cancel();
            }
        }, 1000);
    }

    public static void showMessage(Context mView, String message) {
//        final Snackbar snackbar =Snackbar.make(mView, message, Snackbar.LENGTH_LONG);
//                snackbar.setAction("Ok", new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        snackbar.dismiss();
//                    }
//                }).show();

        Toast toast = Toast.makeText(mView, message, Toast.LENGTH_SHORT);
        View customView = toast.getView();
        customView.getBackground().setColorFilter(mView.getResources().getColor(R.color.depot_list_item_background), PorterDuff.Mode.SRC_IN);
        TextView text = customView.findViewById(android.R.id.message);
        text.setTextColor(mView.getResources().getColor(R.color.white));
        text.setTypeface(null, Typeface.BOLD);
        toast.setGravity(Gravity.CENTER, 0, -300);
        toast.show();
        Handler handler = new Handler();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                toast.cancel();
            }
        }, 1000);
    }
    public static void showMessage(View view, String message,String actionLable) {
        final Snackbar snackbar =Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        snackbar.setAction(actionLable, v -> snackbar.dismiss()).show();
    }

    public static void showMessageWithAction(View view, String message, final SnackBarActionListner snackBarActionListner, String buttonMsg, final String flag){
        final Snackbar snackbar =Snackbar.make(view, message, Snackbar.LENGTH_LONG);
                snackbar.setAction(buttonMsg, v -> {
                    snackbar.dismiss();
                    snackBarActionListner.onSnackBarAction(flag);
                })
                .show();
    }




    public interface SnackBarActionListner{
        public void onSnackBarAction(String flag);
    }
}
