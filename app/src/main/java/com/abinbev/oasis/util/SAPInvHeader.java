package com.abinbev.oasis.util;

import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.unvired.logger.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import static com.abinbev.oasis.util.Controllers.InvoiceController.formatMessage;


/**
 * Created by kunchok on 28/01/2021
 */
public class SAPInvHeader {
    //Left Block
    public String custName;
      int lenCustName = -54;
    public   String legalEntityLabel = "Sold to: ";
     String heading;
     public String legalEntAccountNo;
     public  int lenLegalEntAccountNo = -45;

     String accountStatusID;
    static int lenAccountStatusID = 6;

    public String creditControlLabel = "Accounting Clerk:";


    public String invoiceLabel = "Invoice Number:             ";


    public String creditControl1;
    public String creditControl2;
    public int lenCreditControl = 21;


    public String custAddress1;
    public int lenCustAddress1 = -54;
    public String custAddress2;
    public int lenCustAddress2 = -54;
     public String custAddress3;
    public int lenCustAddress3 = -54;
    public String custAddress4;
    public int lenCustAddress4 = -54;
    public String custAddress5;
    public int lenCustAddress5 = -54;

    public String deliveredToLabel = "Ship to: ";
    public String deliveryAddress1;
    public int lenDeliveryAddress1 = -54;
    public String deliveryAddress2;
    public int lenDeliveryAddress2 = -54;
    public String deliveryAddress3;
    public int lenDeliveryAddress3 = -54;
    public String deliveryAddress4;
    public int lenDeliveryAddress4 = -54;
     String deliveryAddress5;
    public int lenDeliveryAddress5 = -54;

    //Right Block
    public String invDateLabel = "Date & Time:       ";
    public String invDate;
    public static int lenInvDate = 19;
    public String accountNoLabel = "Account No:                 ";
    public String accountNo;
    public int lenAccountNo = 10;
    public String bankingRefLabel = "Customer banking ref:";
    public String bankingRef;
    public int lenBankingRef = 17;
    public String custOrderNoLabel = "Customer Order No:";
    public String custOrderNo;
    public int lenCustOrderNo = 18;
    public String invoiceNoLabel = "Invoice No :                   ";
    public String invoiceNo;
    public int lenInvoiceNo = 12;
    public String SABOrderNoLabel = "SAB Order No:               ";
    public  String SABOrderNo;
    public int lenSABOrderNo = 10;
    public String deliveryNoLabel = "Delivery No:                ";
    public String deliveryNo;
    public int lenDeliveryNo = 10;

    public String shipmentNoLabel = "Shipment No:                ";
    public String shipmentNo;
    public int lenShipmentNo = 10;

    public String depotLabel = "Home Plant:    ";
    public String depot;
    public int lenDepot = 23;
    public String depotAddress;
    public int lenDepotAddress = 38;
    public String faxTelLabel = "Fax No:                ";
    public String faxTel;
    public int lenFaxTel = 15;
    public String ordersTelLabel = "Orders tel no:         ";
    public String ordersTel;
    public int lenOrdersTel = 15;
    public String accQueriesLabel = "Acc Queries:           ";
    public String accQueries;
    public int lenAccQueries = 15;
    public String accManagerLabel = "Acc Manager:          ";
    public String accManager;
    public int lenAccManager = 15;
    public String accCellNoLabel = "Telephone:                ";
    public String sAccCellNo;

    //Centre Block
    //public String paymentTermsLabel = "Payment terms:";
    public String paymentTermsMessageLine1;
    public int lenPaymentTermsMessageLine1 = -52;
    public String paymentTermsMessageLine2;
    public int lenPaymentTermsMessageLine2 = -52;
    public String paymentTermsMessageLine3;
    public int lenPaymentTermsMessageLine3 = -52;
    public String paymentTermsMessageLine4;
    public int lenPaymentTermsMessageLine4 = -52;
    public String paymentTermsMessageLine5;
    public int lenPaymentTermsMessageLine5 = -52;
    public String payByDate;
    public int lenPayByDate = 12;
    public String payTerm;
    public int lenPayTerm = -4;
    public String emptiesMessageLabel1 = "Subject to any terms of trade, and the";
    public int lenEmptiesMessageLabel1 = -53;
    public String emptiesMessageLabel2 = "disclaimer below, returned and credited";
    public String emptiesMessageLabel3 = "empties may be deducted.";
    public String payMethodLabel = "Payment Method:";
    public String payMethod;
    public int lenPayMethod = 23;
    public String custVATNoLabel = "Customer VAT No:";
    public String custVATNo;
    public int lenCustVATNo = 22;
    public String liquorLicenceNoLabel = "Liq Lic No:";
    public  String liquorLicenceNo;
    public int lenLiquorLicenceNo = 26;
    //public int lenLiquorLicenceNo = 19;
    public String deliveredFromLabel = "Issuing Plant:";
    public String issueDepot;
    public int lenIssueDepot = 32;

    public String[] getSplitAddress(String addressToSplit)
        {
            String[] billToAddressSplit = new String[] { "", "", "", "" };

            if (Utils.isNullOrEmpty(addressToSplit))
            {
                return null;
            }

            String[] addressToSplitSplit = addressToSplit.split(",");

            if (addressToSplitSplit != null && addressToSplitSplit.length > 0)
            {
                for (int i = 0; i < addressToSplitSplit.length; i++)
                {
                    String splitAdd = Utils.isNullOrEmpty(addressToSplitSplit[i]) ? "" : addressToSplitSplit[i];

                    if (i == 0)
                    {
                        billToAddressSplit[0] = splitAdd;

                        continue;
                    }
                    else if (i == 1)
                    {
                        if (Utils.isNullOrEmpty(billToAddressSplit[0]))
                            billToAddressSplit[0] = splitAdd;
                        else
                            billToAddressSplit[1] = splitAdd;

                        continue;

                    }
                    else
                    {
                        if (Utils.isNullOrEmpty(billToAddressSplit[0]))
                            billToAddressSplit[0] = splitAdd;
                        else if (Utils.isNullOrEmpty(billToAddressSplit[1]))
                            billToAddressSplit[1] = splitAdd;
                        else if (Utils.isNullOrEmpty(billToAddressSplit[2]))
                            billToAddressSplit[2] = splitAdd;
                        else
                            billToAddressSplit[2] = billToAddressSplit[2] + splitAdd; ;

                        continue;
                    }
                }
            }
            else
            {
                billToAddressSplit[0] = addressToSplit;
            }
            return billToAddressSplit;

    }
    public static void setPaymentTermsMessages(SAPInvHeader InvoiceHeader, String paymentTerm, String baseLineDate) {

                final String CUSTOMIZATION_INVH_LBL = "NINVH_";
        final String CUSTOMIZATION_INVF_LBL = "NINVF_";
        final String PAYMENT_TERM_DAY_LBL = "DAY";
        final String PAYMENT_TERM_MSG_LBL = "MSG";


            if (!Utils.isNullOrEmpty(paymentTerm) && !Utils.isNullOrEmpty(baseLineDate))
            {
                List<CUSTOMIZATION_HEADER> invoiceFooterKeysString = DBHelper.getInstance().getInvoiceKeys("NINV");
                if (invoiceFooterKeysString != null && invoiceFooterKeysString.size() > 0)
                {

                    List<CUSTOMIZATION_HEADER> PaymentTermMsgDays= new ArrayList<CUSTOMIZATION_HEADER>();
                    List<CUSTOMIZATION_HEADER> PaymentTermHeaderMsg= new ArrayList<CUSTOMIZATION_HEADER>();



                    for(CUSTOMIZATION_HEADER header:invoiceFooterKeysString){
                        if(header.getKEY_NAME().contains(CUSTOMIZATION_INVF_LBL + paymentTerm + "_" + PAYMENT_TERM_DAY_LBL)){
                            PaymentTermMsgDays.add(header);
                        }
                    }
                    for(CUSTOMIZATION_HEADER header:invoiceFooterKeysString){
                        if(header.getKEY_NAME().contains(CUSTOMIZATION_INVH_LBL + paymentTerm + "_" + PAYMENT_TERM_MSG_LBL)){
                            PaymentTermHeaderMsg.add(header);
                        }
                    }

                    if (PaymentTermMsgDays != null && PaymentTermMsgDays.size() > 0 &&
                            PaymentTermHeaderMsg != null && PaymentTermHeaderMsg.size() > 0)
                    {
                        CUSTOMIZATION_HEADER customer_header= PaymentTermHeaderMsg.get(0);

                        if (!Utils.isNullOrEmpty(customer_header.getKEY_DESC()))
                        {
                            String paymentTermMsgBuilder = customer_header.getKEY_DESC();

                            //Format the message as per new requirement
                            String[] paymentTermMsgs = formatMessage(paymentTermMsgBuilder);
                            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
                            SimpleDateFormat outputFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH);
                            Date baseLineDateTime = null;
                            try {
                                baseLineDateTime = format.parse(baseLineDate);
                            } catch (ParseException e) {
                                Logger.e("", e);
                            }

                            int length = PaymentTermMsgDays.size();
                            int index = 0;
                                 int MaxNofMsg = 5;

                            for (int paymentTermMsgsIndex = 0; paymentTermMsgsIndex < MaxNofMsg; paymentTermMsgsIndex++)
                            {
                                if ((!Utils.isNullOrEmpty(paymentTermMsgs[paymentTermMsgsIndex]) && (paymentTermMsgs[paymentTermMsgsIndex].indexOf("DT") != -1)))
                                {
                                    if (index < length)
                                    {
                                        CUSTOMIZATION_HEADER customizationRow = PaymentTermMsgDays.get(index++);

                                        try
                                        {
                                            int NoOfDaysToBeAdded = Utils.isNullOrEmpty(customizationRow.getKEY_DESC()) ? 0 :Integer.parseInt(customizationRow.getKEY_DESC());
                                            Calendar c = Calendar.getInstance();
                                            c.setTime(baseLineDateTime);
                                            c.add(Calendar.DATE, NoOfDaysToBeAdded);
                                            //baseLineDateTime = c.getTime();
                                            paymentTermMsgs[paymentTermMsgsIndex] = paymentTermMsgs[paymentTermMsgsIndex].replace("DT", outputFormat.format(c.getTime()));
                                        }
                                        catch (Exception e)
                                        {
                                            Logger.e("", e);
                                        }
                                        continue;
                                    }
                                    break;
                                }
                            }

                            setPaymentTermMsgs(InvoiceHeader, paymentTermMsgs, MaxNofMsg);
                        }
                    }
                }
            }

    }

    private static void setPaymentTermMsgs(SAPInvHeader InvoiceHeader,String[] paymentTermMsgs, int maxCountOfMsgs)
    {

        InvoiceHeader.paymentTermsMessageLine1 = InvoiceHeader.paymentTermsMessageLine2 = InvoiceHeader.paymentTermsMessageLine3 = InvoiceHeader.paymentTermsMessageLine4 = InvoiceHeader.paymentTermsMessageLine5 = "";


        for (int index = 0; index < maxCountOfMsgs; index++)
        {
            if (!Utils.isNullOrEmpty(paymentTermMsgs[index]))
            {

                switch (index)
                {
                    case 0:
                        InvoiceHeader.paymentTermsMessageLine1 = paymentTermMsgs[index];
                        break;

                    case 1:
                        InvoiceHeader.paymentTermsMessageLine2 = paymentTermMsgs[index];
                        break;

                    case 2:
                        InvoiceHeader.paymentTermsMessageLine3 = paymentTermMsgs[index];
                        break;

                    case 3:
                        InvoiceHeader.paymentTermsMessageLine4 = paymentTermMsgs[index];
                        break;

                    case 4:
                        InvoiceHeader.paymentTermsMessageLine5 = paymentTermMsgs[index];
                        break;
                }
            }
        }
    }
}
