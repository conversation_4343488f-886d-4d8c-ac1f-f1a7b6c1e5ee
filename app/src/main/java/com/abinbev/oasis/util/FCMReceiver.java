package com.abinbev.oasis.util;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;

import com.abinbev.oasis.activities.StartUpActivity;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.unvired.fcm.UnviredFCMMessagingService;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;
import com.unvired.sync.in.NotificationBO;

/**
 * Follow FCM integration guide and extend UnviredFCMMessagingService.
 * Override onCallBack() to get Notification object from Unvired Library
 */

public class FCMReceiver extends UnviredFCMMessagingService {

    @Override
    public void onCallBack(NotificationBO notificationBO) {
        showNotification(notificationBO);
    }

    private void showNotification(NotificationBO notificationBO) {
        Context context = getApplicationContext();

        try {

            // Query the package manager for the best launch intent for the app
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (intent == null) {
                intent = new Intent();
            } else {
                intent.setPackage(null);
            }

            final NotificationManager nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            PendingIntent contentIntent = PendingIntent.getActivity(context, 100, intent, PendingIntent.FLAG_CANCEL_CURRENT);
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, "CHANNEL_ID");
            builder.setContentIntent(contentIntent)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setColor(Color.GREEN)
                    .setWhen(System.currentTimeMillis())
                    .setAutoCancel(true)
                    .setLights(Color.GREEN, 500, 500)
                    .setContentTitle(notificationBO.getTitle())
                    .setStyle((new NotificationCompat.BigTextStyle()).bigText(notificationBO.getAlert()))
                    .setContentText(notificationBO.getAlert())
                    .setPriority(Notification.PRIORITY_MAX);

            if (Build.VERSION.SDK_INT >= 26) {
                nm.createNotificationChannel(this.getNotificationChannel(context));
            }

            nm.notify(100, builder.build());
        } catch (Exception var6) {
            Logger.e("", var6);
        }

    }

    @RequiresApi(
            api = 26
    )
    private NotificationChannel getNotificationChannel(Context context) {
        NotificationChannel mChannel = new NotificationChannel("CHANNEL_ID", "CHANNEL_ID", NotificationManager.IMPORTANCE_HIGH);
        mChannel.setDescription("CHANNEL_ID");
        mChannel.enableLights(true);
        mChannel.setLightColor(Color.GREEN);
        mChannel.enableVibration(true);
        mChannel.setVibrationPattern(new long[]{100L, 200L, 300L, 400L, 500L, 400L, 300L, 200L, 400L});
        return mChannel;
    }

}