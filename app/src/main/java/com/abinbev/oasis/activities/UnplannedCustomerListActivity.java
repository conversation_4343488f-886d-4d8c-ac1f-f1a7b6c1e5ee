package com.abinbev.oasis.activities;

import android.os.Bundle;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.adapter.UnplannedCustomerAdapter;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.google.android.material.textfield.TextInputEditText;
import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class UnplannedCustomerListActivity extends AppCompatActivity {
    RecyclerView mRecyclerView;
    List<CUSTOMER_HEADER> customerHeaders;
    TextInputEditText mSearch;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_unplanned_customer_list);
    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        getData();
    }

    private void getData() {
        customerHeaders=new ArrayList<>();
        customerHeaders= TripSummaryController.getInstance().getAdhocCustomers();
        if(customerHeaders!=null){
            setupRecyclerview();
        }
    }

    private void iniViews()
    {
        mRecyclerView=findViewById(R.id.xUnplannedCustomerRecyclerview);
        mSearch=findViewById(R.id.xUnplannedCustomersSearchEt);
    }

    private void setupRecyclerview() {
        final UnplannedCustomerAdapter adapter = new UnplannedCustomerAdapter(this,customerHeaders);
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mRecyclerView.setAdapter(adapter);
        mSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                adapter.filter(s.toString());
            }
        });
    }
    public void navigateToSettings(View view) {
        Utils.showSettingDialog(UnplannedCustomerListActivity.this);
    }

    public void closeActivity(View view) {
        finish();
    }
}