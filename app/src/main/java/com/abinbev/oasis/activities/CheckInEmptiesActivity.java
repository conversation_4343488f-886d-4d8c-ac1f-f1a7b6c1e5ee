package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import com.abinbev.oasis.adapter.CheckInEmptiesTableAdapter;
import com.abinbev.oasis.adapter.CheckInFullsTableAdapter;
import com.abinbev.oasis.adapter.MaterialDroDownAdapter;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CheckInController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.FontAwesomeView;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class CheckInEmptiesActivity extends AppCompatActivity implements CheckInFullsTableAdapter.TableClickListner {
    private Spinner mMaterialSpinner;
    private EditText mQty;
    private ImageButton mQtyClear;
    private FontAwesomeView mMinus;
    private Button mReduce, mAdd;
    private TextView mNoDataAvailable;
    private RecyclerView mRecyclerView;


    private List<MATERIAL_HEADER> materialHeaderList;
    private List<STOCK> stockList;
    private MATERIAL_HEADER selectedMaterial = null;
    private STOCK selectedSmartGrid = null;
    CheckInEmptiesTableAdapter adapter = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_in_empties);
        iniViews();
        getData();
    }

    private void getData() {
        materialHeaderList = MaterialController.getInstance().getEmptyMaterials();
        stockList = CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE);
        if (materialHeaderList != null) {
            setUpSpinner();
        }
        loadSmartGrid();
    }

    private void loadSmartGrid() {
        List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE);
        if (list != null && list.size() > 0) {
            List<STOCK> stockListTemp = new ArrayList<>();
            for (STOCK stock : list) {
                if (stock.getCHCK_IN_QTY() != null && stock.getCHCK_IN_QTY() > 0) {
                    stockListTemp.add(stock);
                }
            }
            if (stockListTemp.size() > 0) {
                mNoDataAvailable.setVisibility(View.GONE);
                mRecyclerView.setVisibility(View.VISIBLE);
                adapter = new CheckInEmptiesTableAdapter(this, stockListTemp, this);
                mRecyclerView.setHasFixedSize(true);
                mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
                mRecyclerView.setAdapter(adapter);
            } else {
                mRecyclerView.setVisibility(View.GONE);
                mNoDataAvailable.setVisibility(View.VISIBLE);
            }
        }
    }

    private void setUpSpinner() {
        final MaterialDroDownAdapter materialDroDownAdapter = new MaterialDroDownAdapter(this, R.layout.support_simple_spinner_dropdown_item, materialHeaderList, false, true);
        mMaterialSpinner.setAdapter(materialDroDownAdapter);
        mMaterialSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedMaterial = materialHeaderList.get(position);
                materialDroDownAdapter.setSelectionPosition(position);
                List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE);
                boolean isStockAvailable=false;
                if (list != null && list.size() > 0) {
                    for (STOCK stock : list) {
                        if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                            selectedSmartGrid = stock;
                            isStockAvailable=true;
                        }
                        if (stock.getCHCK_IN_QTY() != null && stock.getCHCK_IN_QTY() > 0) {
                            if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                                if (stock.getCHCK_IN_QTY() != null && !stock.getCHCK_IN_QTY().toString().isEmpty()) {
//                                    mQty.setText(String.valueOf(stock.getCHCK_IN_QTY().intValue()));
                                    if (adapter != null) {
                                        adapter.setSelectedPos(stock);
                                    }
                                    break;
                                }
                            }
                        } else {
                            mQty.setText("");
                            if (adapter != null) {
                                adapter.resetSelectedPos();
                            }
                        }
                    }
                    if(!isStockAvailable){
                        selectedSmartGrid=null;
                    }
                } else {
                    mQty.setText("");
                    if (adapter != null) {
                        adapter.resetSelectedPos();
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    @Override
    public void onBackPressed() {

    }

    private void iniViews() {
        mMaterialSpinner = findViewById(R.id.xCheckInEmptiesMaterialSpinner);
        mQty = findViewById(R.id.xCheckInEmptiesQty);
        mQtyClear = findViewById(R.id.xCheckInEmptiesQtyClear);
        mMinus = findViewById(R.id.xCheckInEmptiesMinusIcon);
        mReduce = findViewById(R.id.xCheckInEmptiesReduceBtn);
        mAdd = findViewById(R.id.xCheckInEmptiesAddBtn);
        mNoDataAvailable = findViewById(R.id.xCheckInEmptiesNoDataAvailable);
        mRecyclerView = findViewById(R.id.xCheckInEmptiesRecyclerView);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(CheckInEmptiesActivity.this);
    }

    public void clearReturnQty(View view) {
        mQty.setText("");
    }

    @Override
    public void onTableClick(int position, STOCK stock) {
        selectedSmartGrid = stock;
        if (selectedSmartGrid != null) {
            if (selectedSmartGrid.getMAT_NO() != null && !selectedSmartGrid.getMAT_NO().isEmpty()) {
                if (materialHeaderList != null && materialHeaderList.size() > 0) {
                    if (selectedSmartGrid.getMAT_NO() == null || selectedSmartGrid.getMAT_NO().isEmpty()) {
                        selectedMaterial = null;
                        clearAll();
                        return;
                    }
//                    if(selectedSmartGrid.getCHCK_IN_QTY()!=null && !selectedSmartGrid.getCHCK_IN_QTY().toString().isEmpty()){
//                        mQty.setText(String.valueOf(selectedSmartGrid.getCHCK_IN_QTY().intValue()));
//                    }
                    for (int i = 0; i < materialHeaderList.size(); i++) {
                        if (materialHeaderList.get(i).getMAT_NO().equals(selectedSmartGrid.getMAT_NO())) {
                            mMaterialSpinner.setSelection(i);
                        }
                    }

                }
            }
        }
    }

    private void clearAll() {
        mQty.setText("");
    }

    public void minus_button_click(View view) {
        if (selectedSmartGrid != null) {
            if (selectedSmartGrid.getCHCK_IN_QTY() == null) {
                selectedSmartGrid.setCHCK_IN_QTY((double) 0);
            } else {
                selectedSmartGrid.setCHCK_IN_QTY((double) 0);
            }
            CheckInController.getInstance().updateStock(selectedSmartGrid);
        }
        clearAll();
        loadSmartGrid();
    }


    public void reduceFromStockItems(View view) {
        if (validate(view)) {
            if (selectedSmartGrid != null) {
                if (selectedSmartGrid.getCHCK_IN_QTY() == null) {
                    selectedSmartGrid.setCHCK_IN_QTY((double) 0);
                } else {
                    selectedSmartGrid.setCHCK_IN_QTY((selectedSmartGrid.getCHCK_IN_QTY() - Integer.parseInt(mQty.getText().toString())) <= 0 ? 0 : (selectedSmartGrid.getCHCK_IN_QTY() - Integer.parseInt(mQty.getText().toString())));
                    CheckInController.getInstance().updateStock(selectedSmartGrid);
                    loadSmartGrid();
                }
            }
            updateSelectedStock();
            clearAll();
        }
    }

    private boolean validate(View view) {
        if (selectedMaterial == null) {
            SnackBarToast.showMessage(view, "Please select a Product");
            return false;
        }

        if (mQty.getText().toString().isEmpty()) {
            SnackBarToast.showMessage(view, "Please enter a Quantity");
            return false;
        }
        int value = 0;
        try {
            value = Integer.parseInt(mQty.getText().toString());
        } catch (Exception e) {

            SnackBarToast.showMessage(view, "Please enter a valid quantity");
            return false;
        }

        if (value <= 0) {
            SnackBarToast.showMessage(view, "Please enter a valid value");
            return false;
        }

        return true;
    }


    public void add_stock_button_click(View view) {
        addToStock(view);
        loadSmartGrid();
    }

    private void addToStock(View view) {
        if (validate(view)) {
            List<STOCK> stockList = CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE);
            List<STOCK> stocks = new ArrayList<>();
            for (STOCK stock : stockList) {
                if (stock.getMAT_NO() != null && !stock.getMAT_NO().isEmpty() && stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                    stocks.add(stock);
                }
            }
            STOCK stockRow = null;
            if (stocks.size() > 0 && stocks.get(0) != null) {
                stockRow = stocks.get(0);
                stockRow.setCHCK_IN_QTY((stockRow.getCHCK_IN_QTY() == null ? 0 : stockRow.getCHCK_IN_QTY()) + Integer.parseInt(mQty.getText().toString()));
            } else {
                try {
                    stockRow = new STOCK();
                    stockRow.setCHCK_IN_QTY(Double.valueOf(mQty.getText().toString()));
                    stockRow.setFid(DBHelper.getInstance().getShipmentRow().getLid());
                    stockRow.setLid(stockRow.getLid());
                    stockRow.setMAT_DESC(selectedMaterial.getMAT_DESC());
                    stockRow.setMAT_NO(selectedMaterial.getMAT_NO());
                    stockRow.setSHIP_NO(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
                    stockRow.setTYPE(Constants.RCR_TYPE);
                    stockRow.setUOM(selectedMaterial.getBASE_UOM());
                } catch (DBException e) {
                    Logger.e("", e);
                }
            }
            if (stockRow != null) {
                CheckInController.getInstance().insertOrUpdateStock(stockRow);
            }
            updateSelectedStock();
            clearAll();
        }
    }

    public void check_in_complete_click(View view) {
        DialogMessage dialogMessage = new DialogMessage(this);
        if (!CheckInController.getInstance().checkDiscrepancy(CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE))) {
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Some Discrepancy is noticed \n Do you want to Recount?", "Proceed", "Recount", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    navigateToUploadMenu();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    navigateToChekInFulls();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        } else if (!CheckInController.getInstance().checkDiscrepancy(CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE))) {
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Some Discrepancy is noticed \n Do you want to Recount?", "Proceed", "Recount", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    navigateToUploadMenu();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();

                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        } else {
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Are you sure to proceed to Upload to SAP", "Proceed", "Cancel", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    navigateToUploadMenu();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }
    }

    private void updateSelectedStock() {
        List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.RCR_TYPE);
        if (list != null && list.size() > 0) {
            for (STOCK stock : list) {
                if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                    selectedSmartGrid = stock;
                }
            }
        }
    }

    private void navigateToUploadMenu() {
        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
        shipment_header.setSTAT((long) Constants.SHIP_STATUS_END_CHECKIN);
        DBHelper.getInstance().updateShipmentStatus(shipment_header);
        DBHelper.getInstance().createNewTime(shipment_header.getSHIP_NO(), Constants.TIME_TYPE_CHECKIN_END);
        startActivity(new Intent(CheckInEmptiesActivity.this, UploadMenuActivity.class));
        finish();
    }

    private void navigateToChekInFulls() {
        startActivity(new Intent(CheckInEmptiesActivity.this, CheckInFullsActivity.class));
        finish();
    }

    public void goBackToPreviousActivity(View view) {
        finish();
    }
}
