package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.Utils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;
import com.unvired.pdf.writer.PDFDocument;
import com.unvired.ui.Home;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;


public class DeliveryMenuActivity extends AppCompatActivity implements View.OnClickListener, IPrintCallBack {
    RelativeLayout mRcr,mFBRBIT,mFullFbr,mWoodenSupportPallets,mUbr,mDropTrailers,mPrintInputCheckList,mPrintProForma,mPrintInvoice;
    AlertDialog palletsMenuDialog,trailerMenuDialog;
    private VISIT currentVisit;
    DeliveryMenuActivity activity;
    private ImageView palletsDialogClose;
    private Button palletsPickUp,palletsDropOff;
    private boolean palletPickPrompted = false;
    private boolean isProFormaPrinted = false;
    List<DELIVERY> deliveryList = null;
    int position = 0;
    InvoiceReport invoiceReport;
    VISIT visitRow;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_delivery_menu);
        PrinterUtils.setLocale(this);

    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        iniListners();
        getData();
        activity = this;
    }

    private void getData() {
        currentVisit= TripSummaryController.getInstance().getCurrentVisitRow();
        if(currentVisit==null){
            clearDataAndGoback();
        }
        disableAllButtons();
        Utils.enableButton(mPrintInputCheckList,this);
        CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(currentVisit.getCUST_NO());
        if(Constants.NAT_CUSTOMER.equals(customer_header.getIS_NAT_CUST()) && currentVisit.getSTAT() == Constants.VISIT_STATUS_CHECKLIST_PRINTED){
            Utils.enableButton(mPrintProForma,this);
        }else {
            Utils.disableButton(mPrintProForma,this);
        }

        if(currentVisit.getSTAT()==null || currentVisit.getSTAT()==Constants.VISIT_STATUS_CHECKLIST_PRINTED){
            enableControls();
        }

        //Below visit status check to fix issue #1317 https://support.unvired.com/issues/1317
        if (currentVisit.getSTAT() == Constants.VISIT_STATUS_CUSTOMER_SIGN_CAPTURED)
        {
            disableAllButtons();
            Utils.disableButton(mPrintInputCheckList,this);
            Utils.disableButton(mPrintProForma,this);
            Utils.enableButton(mPrintInvoice,this);
        }
    }

    private void enableControls() {
        //initially everthing is diabled except checklist
        //If adhoc FBR/BIT,FULLFBR will be kept disabled bt pallets will be enabled
        //pallet Drop will  be enabled for non-adhoc visit and deliveries other than UBR.
        //UBR will be enabled only if atleast one delivery is of type TYPE UBR
        //nonUBRLoadsWhichCanBeUsed may not be needed for us..need to chk
        if (currentVisit == null) {
            return;
        }
        Utils.disableButton(palletsDropOff,this);
        //Commented IF check as per : #https://support.unvired.com/issues/1402
        //if (DeliveryController.ISFBRBITPalletsEnabled())
        //{
        if (currentVisit.getHHT() == null || currentVisit.getHHT().isEmpty())//non adhoc condition
        {
            Utils.enableButton(mFBRBIT,this);
            Utils.enableButton(palletsDropOff,this);
        }
        else
        {
            Utils.disableButton(palletsDropOff,this);
        }
        Utils.enableButton(mPrintInputCheckList,this);
        CUSTOMER_HEADER customerHeader = TripSummaryController.getInstance().getCustomer(currentVisit.getCUST_NO());
        if (Constants.NAT_CUSTOMER.equals(customerHeader.getIS_NAT_CUST()))
        {
             Utils.enableButton(mPrintProForma,this);
        }
        else
        {
            Utils.disableButton(mPrintProForma,this);
        }
        Utils.enableButton(mWoodenSupportPallets,this);
        //}
        //else
        //{
        //    CaptureFbrButton.Enabled = false;
        //    uiButtonPrintInputChecklist.Enabled = false;
        //    CaptureWoodenButtonSupport.Enabled = false;
        //}
        //https://support.unvired.com/issues/1212 - disable FBR/BIT if customer only has empty returns dummy order
        if (DBHelper.getInstance().hasOnlyReturnsCollectionOrder())
        {
            Utils.disableButton(mFBRBIT,this);
        }

        if (currentVisit.getHHT()==null || currentVisit.getHHT().isEmpty())
        {
            if(DeliveryController.getInstance().isFullFBREnabled()){
                Utils.enableButton(mFullFbr,this);
            }else {
                Utils.disableButton(mFullFbr,this);
            }
        }
        else
        { //since pick of pallet is possible
            Utils.enableButton(mWoodenSupportPallets,this);
            if(DBHelper.getInstance().checkPrintEnable()){
                Utils.enableButton(mPrintInputCheckList,this);
                Utils.enableButton(mPrintProForma,this);
            } else {
                Utils.disableButton(mPrintInputCheckList,this);
                Utils.disableButton(mPrintProForma,this);
            }
        }

        if(DeliveryController.getInstance().isRCREnabled()){
            Utils.enableButton(mRcr,this);
        } else {
            Utils.disableButton(mRcr,this);
        }

        if(DeliveryController.getInstance().isUBREnabled()){
            Utils.enableButton(mUbr,this);
            Utils.enableButton(mWoodenSupportPallets,this);
        } else {
            Utils.disableButton(mUbr,this);
        }
        Utils.enableButton(mDropTrailers,this);
        Utils.enableButton(mPrintInvoice,this);

    }

    private void disableAllButtons() {
        Utils.disableButton(mRcr,this);

        Utils.disableButton(mFBRBIT,this);
        Utils.disableButton(mFullFbr,this);

        Utils.disableButton(mWoodenSupportPallets,this);
        Utils.disableButton(mUbr,this);
        Utils.disableButton(mDropTrailers,this);
        Utils.disableButton(mPrintInvoice,this);
    }

    private void clearDataAndGoback() {
        if(DBHelper.getInstance().isUnplannedCustomer()){
            DialogMessage dialogMessage = new DialogMessage(this);
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Going back will clear all captured data for this Visit", "Ok", "Cancel", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    DeliveryController.getInstance().deleteCurrentVisitDeliveries(currentVisit);
                    InvoiceController.setDeliveryItemPricingDictionary(null);
                    DBHelper.getInstance().setUnplannedCustomer(false);
                    DBHelper.getInstance().setPalletPickPrompted(false);
                    finish();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }else {
            InvoiceController.setDeliveryItemPricingDictionary(null);
            DBHelper.getInstance().setUnplannedCustomer(false);
            DBHelper.getInstance().setPalletPickPrompted(false);
            finish();
        }
    }

    private void iniListners() {
        mRcr.setOnClickListener(this);
        mFBRBIT.setOnClickListener(this);
        mFullFbr.setOnClickListener(this);
        mWoodenSupportPallets.setOnClickListener(this);
        mUbr.setOnClickListener(this);
        mDropTrailers.setOnClickListener(this);
        mPrintInputCheckList.setOnClickListener(this);
        mPrintInvoice.setOnClickListener(this);
        mPrintProForma.setOnClickListener(this);
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private void iniViews() {
        mRcr=findViewById(R.id.xDeliveryMenuRCR);
        mFBRBIT=findViewById(R.id.xDeliveryMenuFBRBIT);
        mFullFbr=findViewById(R.id.xDeliveryMenuFullFbr);
        mWoodenSupportPallets=findViewById(R.id.xDeliveryMenuWoodenSupportPallets);
        mUbr=findViewById(R.id.xDeliveryMenuUBR);
        mDropTrailers=findViewById(R.id.xDeliveryMenuDropTrailers);
        mPrintInputCheckList=findViewById(R.id.xDeliveryMenuPrintInputChecklist);
        mPrintProForma=findViewById(R.id.xDeliveryMenuPrintProForma);
        mPrintInvoice=findViewById(R.id.xDeliveryMenuPrintInvoice);
        initializeWoodenPalletsDialog();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(DeliveryMenuActivity.this);

    }

    public void closeActivity(View view) {
        clearDataAndGoback();
    }

    @Override
    public void onClick(View v) {

        if (v.getId() == R.id.xDeliveryMenuRCR) {
            navigateToRcrFormActivity();
        }

        if(v.getId() == R.id.xDeliveryMenuFBRBIT){
            navigateToFbrBitActivity();
        }

        if(v.getId() == R.id.xDeliveryMenuFullFbr){
            navigateToFullFbrActivity();
        }

        if(v.getId()==R.id.xDeliveryMenuWoodenSupportPallets){
            showWoodenPalletsDialog();
        }

        if(v.getId()==R.id.xDeliveryMenuUBR){
            navigateToUbrActivity();
        }

        if(v.getId()==R.id.xDeliveryMenuDropTrailers){
            showDropTrailersMenu();
        }

        if(v.getId()==R.id.xWoodenPalletsDialogPickUp){
            DBHelper.getInstance().setPalletPickPrompted(true);
            navigateToWoodenPalletsPickUp();
        }

        if(v.getId()==R.id.xWoodenPalletsDialogDropOff){
            navigateToWoodenPalletsDropOff();
        }

        if(v.getId()==R.id.xTrailerMenuDialogPickUp){
            navigateToTrailerPickUp();
        }

        if(v.getId()==R.id.xTrailerMenuDialogDropOff){
            navigateToTrailerDropOff();
        }
        if(v.getId()==R.id.xDeliveryMenuPrintInputChecklist){
            Utils.disableButton(mPrintInputCheckList,this);
            try {
                position = 0;
                preparePdf(Constants.DocType.InputChecklist);
                Logger.i("User clicked PrintInputChecklist Button");
            } catch (Exception e) {
                Logger.e("DeliveryMenuPrintInputChecklist Error", e);
                Utils.enableButton(mPrintInputCheckList,this);
            }
        }
        if(v.getId()==R.id.xDeliveryMenuPrintProForma){
            //preparePdf
            try {
                position = 0;
                //not present in windows
                DeliveryController.getInstance().processUnProcessedUBR();
                preparePdf(Constants.DocType.ProForma);
                Logger.i("User clicked PrintProForma Button");
                this.isProFormaPrinted = true;
            } catch (Exception e) {
                Logger.e("DeliveryMenuPrintProForma Error", e);
                Log.e("TAG", "onClick: "+e.getMessage() );
            }
        }
        if(v.getId()==R.id.xDeliveryMenuPrintInvoice){
            boolean isPorFormaEnabled = false;
            try {
                isPorFormaEnabled = DeliveryController.getInstance().isProFormaMandatory();
            } catch (DBException e) {
                Logger.e("DeliveryMenuPrintInvoice Error", e);
            }

            if (palletPickPrompted || currentVisit.getSTAT() == Constants.VISIT_STATUS_CUSTOMER_SIGN_CAPTURED) {
                if((isPorFormaEnabled && isProFormaPrinted) || !isPorFormaEnabled) {
                    try {
                        DeliveryController.getInstance().processUnProcessedUBR();
                        InvoiceController.generateInvoice(currentVisit);
                        Logger.i("User clicked Print Invoice Button, navigating to DeliverySummary");
                        navigateToDeliverySummary();
                    } catch (Exception e) {
                        showErrorDialog(e.getMessage());
                        Logger.e("DeliveryMenuPrintInvoice Error", e);
                    }
                } else {
                    String message = "A Pro Forma is MANDATORY for this Customer.\n\nPlease print the Pro Forma before printing the final Invoice.";
                    showProformaPrintDialog(message);
                }
            } else {
                showWoodenPalletsDialog();
            }
        }
    }

    private void showProformaPrintDialog(String message) {
        DialogMessage dialogMessage =new DialogMessage(this);
        dialogMessage.showDialogWithPositive("", message, "Print Pro Forma", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                try {
                    dialogMessage.dismiss();
                    position = 0;
                    DeliveryController.getInstance().processUnProcessedUBR();
                    preparePdf(Constants.DocType.ProForma);
                    isProFormaPrinted = true;
                } catch (Exception e) {
                    Logger.e("showProformaPrintDialog Error", e);
                    Log.e("TAG", "onClick: " + e.getMessage());
                }
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage){
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }

    private void showErrorDialog(String message) {
        DialogMessage dialogMessage =new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", message, "Ok","Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                DeliveryController.getInstance().deleteCurrentVisitDeliveries(currentVisit);
                DBHelper.getInstance().setUnplannedCustomer(false);
                DBHelper.getInstance().setPalletPickPrompted(false);
                finish();
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }

    private void navigateToTrailerDropOff() {
        trailerMenuDialog.dismiss();
        startActivity(new Intent(DeliveryMenuActivity.this,TrailerDropOffActivity.class));
    }

    private void navigateToTrailerPickUp() {
        trailerMenuDialog.dismiss();
        startActivity(new Intent(DeliveryMenuActivity.this,TrailerPickupActivity.class));
    }

    private void navigateToWoodenPalletsDropOff() {
        palletsMenuDialog.dismiss();
        startActivity(new Intent(DeliveryMenuActivity.this,PalletsDropOffActivity.class));
    }

    private void navigateToWoodenPalletsPickUp() {
        palletsMenuDialog.dismiss();
        startActivity(new Intent(DeliveryMenuActivity.this,PalletsPickUpActivity.class));
    }

    private void showDropTrailersMenu() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.trailer_menu_dialog_view,  null);
        builder.setView(dialogView);
        trailerMenuDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xTrailerMenuDialogClose);
        Button trailerPickUp = dialogView.findViewById(R.id.xTrailerMenuDialogPickUp);
        Button trailerDropOff = dialogView.findViewById(R.id.xTrailerMenuDialogDropOff);
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                trailerMenuDialog.dismiss();
            }
        });
        trailerPickUp.setOnClickListener(this);
        trailerDropOff.setOnClickListener(this);
        trailerMenuDialog.show();
    }

    private void navigateToUbrActivity() {
        startActivity(new Intent(DeliveryMenuActivity.this,UBRFormActivity.class));
    }

    private void initializeWoodenPalletsDialog(){
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.wooden_pallets_dialog_view,  null);
        builder.setView(dialogView);
        palletsMenuDialog = builder.create();
        palletsDialogClose = dialogView.findViewById(R.id.xWoodenPalletsDialogClose);
        palletsPickUp = dialogView.findViewById(R.id.xWoodenPalletsDialogPickUp);
        palletsDropOff = dialogView.findViewById(R.id.xWoodenPalletsDialogDropOff);
    }

    private void showWoodenPalletsDialog() {
        palletPickPrompted=true;
        palletsDialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                palletsMenuDialog.dismiss();
            }
        });
        palletsPickUp.setOnClickListener(this);
        palletsDropOff.setOnClickListener(this);
        palletsMenuDialog.show();
    }

    private void navigateToFullFbrActivity() {
        startActivity(new Intent(DeliveryMenuActivity.this,FullFBRFormActivity.class));
    }

    private void navigateToFbrBitActivity() {
        startActivity(new Intent(DeliveryMenuActivity.this,FBR_BITFormActivity.class));
    }

    private void navigateToRcrFormActivity() {
        startActivity(new Intent(DeliveryMenuActivity.this,RcrFormActivity.class));
    }

    private void showRcrForm() { }


    private void preparePdf(Enum<Constants.DocType> type) throws Exception {
        position = 0;
        Loader.showLoader(this, "Please wait while printing");
        Font font = Utils.getFont(10f);
         invoiceReport = new InvoiceReport(font, false);
        //invoiceReport.generateReportPrint(type,this,false,this);
         visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
        Loader.hideLoader();
        invoiceReport.generateInvoice();
        deliveryList = DeliveryController.getInstance().getDeliveryHeaders(visitRow.getVISIT_NO(), DeliveryController.Quantity.ALL);
        if(deliveryList!=null && deliveryList.get(position)!=null){
            invoiceReport.generateInvoicePdf(deliveryList.get(position),visitRow, type, false, this,this,null);
        }
    }

    @Override
    public void onPrintSuccess(final Enum<Constants.DocType> type, Constants.CopyType copyType, boolean isLast,String filePath,boolean reprint) {
        //save in storage for testing purpose
        File file = new File(filePath);
        if(!PrinterUtils.printFlag){
            try {
                InvoiceReport.exportFile(file,file.getName(),this);
            } catch (IOException e) {
                Logger.e("InvoiceReport.exportFile() Error", e);
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
        String timeType = Constants.TIME_TYPE_CUSTOMER_PRINT_INVOICE;
        if (type == Constants.DocType.InputChecklist)
            timeType = Constants.TIME_TYPE_CUSTOMER_PRINT_INPUT_CHECKLIST;
        VISIT currentVisit = TripSummaryController.getInstance().getCurrentVisitRow();
        if(currentVisit != null) {
            TimeController.createNewTime(currentVisit.getVISIT_NO().toString(), timeType);
        }
        Log.e("Print OVER", "Printing Success!!!"+type.toString()+"--Type--->"+type);
        if(deliveryList!=null && position<(deliveryList.size()-1) && type== Constants.DocType.InputChecklist){
            showMessageDialog(type,getString(R.string.print_next_input_list));
        }
        if(deliveryList!=null && position<(deliveryList.size()-1) && type== Constants.DocType.Invoice){
            showMessageDialog(type,getString(R.string.print_next_invoice_list));

        }
        if(deliveryList!=null && position<(deliveryList.size()-1) && type== Constants.DocType.ProForma){
            showMessageDialog(type,getString(R.string.print_next_proforma_list));
        }
        position++;
        if(deliveryList!=null && position==(deliveryList.size())){
            if (Constants.DocType.InputChecklist.equals(type)) {
                currentVisit.setSTAT((long) Constants.VISIT_STATUS_CHECKLIST_PRINTED);
                DeliveryController.getInstance().insertOrUpdateVisit(currentVisit);
                Utils.enableButton(mPrintInputCheckList,this);
                enableControls();
            }
        }
        Log.i("TAG:::", "onPrintSuccess: "+position);
    }

    private void showMessageDialog(final Enum<Constants.DocType> type, String message) {
        DialogMessage dialogMessage =new DialogMessage(DeliveryMenuActivity.this);
        dialogMessage.showDialogWithPositive("", message, "OK", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                try {
                    //invoiceReport.generateInvoice();
                    invoiceReport.generateInvoicePdf(deliveryList.get(position),visitRow, type, false, DeliveryMenuActivity.this,DeliveryMenuActivity.this,null);
                } catch (Exception e) {
                    Logger.e("generateInvoicePdf() Error", e);
                }
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {
            }
        });
    }

    private void navigateToDeliverySummary() {
        startActivity(new Intent(DeliveryMenuActivity.this,DeliverySummaryForm.class));
        //finish();
    }

    @Override
    public void onPrintError(Exception e,Enum<Constants.DocType> type) {
        if (Constants.DocType.InputChecklist.equals(type)) {
            Utils.enableButton(mPrintInputCheckList,this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if(DBHelper.getInstance().isUnplannedCustomer()) {
//            DeliveryController.getInstance().deleteCurrentVisitDeliveries(currentVisit);
//            DBHelper.getInstance().setUnplannedCustomer(false);
//            DBHelper.getInstance().setPalletPickPrompted(false);
//            finish();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if(DBHelper.getInstance().isUnplannedCustomer()) {
//            DeliveryController.getInstance().deleteCurrentVisitDeliveries(currentVisit);
//            DBHelper.getInstance().setUnplannedCustomer(false);
//            DBHelper.getInstance().setPalletPickPrompted(false);
//            finish();
        }
    }

    @Override
    public void onBackPressed() {
    }
}