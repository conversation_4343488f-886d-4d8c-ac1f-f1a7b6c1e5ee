package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.abinbev.oasis.adapter.PalletCheckerDropDownAdapter;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Controllers.TripInspController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;
import com.unvired.pdf.writer.PDFDocument;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class EndOfTripMenuActivity extends AppCompatActivity implements IPrintCallBack {
    double current_status;
    private RelativeLayout mPrintFinalReports,mFinalOdometer,mFullLoadFBRReason,mCheckIn;
    private AUTH_HEADER selectedChecker=null;

    private IDataManager iDataManager = null;

    EndOfTripMenuActivity activity;
    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        try {
            iDataManager = ApplicationManager.getInstance().getDataManager();
        } catch (DBException e) {
            Logger.e("", e);
        }
        setContentView(R.layout.activity_end_of_trip_menu);
        iniViews();
        getData();
        activity = this;
    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        getData();
        activity = this;
    }


    @Override
    public void onBackPressed() {

    }

    private void iniViews() {
        mPrintFinalReports=findViewById(R.id.xEndOfTripMenuFinalReport);
        mFinalOdometer=findViewById(R.id.xEndOfTripMenuFinalOdometer);
        mFullLoadFBRReason=findViewById(R.id.xFullLoadFBRReason);
        mCheckIn=findViewById(R.id.xEndOfTripMenuCheckIn);
    }

    private void getData() {
        current_status = (double)DBHelper.getInstance().getCurrentShipment().getSTAT();
        checkShipmentStatus();
    }

    public List<DELIVERY> getOpenFullFBRDeliveryHeaders(long visit_no) {

        List<DELIVERY> deliveryList = new ArrayList<>();
        List<DELIVERY> deliveryListDummyMaterials = new ArrayList<>();

        String query = "";
        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        query = "(" + DELIVERY.FIELD_SHIP_NO + " ='" + ship_no + "' AND (FULL_FBR_RSN <> '' AND FULL_FBR_RSN is NOT NULL))";

        try {
            IDataStructure[] structures = iDataManager.get(DELIVERY.TABLE_NAME, query);
            if (structures != null && structures.length > 0) {
                for (IDataStructure structure : structures) {
                    deliveryList.add((DELIVERY) structure);
                }
            }

            // fix: https://support.unvired.com/issues/1979
            // Logic to remove info collection ie delivery with dummy material : start
            // select delivery items of info collection type
            List<String> deliveries = new ArrayList<>();
            for (DELIVERY delv : deliveryList ) {
//          LOOK FOR DELIVERY ITEMS WHICH ARE NOT COPIED THROUGH SPLIT BILL PROCESS.
                String queryForDummy = "SELECT MAT_NO, DELV_NO FROM DELIVERY_ITEM WHERE DELV_NO = '" + delv.getDELV_NO() +"' AND PROMO_TYPE_DESC != 'OSB'";
                try {
                    Cursor cursor = iDataManager.executeQuery(queryForDummy);
                    // INFO COLLECTION DELIVERY WE EXPECT ONLY ONE ITEM (DUMMY MATERIAL)
                    if (cursor != null && cursor.getCount() == 1) {
                        while (cursor.moveToNext()) {
                            String mat_no = cursor.getString(0);
                            if (MaterialController.getInstance().getDummyMaterialNos() != null && Arrays.asList(MaterialController.getInstance().getDummyMaterialNos()).contains(mat_no)) {
                                if (!deliveries.contains(cursor.getString(1))) {
                                    deliveries.add(cursor.getString(1));
                                }
                            } else {

                            }
                            if (cursor.isLast()) {
                                cursor.close();
                            }
                        }
                    } else {
                        if (!cursor.isClosed()) {
                            cursor.close();
                        }
                    }
                } catch (DBException e) {
                    Logger.e("", e);
                }

                // sort based delivery that have info collection items


            }
            if (deliveries != null && deliveries.size() > 0) {
                for (int x = (deliveries.size() - 1); x >= 0; x--) {
                    for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                        if (deliveryList.get(i).getDELV_NO().equals(deliveries.get(x))) {
                            deliveryListDummyMaterials.add(deliveryList.get(i));
                            deliveryList.remove(i);
                        }
                    }
                }
            }

            // Logic to remove info collection ie delivery with dummy material : end


        } catch (DBException e) {
            Logger.e("", e);
        }

        if (deliveryList.size() > 0) {
            return deliveryList;
        }
        return null;
    }

    public int getFullFBRDeliveryCount() {
        String query = "SELECT COUNT(*) FROM DELIVERY WHERE (FULL_FBR_RSN == '' OR FULL_FBR_RSN is NULL) AND ORD_TYPE != '" + Constants.ORDER_TYPE.ZUBR + "'";
        List<DELIVERY> delivery = getOpenFullFBRDeliveryHeaders(-1);
        String deliveryINStr = "";
        if(delivery != null) {
            for (DELIVERY delv : delivery) {
                if (deliveryINStr == null || deliveryINStr.isEmpty()) {
                    deliveryINStr = "'" + delv.getDELV_NO() + "'";
                } else {
                    deliveryINStr = deliveryINStr + ", '" + delv.getDELV_NO() + "'";
                }
            }
            query = query + " AND (DELV_NO NOT IN (" + deliveryINStr + "))";
        }

        try {
            Cursor cursor = iDataManager.executeQuery(query);
            cursor.moveToFirst();
            int count = Integer.parseInt(cursor.getString(cursor.getColumnIndex("COUNT(*)")));
            cursor.close();
            return count;
        } catch (DBException e) {
            Logger.e("", e);
            return 0;
        }
    }

    private void checkShipmentStatus() {
        Utils.enableButton(mPrintFinalReports,this);
        Utils.enableButton(mFinalOdometer,this);
        Utils.enableButton(mFullLoadFBRReason,this);
        Utils.enableButton(mCheckIn,this);
        if(current_status== Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED){
            Utils.disableButton(mPrintFinalReports,this);
            Utils.disableButton(mFinalOdometer,this);

        }else if(current_status== Constants.SHIP_STATUS_FINAL_REPORT_PRINTED){
            Utils.disableButton(mPrintFinalReports,this);
        }
        if(getFullFBRDeliveryCount() > 0) {
            Utils.disableButton(mFullLoadFBRReason,this);
        } else {
            SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
            if(shipment_header.getFULL_FBR_RSN_CODE()!= null && shipment_header.getFULL_FBR_RSN_CODE().length() > 0) {
                Utils.disableButton(mFullLoadFBRReason,this);
            }
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(EndOfTripMenuActivity.this);
    }

    public void clearShipment(View view) {
        SHIPMENT_HEADER shipmentRow = DBHelper.getInstance().getShipmentRow();
        shipmentRow.setFULL_FBR_RSN_CODE("");
        DBHelper.getInstance().updateShipmentStatus(shipmentRow);
        getData();
    }

    public void checkInButtonClick(View view) {
        String FULL_FBR_RSN_CODE = DBHelper.getInstance().getCurrentShipment().getFULL_FBR_RSN_CODE();
        if(FULL_FBR_RSN_CODE == null) {
            FULL_FBR_RSN_CODE = "";
        }
        if(current_status!=Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED){
            if(current_status!=Constants.SHIP_STATUS_FINAL_REPORT_PRINTED){
                if(FULL_FBR_RSN_CODE.length() == 0 && getFullFBRDeliveryCount() == 0) {
                    SnackBarToast.showMessage(view, "Please Print Final Report, Complete Final odometer reading and Select Full Load FBR Reason");
                } else {
                    SnackBarToast.showMessage(view, "Please Print Final Report and Complete Final odometer reading");
                }
            } else {
                if(FULL_FBR_RSN_CODE.length() == 0 && getFullFBRDeliveryCount() == 0) {
                    SnackBarToast.showMessage(view, "Please Complete Final odometer reading and Select Full Load FBR Reason");
                } else {
                    SnackBarToast.showMessage(view, "Please Complete Final odometer reading");
                }
            }
            return;
        } else if (FULL_FBR_RSN_CODE.length() == 0 && getFullFBRDeliveryCount() == 0) {
            SnackBarToast.showMessage(view, "Please Select Full Load FBR Reason");
            return;
        }
        showPanelCheckerDialog();
    }

    public void navigateToFullLoadFBRReason(View view) {
        Intent intent = new Intent(EndOfTripMenuActivity.this,FullLoadFBRReason.class);
        startActivity(intent);
    }

    private void showPanelCheckerDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.pallet_checker_dialog,  null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xPalletCheckerDialogClose);
        Button dialogProceed = dialogView.findViewById(R.id.xPalletCheckerDialogProceed);
        Spinner mCheckersSpinner = dialogView.findViewById(R.id.xPalletCheckerDialogSpinner);
        final TextInputEditText mPassword = dialogView.findViewById(R.id.xPalletCheckerDialogPassword);
        final List<AUTH_HEADER> authHeaderList = TripInspController.getInstance().getUsernames(Constants.AUTH_TYPE_CHECKER);
        final PalletCheckerDropDownAdapter palletCheckerDropDownAdapter = new PalletCheckerDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, authHeaderList);
        mCheckersSpinner.setAdapter(palletCheckerDropDownAdapter);
        mCheckersSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                palletCheckerDropDownAdapter.setSelectionPosition(position);
                selectedChecker=authHeaderList.get(position);
                Log.e("PASS",selectedChecker.getUSER_ID());
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        dialogProceed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(chekInValidate(v,mPassword)){
                    alertDialog.dismiss();
                    showChekInConfirmationDialog();
                }
            }
        });
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE  | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mPassword.requestFocus();
        alertDialog.show();
    }

    private void showChekInConfirmationDialog() {
        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Are you sure to proceed to CHECK-IN", "CHECK-IN", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
                    shipment_header.setSTAT((long) Constants.SHIP_STATUS_START_CHECKIN);
                    DBHelper.getInstance().updateShipmentStatus(shipment_header);
                    DBHelper.getInstance().createNewTime(shipment_header.getSHIP_NO(),Constants.TIME_TYPE_CHECKIN_START);
                    DBHelper.getInstance().createTimeCheckinUser(Constants.CHECKIN_TYPE,selectedChecker.getUSER_ID());
                    navigateToCheckInActivity();
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }
        });
    }

    private void navigateToCheckInActivity() {
        startActivity(new Intent(EndOfTripMenuActivity.this,CheckInActivity.class));
    }

    private boolean chekInValidate(View v, TextInputEditText mPassword) {
        if(selectedChecker==null){
            SnackBarToast.showMessage(v,"Select Checker.");
            return false;
        }

        if(mPassword.getText().toString().isEmpty()){
            SnackBarToast.showMessage(v,"Enter Password.");
            return false;
        }
        Log.e("password",selectedChecker.getUSER_ID());
        String barcode = mPassword.getText().toString();
        if(barcode.length()<=selectedChecker.getUSER_ID().length() || !barcode.contains(selectedChecker.getUSER_ID())){
            SnackBarToast.showMessage(v,"Incorrect Password.");
            return false;
        }

        return true;
    }


    private void showOdometerReadingDialog(){
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.odometer_reading_dialog_trip_summary,  null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xOdometerReadingDialogClose);
        Button dialogProceed = dialogView.findViewById(R.id.xOdometerReadingDialogProceed);
        TextView mMessage = dialogView.findViewById(R.id.xOdometerReadingDialogMessage);
        mMessage.setGravity(Gravity.CENTER_HORIZONTAL);
        mMessage.setText("Final Odometer Reading");
        final TextInputEditText mReadingEt = dialogView.findViewById(R.id.xOdometerReadingDialogReadingEt);
        dialogProceed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                float currentReading = 0;
                if(mReadingEt.getText().toString().isEmpty()){
                    SnackBarToast.showMessage(v,"Please enter truck KM reading");
                }else {
                    try {
                        currentReading= Float.parseFloat(mReadingEt.getText().toString());
                    }catch (Exception e){
                        SnackBarToast.showMessage(v,"Please enter a valid KM reading");
                        return;
                    }
                    SHIPMENT_HEADER shipmentRow = DBHelper.getInstance().getShipmentRow();

                    double previousReading = shipmentRow.getEND_MILE()==null ? 0 : shipmentRow.getEND_MILE();
                    if(currentReading<=previousReading){
                        SnackBarToast.showMessage(v,"Entered KM reading less than truck odometer.");
                        return;
                    }else {
                        shipmentRow.setEND_MILE((double) currentReading);
                        shipmentRow.setSTAT((long) Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED);
                        current_status = Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED;
                        DBHelper.getInstance().updateShipmentStatus(shipmentRow);
                        DBHelper.getInstance().createNewTime(DBHelper.getInstance().getShipmentRow().getSHIP_NO(),Constants.TIME_TYPE_ODOMETER_FINAL);
                        Utils.disableButton(mFinalOdometer,EndOfTripMenuActivity.this);
                        alertDialog.dismiss();
                    }
                }
            }
        });
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE  | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mReadingEt.requestFocus();
        alertDialog.show();
    }



    public void final_odometer_click(View view) {
        showOdometerReadingDialog();
    }

    public void print_final_report_click(View view) {
        try {
            printStockReportPrint();
        } catch (Exception e) {
            Logger.e("", e);
            Utils.enableButton(mPrintFinalReports,this);
        }

    }

    private void printStockReportPrint() throws Exception {
        Loader.showLoader(this, "Please wait while printing");
        Font font = Utils.getFont(10f);
        PDFDocument pdfDocument = new PDFDocument(getFilesDir() + "",
                PageSize.A6.getWidth(), 10.0f, 10.0f, 15, 5.0f, font, getBaseContext());
        ReportHelper.getInstance().setPDFDocument(pdfDocument);
        InvoiceReport invoiceReport = new InvoiceReport(font, false);
        invoiceReport.stockReportPrint(Constants.TRIP_INSP_TYPE_POSTTRIP,true);
        String pdfPath = pdfDocument.generatePdf();
        Loader.hideLoader();

      //  if(PrinterUtils.printFlag){
            if (!Utils.isNullOrEmpty(pdfPath))
                PrinterUtils.print(pdfPath, activity, this,null,null,false,false);
       // }
    }

    @Override
    public void onPrintSuccess(Enum<Constants.DocType> type,Constants.CopyType copyType,boolean isLast,String filePath,boolean reprint) {
        //Update time
        TimeController.createNewTime(DBHelper.getInstance().getShipmentRow().getSHIP_NO(), Constants.TIME_TYPE_DEPOT_STOCK_FINAL);



        //current_status=Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED;
        Utils.disableButton(mPrintFinalReports,this);
        //save in storage for testing purpose
        if(current_status != Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED){
            SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
            currentShipmentHeader.setSTAT((long) Constants.SHIP_STATUS_FINAL_REPORT_PRINTED);
            DBHelper.getInstance().updateShipmentStatus(currentShipmentHeader);
        }
        File file = new File(filePath);
        if(!PrinterUtils.printFlag){
            try {
                InvoiceReport.exportFile(file,file.getName(),this);
            } catch (IOException e) {
                Logger.e("", e);
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
    }

    @Override
    public void onPrintError(Exception e, Enum<Constants.DocType> type) {
        Utils.enableButton(mPrintFinalReports,this);
    }
}