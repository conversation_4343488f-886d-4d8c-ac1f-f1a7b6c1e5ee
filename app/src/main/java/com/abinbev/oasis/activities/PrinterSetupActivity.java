package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import android.os.Bundle;

import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.abinbev.oasis.R;

import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewConfiguration;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.database.IDataStructure.OBJECT_STATUS;
import com.unvired.database.IDataStructure.SYNC_STATUS;
import com.unvired.logger.Logger;
import com.unvired.print.DeviceBTNotAvailableException;
import com.unvired.print.DeviceBTNotEnabledException;
import com.unvired.print.PrinterFactory;
import com.unvired.print.PrinterNotActiveException;
import com.unvired.print.PrinterNotPairedException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Set;


public class PrinterSetupActivity extends AppCompatActivity implements OnClickListener {

    private static final int REQUEST_ENABLE_BT = 0;
    private static final int REQUEST_SCAN_BT = 1;
    private final int SCAN_BUTTON = 1;
    private final int SAVE_BUTTON = 2;
    private final int CANCEL_BUTTON = 3;

    private Button scan;
    private Button save;
    private Button cancel;

    private TextView macAddress;
    private TextView deviceNameText;
    private Activity activity;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        getWindow().requestFeature(Window.FEATURE_ACTION_BAR_OVERLAY);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_printer_setup);
        setToolBar();

        try {
            ViewConfiguration config = ViewConfiguration.get(this);
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if (menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception ex) {
            // Ignore
        }

        activity = this;
        initialize();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Logger.i("User in PrinterSetupActivity");
    }

    private void initialize() {

        scan = (Button) findViewById(R.id.scanForMacAddr);
        scan.setOnClickListener(this);

        save = (Button) findViewById(R.id.saveMacAddr);
        save.setOnClickListener(this);

        cancel = (Button) findViewById(R.id.printerSetupCancelBtn);
        cancel.setOnClickListener(this);

        macAddress = (TextView) findViewById(R.id.printerMacAddress);
        deviceNameText = (TextView) findViewById(R.id.printerName);

        if (getExistingMacAddr() != null)
            macAddress.setText(getExistingMacAddr() + "");
        if (getExistingMacName() != null)
            deviceNameText.setText(getExistingMacName() + "");

    }

    private String getExistingMacName() {
        String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_NAME + "' ";
        IDataStructure[] iDataStructure;
        try {
            iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);

            if (iDataStructure != null && iDataStructure.length > 0)
                return ((CUSTOMIZATION_HEADER) iDataStructure[0]).getKEY_VALUE();
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "getExistingMacAddr", "DBException while saveMacAddress: " + Utils.getExceptionMessage(e));
        }
        return null;
    }

    private String getExistingMacAddr() {

        String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_ADDRESS + "' ";
        IDataStructure[] iDataStructure;
        try {
            iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);

            if (iDataStructure != null && iDataStructure.length > 0)
                return ((CUSTOMIZATION_HEADER) iDataStructure[0]).getKEY_VALUE();
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "getExistingMacAddr", "DBException while saveMacAddress: " + Utils.getExceptionMessage(e));
        }
        return null;
    }

    @Override
    public void onClick(View v) {
        if (v == scan) {
            onClick(SCAN_BUTTON);
        } else if (v == save) {
            onClick(SAVE_BUTTON);
        } else if (v == cancel) {
            onClick(CANCEL_BUTTON);
        }
    }

    public void onClick(int key) {

        switch (key) {

            case SCAN_BUTTON:
                String macAddr = macAddress.getText().toString();
                if (!Utils.isNullOrEmpty(macAddr)) {
                    confirmAndClearPrinterSetupEntry();
                } else {
                    scanForMacAddress();
                }

                break;

            case SAVE_BUTTON:
                saveMacAddress();
                saveMacName();
                break;

            case CANCEL_BUTTON:
                onBackPressed();
                break;

            default:
                break;
        }
    }

    private void saveMacName() {
        {

            try {

                String deviceName = deviceNameText.getText().toString();
                if (!Utils.isNullOrEmpty(deviceName)) {

                    String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_NAME + "' ";
                    IDataStructure[] iDataStructure;
                    try {
                        iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);
                        if (iDataStructure == null || iDataStructure.length <= 0) {
                            insertNameIntoTable();
                        } else {
                            CUSTOMIZATION_HEADER settingsHeader = (CUSTOMIZATION_HEADER) iDataStructure[0];

                            settingsHeader.setKEY_VALUE(deviceName);

                            settingsHeader.setObjectStatus(OBJECT_STATUS.MODIFY);
                            settingsHeader.setSyncStatus(SYNC_STATUS.NONE);

                            ApplicationManager.getInstance().getDataManager().update(settingsHeader);
                            Toast.makeText(activity, "Updated", Toast.LENGTH_SHORT).show();
                        }
                        finish();
                    } catch (DBException e) {
                        Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "saveMacAddress", "DBException while saveMacAddress: " + Utils.getExceptionMessage(e));
                    }
                } else
                    Toast.makeText(activity, "Please scan for Bluetooth device, then save.", Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Logger.log(Logger.LEVEL_ERROR, this.getClass().getName(), "saveMacAddress", Utils.getExceptionMessage(e));
                Logger.e("", e);
            }
        }
    }

    private void insertNameIntoTable() {
        try {
            CUSTOMIZATION_HEADER settingsHeader = new CUSTOMIZATION_HEADER();
            String name = deviceNameText.getText().toString();
            settingsHeader.setKEY_NAME(Constants.PRINTER_NAME);
            settingsHeader.setKEY_VALUE(name);
            settingsHeader.setObjectStatus(OBJECT_STATUS.ADD);
            settingsHeader.setSyncStatus(SYNC_STATUS.NONE);
            ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(settingsHeader);
            Toast.makeText(activity, "Saved", Toast.LENGTH_SHORT).show();
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "createTable", "DBException while createTable: " + Utils.getExceptionMessage(e));
        }
    }

    private void clearPrinterSetUpEntry() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();

        try {
            Class<?> btDeviceInstance = Class.forName(BluetoothDevice.class.getCanonicalName());
            Method removeBondMethod = btDeviceInstance.getMethod("removeBond");
            String macAddr = macAddress.getText().toString();
            boolean cleared = false;
            for (BluetoothDevice bluetoothDevice : pairedDevices) {
                String mac = bluetoothDevice.getAddress();
                if (mac.equals(macAddr)) {
                    removeBondMethod.invoke(bluetoothDevice);
                    System.out.print("Cleared Pairing");
                    macAddress.setText("");
                    deviceNameText.setText("");
                    cleared = true;
                    break;
                }
            }

            if (!cleared) {
                System.out.print("Not Paired");
            }
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, "PrinterSetupActivity", "clearPrinterSetUpEntry", "Exception while clearing printer setup entry: " + Utils.getExceptionMessage(e));
        }
    }

    private void scanForMacAddress() {

        showDialog();
    }

    private int cnt;

    private void showDialog() {

        final ProgressDialog progressDialog = ProgressDialog.show(activity, "", "Please wait while Scanning...");
        cnt = getPairedDeviceCnt();
        new Thread() {

            @Override
            public void run() {
                Looper.prepare();
                try {

                    Thread.sleep(4000);
                    final String scannedMacAddress = PrinterFactory.getPrinterAddress();
                    String deviceName = getBluetoothName();
                    activity.runOnUiThread(new Runnable() {

                        @Override
                        public void run() {
                            if (!Utils.isNullOrEmpty(scannedMacAddress)) {
                                macAddress.setText(scannedMacAddress + "");
                                deviceNameText.setText(deviceName + "");
                            }
                        }
                    });

                } catch (DeviceBTNotEnabledException e) {
                    Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                    startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);

                } catch (PrinterNotPairedException e) {
                    Intent intentBluetooth = new Intent();
                    intentBluetooth.setAction(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS);
                    startActivityForResult(intentBluetooth, REQUEST_SCAN_BT);

                } catch (PrinterNotActiveException e1) {
                    scanResultDialog(e1.getMessage() + "", PrinterSetupActivity.this);
                    Logger.e("", e1);

                } catch (DeviceBTNotAvailableException e1) {
                    scanResultDialog(e1.getMessage() + "", PrinterSetupActivity.this);
                    Logger.e("", e1);

                } catch (Exception e1) {
                    scanResultDialog(e1.getMessage() + "", PrinterSetupActivity.this);
                    Logger.e("", e1);
                }
                progressDialog.dismiss();
            }

        }.start();

    }

    private String getBluetoothName() {
        String deviceName = "";
        Set<BluetoothDevice> pairedDevices = BluetoothAdapter.getDefaultAdapter().getBondedDevices();
        if (pairedDevices.size() > 0) {
            for (BluetoothDevice d: pairedDevices) {
                deviceName = d.getName();
            }
        }
        return deviceName;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_ENABLE_BT && resultCode == RESULT_OK) {
            Log.e("Search bluetooth", requestCode + "/" + resultCode + "/" + data + "");
            scanForMacAddress();
        } else if (requestCode == REQUEST_SCAN_BT) {
            Log.e("Search bluetooth", requestCode + "/" + resultCode + "/" + data + "");
            scan.setText("Re-Scan");
            if (cnt == getPairedDeviceCnt()) {
            } else {
                scanForMacAddress();
            }
        }
    }

    private int getPairedDeviceCnt() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        Set<BluetoothDevice> pairedDevices = null;
        if(bluetoothAdapter!=null){
            pairedDevices = bluetoothAdapter.getBondedDevices();
            return pairedDevices.size();
        }else{
            return 0;
        }

    }

    private void deleteMacAddressEntryFromDB() {

        try {
            String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_ADDRESS + "' ";
            String deviceNameWhereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_NAME + "' ";
            IDataStructure[] iDataStructure;
            try {
               ApplicationManager.getInstance().getDataManager().delete(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);
               ApplicationManager.getInstance().getDataManager().delete(CUSTOMIZATION_HEADER.TABLE_NAME, deviceNameWhereClause);
            } catch (DBException e) {
                Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "deleteMacAddress", "DBException while saveMacAddress: " + Utils.getExceptionMessage(e));
            }
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, this.getClass().getName(), "deleteMacAddress", Utils.getExceptionMessage(e));
            Logger.e("", e);
        }
    }


    private void saveMacAddress() {

        try {

            String macAddr = macAddress.getText().toString();
            if (!Utils.isNullOrEmpty(macAddr)) {

                String whereClause = CUSTOMIZATION_HEADER.FIELD_KEY_NAME + " = '" + Constants.PRINTER_ADDRESS + "' ";
                IDataStructure[] iDataStructure;
                try {
                    iDataStructure = ApplicationManager.getInstance().getDataManager().get(CUSTOMIZATION_HEADER.TABLE_NAME, whereClause);
                    if (iDataStructure == null || iDataStructure.length <= 0) {
                        insertIntoTable();
                    } else {
                        CUSTOMIZATION_HEADER settingsHeader = (CUSTOMIZATION_HEADER) iDataStructure[0];

                        settingsHeader.setKEY_VALUE(macAddr);

                        settingsHeader.setObjectStatus(OBJECT_STATUS.MODIFY);
                        settingsHeader.setSyncStatus(SYNC_STATUS.NONE);

                        ApplicationManager.getInstance().getDataManager().update(settingsHeader);
                    }
                    finish();
                } catch (DBException e) {
                    Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "saveMacAddress", "DBException while saveMacAddress: " + Utils.getExceptionMessage(e));
                }
            } else
                Toast.makeText(activity, "Please scan for Bluetooth device, then save.", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, this.getClass().getName(), "saveMacAddress", Utils.getExceptionMessage(e));
            Logger.e("", e);
        }
    }

    private void insertIntoTable() throws DBException {

        try {
            CUSTOMIZATION_HEADER settingsHeader = new CUSTOMIZATION_HEADER();
            String macAddr = macAddress.getText().toString();
            settingsHeader.setKEY_NAME(Constants.PRINTER_ADDRESS);
            settingsHeader.setKEY_VALUE(macAddr);
            settingsHeader.setObjectStatus(OBJECT_STATUS.ADD);
            settingsHeader.setSyncStatus(SYNC_STATUS.NONE);
           ApplicationManager.getInstance().getDataManager().insertOrUpdateBasedOnGID(settingsHeader);
        } catch (DBException e) {
            Logger.log(Logger.LEVEL_ERROR, "PrinterSetup", "createTable", "DBException while createTable: " + Utils.getExceptionMessage(e));
        }
    }

    @Override
    public void onBackPressed() {
        whenBackPressed();
    }

    private void whenBackPressed() {

        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn(
                getString(R.string.confirmation),
                "Are you sure want to exit?",
                "Cancel",
                "Yes",
                new DialogClickListner() {
                    @Override
                    public void onPositiveClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();

                    }

                    @Override
                    public void onNegativeClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                        finish();
                    }

                    @Override
                    public void onNeutralClick(Dialog dialogMessage) {

                    }
                }

        );

    }

    private void confirmAndClearPrinterSetupEntry() {

        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", getString(R.string.cancel_dialog), "Cancel", "Ok", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialog) {
                dialog.dismiss();
            }

            @Override
            public void onNegativeClick(Dialog dialog) {
                dialog.dismiss();
                clearPrinterSetUpEntry();
                deleteMacAddressEntryFromDB();
                scanForMacAddress();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });

    }

    private void scanResultDialog(final String exception, final Context context) {

        this.runOnUiThread(new Runnable() {

            @Override
            public void run() {
                DialogMessage dialogMessage = new DialogMessage(PrinterSetupActivity.this);
                if(exception!=null)
                dialogMessage.showDialogWithPositive(
                        getString(R.string.print_status),

                        exception
                        , "Okay", new DialogClickListner() {
                            @Override
                            public void onPositiveClick(Dialog dialogMessage) {
                                dialogMessage.dismiss();
                            }

                            @Override
                            public void onNegativeClick(Dialog dialogMessage) {

                            }

                            @Override
                            public void onNeutralClick(Dialog dialogMessage) {

                            }
                        });

            }
        });
    }

    private void setToolBar() {
        Toolbar toolbar = (Toolbar) findViewById(R.id.printer_setup_activity_tool_bar);
        toolbar.setTitleTextColor(getResources().getColor(R.color.white));
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(null);
    }
}
