package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;

import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import androidx.appcompat.app.AppCompatActivity;

import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.SnackBarToast;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.BaseFont;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;
import com.unvired.pdf.writer.PDFDocument;
import com.unvired.ui.Home;

import java.io.File;
import java.io.IOException;

public class OdometerActivity extends AppCompatActivity implements DialogClickListner, IPrintCallBack {
    TextInputEditText mReading,mConfirmReading;
    private double finalReading = 0;
    private SHIPMENT_HEADER currentShipmentHeader;
    OdometerActivity activity;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_odometer);
        currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
        iniViews();
        activity =this;

    }

    @Override
    public void onBackPressed() {

    }

    @Override
    protected void onResume() {
        super.onResume();

    }
    private void printInitialStockReportPrint() throws IOException, DocumentException {
        Loader.showLoader(this, "Please wait while printing");
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        Font font = Utils.getFont(10f);
        PDFDocument pdfDocument = new PDFDocument(getFilesDir() + "",
                PageSize.A6.getWidth(), 10.0f, 10.0f, 15, 5.0f, font, getBaseContext());
        ReportHelper.getInstance().setPDFDocument(pdfDocument);
        InvoiceReport invoiceReport = new InvoiceReport(font, false);
        invoiceReport.stockReportPrint(Constants.TRIP_INSP_TYPE_PRETRIP,false);
        String pdfPath = pdfDocument.generatePdf();
        Loader.hideLoader();
         if(PrinterUtils.printFlag){
            if (!Utils.isNullOrEmpty(pdfPath))
                PrinterUtils.print(pdfPath, activity, this,null,null,false,false);
        }else{
             getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
             updateStatus();
             navigateToTripSummary();
        }

    }

    private void updateStatus(){
        if(currentShipmentHeader.getSTAT()== Constants.SHIP_STATUS_PRE_FORKLIFT_COMPL){
            currentShipmentHeader.setSTART_MILE(finalReading);
        }
        currentShipmentHeader.setEND_MILE(finalReading);
        currentShipmentHeader.setSTAT((long) Constants.SHIP_STATUS_INITIAL_ODOMETER_COMPL);
        DBHelper.getInstance().updateShipmentStatus(currentShipmentHeader);
        DBHelper.getInstance().createNewTime(currentShipmentHeader.getSHIP_NO(),Constants.TIME_TYPE_ODOMETER_INITIAL);
    }

    private void iniViews() {
        mReading=findViewById(R.id.xOdometerReading);
        mConfirmReading=findViewById(R.id.xOdometerConfirmReading);
    }


    public void navigateToSettings(View view) {
        Utils.showSettingDialog(OdometerActivity.this);
    }

    public void validateReading(View view) {
            if(mReading.getText().toString().isEmpty()){
                SnackBarToast.showMessage(view,getString(R.string.readings_mismatch));
            }else if(mConfirmReading.getText().toString().isEmpty()){
                SnackBarToast.showMessage(view,getString(R.string.readings_mismatch));
            }else if(!mReading.getText().toString().equals(mConfirmReading.getText().toString())){
                SnackBarToast.showMessage(view,getString(R.string.readings_mismatch));
            }else if(mConfirmReading.getText().toString().equals("0")){
                SnackBarToast.showMessage(view,getString(R.string.readings_zero));
            }else {
                finalReading = Double.parseDouble(mConfirmReading.getText().toString());
                DialogMessage dialogMessage = new DialogMessage(OdometerActivity.this);
                dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.proceed_alert_message),"YES","NO",OdometerActivity.this);
            }
    }

    private void processData() {

        //if its initial odometer, update the the START MILE to current odometer.

        try {
            printInitialStockReportPrint();
        } catch (DocumentException | IOException e) {
            Logger.e("", e);
        }


    }

    @Override
    public void onPositiveClick(Dialog dialogMessage) {
        dialogMessage.dismiss();
        processData();
    }

    private void navigateToTripSummary() {
        //Update Time
        TimeController.createNewTime(DBHelper.getInstance().getShipmentRow().getSHIP_NO(), Constants.TIME_TYPE_DEPOT_STOCK_INITIAL);
        startActivity(new Intent(OdometerActivity.this,TripSummaryActivity.class));
        finish();
    }

    @Override
    public void onNegativeClick(Dialog dialogMessage) {
            dialogMessage.dismiss();
    }

    @Override
    public void onNeutralClick(Dialog dialogMessage) {

    }


    @Override
    public void onPrintSuccess(Enum<Constants.DocType> type,Constants.CopyType copyType,boolean isLast,String filePath,boolean reprint) {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        updateStatus();
        //save in storage for testing purpose
        File file = new File(filePath);
        if(!PrinterUtils.printFlag){
            try {
                InvoiceReport.exportFile(file,file.getName(),this);
            } catch (IOException e) {
                Logger.e("", e);
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
        navigateToTripSummary();
    }

    @Override
    public void onPrintError(Exception e, Enum<Constants.DocType> type) {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
    }
}