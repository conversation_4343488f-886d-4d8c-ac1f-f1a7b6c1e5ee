package com.abinbev.oasis.activities;


import android.app.ProgressDialog;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


import com.abinbev.oasis.adapter.DepoListAdapter;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.IMAGE_HEADER;
import com.abinbev.oasis.util.AlertControl;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PAHelper;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.IDataStructure;
import com.unvired.model.InfoMessage;
import com.unvired.oasis.R;
import com.unvired.sync.out.ISyncAppCallback;
import com.unvired.sync.response.ISyncResponse;
import com.unvired.sync.response.SyncBEResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;
import java.util.Vector;

public class DepotListActivity extends AppCompatActivity {

    private ProgressDialog progressDialog;

    private RecyclerView recyclerView;
    private DepoListAdapter adapter;
    private DepotListActivity context = this;

    private String responseCode;
    private String responseText;

    private List<DEPOT_HEADER> depotHeaderList = null;
    private List<IMAGE_HEADER> imageHeaderList = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_depot_list);
        recyclerView = (RecyclerView) findViewById(R.id.depotRecyclerView);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setItemAnimator(new DefaultItemAnimator());
        recyclerView.setHasFixedSize(true);
        this.loadData();
    }

    private void loadData() {
        Loader.showLoader(this, getString(R.string.downloading_image));
        downloadImages();
    }

    @Override
    public void onBackPressed() {
    }

    private void loadList() {
        adapter = new DepoListAdapter(this, depotHeaderList, Constants.MODE_GET);
        recyclerView.setAdapter(adapter);
        adapter.notifyDataSetChanged();

    }

    private void downloadImages() {

        imageHeaderList = new ArrayList<>();

        final ISyncAppCallback callback = new ISyncAppCallback() {
            @Override
            public void onResponse(ISyncResponse iSyncResponse) {
                SyncBEResponse syncBEResponse;
                responseText = null;

                if (iSyncResponse == null) {
                    responseCode = Constants.RESPONSE_CODE_ERROR;
                    responseText = getResources().getString(R.string.invalidResponse);
                } else {

                    switch (iSyncResponse.getResponseStatus()) {
                        case SUCCESS:

                            if (iSyncResponse instanceof SyncBEResponse) {

                                syncBEResponse = (SyncBEResponse) iSyncResponse;

                                responseCode = Constants.RESPONSE_CODE_SUCCESSFUL;
                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        responseCode = infoMessages.get(i).getCategory().equals(InfoMessage.CATEGORY_SUCCESS) ? Constants.RESPONSE_CODE_SUCCESSFUL : Constants.RESPONSE_CODE_ERROR;

                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();
                                }

                                if (responseText == null || responseText.trim().isEmpty()) {
                                    responseText = getResources().getString(R.string.DepotListDownloadSuccess);
                                }

                                if (responseCode.equals(Constants.RESPONSE_CODE_SUCCESSFUL)) {
                                    Hashtable<String, Hashtable<IDataStructure, Vector<IDataStructure>>> dataBEs = syncBEResponse.getDataBEs();
                                    Hashtable<IDataStructure, Vector<IDataStructure>> tempCollectionOfHeaderAndItems = null;

                                    if (!dataBEs.isEmpty()) {

                                        Enumeration<String> beKeys = dataBEs.keys();

                                        if (beKeys.hasMoreElements()) {
                                            String customerBEName = beKeys.nextElement();
                                            tempCollectionOfHeaderAndItems = dataBEs.get(customerBEName);

                                            Enumeration<IDataStructure> imageHeaderKeys = tempCollectionOfHeaderAndItems.keys();

                                            while (imageHeaderKeys.hasMoreElements()) {
                                                IMAGE_HEADER imageHeader = (IMAGE_HEADER) imageHeaderKeys.nextElement();
                                                imageHeaderList.add(imageHeader);
                                            }

                                        }
                                    }
                                }
                            }
                            break;

                        case FAILURE:
                            responseCode = Constants.RESPONSE_CODE_ERROR;

                            if (iSyncResponse instanceof SyncBEResponse) {
                                syncBEResponse = (SyncBEResponse) iSyncResponse;
                                responseText = syncBEResponse.getErrorMessage();

                                if (syncBEResponse.getErrorMessage().contains(getResources().getString(R.string.invalidResponse))) {
                                    responseText = getResources().getString(R.string.invalidResponse);
                                } else {
                                    responseText = syncBEResponse.getErrorMessage();
                                }

                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }
                                    responseText = infoMsgs.toString();
                                }

                                if (responseText.trim().isEmpty())
                                    responseText = getResources().getString(R.string.invalidResponse);

                            } else {
                                responseText = getResources().getString(R.string.invalidResponse);
                            }
                            break;
                    }

                    if (responseCode != null && responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Loader.hideLoader();
                                AlertControl.showInfo(context, responseText);
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Loader.hideLoader();
                                Loader.showLoader(context, getString(R.string.downloading_depot_list));
                                downloadDepotList();
                            }
                        });
                    }
                }
            }
        };
        new Thread(new Runnable() {
            @Override
            public void run() {
                PAHelper.getImages(callback);
            }
        }).start();
    }

    public void downloadDepotList() {

        depotHeaderList = new ArrayList<>();

        final ISyncAppCallback callback = new ISyncAppCallback() {
            @Override
            public void onResponse(ISyncResponse iSyncResponse) {

                SyncBEResponse syncBEResponse;
                responseText = null;

                if (iSyncResponse == null) {
                    responseCode = Constants.RESPONSE_CODE_ERROR;
                    responseText = getResources().getString(R.string.invalidResponse);
                } else {

                    switch (iSyncResponse.getResponseStatus()) {
                        case SUCCESS:

                            if (iSyncResponse instanceof SyncBEResponse) {

                                syncBEResponse = (SyncBEResponse) iSyncResponse;

                                responseCode = Constants.RESPONSE_CODE_SUCCESSFUL;
                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        responseCode = infoMessages.get(i).getCategory().equals(InfoMessage.CATEGORY_SUCCESS) ? Constants.RESPONSE_CODE_SUCCESSFUL : Constants.RESPONSE_CODE_ERROR;

                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();
                                }

                                if (responseText == null || responseText.trim().isEmpty()) {
                                    responseText = getResources().getString(R.string.DepotListDownloadSuccess);
                                }

                                if (responseCode.equals(Constants.RESPONSE_CODE_SUCCESSFUL)) {
                                    Hashtable<String, Hashtable<IDataStructure, Vector<IDataStructure>>> dataBEs = syncBEResponse.getDataBEs();
                                    Hashtable<IDataStructure, Vector<IDataStructure>> tempCollectionOfHeaderAndItems = null;

                                    if (!dataBEs.isEmpty()) {

                                        Enumeration<String> beKeys = dataBEs.keys();

                                        if (beKeys.hasMoreElements()) {
                                            String customerBEName = beKeys.nextElement();
                                            tempCollectionOfHeaderAndItems = dataBEs.get(customerBEName);

                                            Enumeration<IDataStructure> depotHeaderKeys = tempCollectionOfHeaderAndItems.keys();

                                            while (depotHeaderKeys.hasMoreElements()) {
                                                DEPOT_HEADER depotHeader = (DEPOT_HEADER) depotHeaderKeys.nextElement();
                                                depotHeaderList.add(depotHeader);

                                            }
                                            Collections.sort(depotHeaderList, new Comparator<DEPOT_HEADER>() {
                                                @Override
                                                public int compare(DEPOT_HEADER lhs, DEPOT_HEADER rhs) {
                                                    return lhs.getDEPOT().compareTo(rhs.getDEPOT());
                                                }
                                            });
//                                            loadList();
                                        }
                                    }
                                }
                            }
                            break;

                        case FAILURE:
                            responseCode = Constants.RESPONSE_CODE_ERROR;

                            if (iSyncResponse instanceof SyncBEResponse) {
                                syncBEResponse = (SyncBEResponse) iSyncResponse;
                                responseText = syncBEResponse.getErrorMessage();

                                if (syncBEResponse.getErrorMessage().contains(getResources().getString(R.string.invalidResponse))) {
                                    responseText = getResources().getString(R.string.invalidResponse);
                                } else {
                                    responseText = syncBEResponse.getErrorMessage();
                                }

                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }
                                    responseText = infoMsgs.toString();
                                }

                                if (responseText.trim().isEmpty())
                                    responseText = getResources().getString(R.string.invalidResponse);

                            } else {
                                responseText = getResources().getString(R.string.invalidResponse);
                            }
                            break;
                    }

                    if (responseCode != null && responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Loader.hideLoader();
                                AlertControl.showInfo(context, responseText);
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Collections.sort(depotHeaderList, new Comparator<DEPOT_HEADER>() {
                                    @Override
                                    public int compare(DEPOT_HEADER lhs, DEPOT_HEADER rhs) {
                                        return lhs.getDEPOT().compareTo(rhs.getDEPOT());
                                    }
                                });
                                Loader.hideLoader();
                                loadList();
                            }
                        });
                    }
                }
            }
        };

        /*
         * Always execute Process Agent(PA) in thread
         */
        new Thread(new Runnable() {
            @Override
            public void run() {
                PAHelper.getDepotList(callback);
            }
        }).start();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(DepotListActivity.this);
    }
}