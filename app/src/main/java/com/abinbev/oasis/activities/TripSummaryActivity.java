package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.Button;

import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.adapter.TripSummaryAdapter;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TripSummaryActivity extends AppCompatActivity {
    private RecyclerView mRecyclerView;
    private List<TRIP_SUMMARY> tripList;
    private Button mEndTrip;
    @Override
    protected void onCreate(Bundle savedInstanceState)
    {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_trip_summary);
        iniViews();
//      SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
    }

    @Override
    public void onBackPressed() {

    }

    @Override
    protected void onResume() {
        super.onResume();
        getTripList();
    }

    private void getTripList() {
        tripList=new ArrayList<>();
        HashMap<Integer,Integer> NO_OF_DELIVERIES = new HashMap<>();
        tripList= TripSummaryController.getInstance().getTripList(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
        NO_OF_DELIVERIES =TripSummaryController.getInstance().get_No_Of_Deliveries();
        if(tripList!=null && NO_OF_DELIVERIES!=null){
            TripSummaryAdapter adapter = new TripSummaryAdapter(this,tripList,NO_OF_DELIVERIES);
            mRecyclerView.setHasFixedSize(true);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            mRecyclerView.setAdapter(adapter);
        }
        if(TripSummaryController.getInstance().isAllVisitsComplete()){
            mEndTrip.setVisibility(View.VISIBLE);
        }else {
            mEndTrip.setVisibility(View.GONE);
        }
    }


    private void iniViews()
    {
        mRecyclerView=findViewById(R.id.xTripSummaryRecyclerview);
        mEndTrip=findViewById(R.id.xTripSummaryEndTrip);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(TripSummaryActivity.this);
         }



    public void navigateToUnplannedCustomers(View view) {
        Logger.i("User in TripSummaryActivity. navigateToUnplannedCustomers");
        startActivity(new Intent(TripSummaryActivity.this,UnplannedCustomerListActivity.class));
    }


    public void endTrip(View view) {
        DialogMessage dialogMessage =new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("Post-Trip Inspection", "", "Perform Inspection", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                Intent intent = new Intent(TripSummaryActivity.this,PreTripInspectionQuestionsActivity.class);
                intent.putExtra(Constants.SCREEN_NAVIGATION,Constants.SCREEN_NAVIGATION_MODE.PostTripInspection);
                startActivity(intent);
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                SHIPMENT_HEADER currentShipment=DBHelper.getInstance().getCurrentShipment();
                currentShipment.setSTAT((long) Constants.SHIP_STATUS_END_TRIP);
                DBHelper.getInstance().updateShipmentStatus(currentShipment);
                DBHelper.getInstance().createNewTime(currentShipment.getSHIP_NO(),Constants.TIME_TYPE_END_TRIP);
                navigateToEndOfTripMenuActivity();
            }
        });
    }

    private void navigateToEndOfTripMenuActivity() {
        startActivity(new Intent(this,EndOfTripMenuActivity.class));
    }
}