package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.PalletCheckerDropDownAdapter;
import com.abinbev.oasis.adapter.ReasonDropDownAdapter;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.ReasonController;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Controllers.TripInspController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.database.IDataManager;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;
import com.unvired.pdf.writer.PDFDocument;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class FullLoadFBRReason extends AppCompatActivity {
    double current_status;
    private RadioGroup mradioFBRReason;
    private Spinner mReasonSpinner;
    private List<REASON_HEADER> reason_headerList;
    private REASON_HEADER selectedReason = null;
    private AUTH_HEADER selectedChecker = null;

    private IDataManager iDataManager = null;

    FullLoadFBRReason activity;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        try {
            iDataManager = ApplicationManager.getInstance().getDataManager();
        } catch (DBException e) {
            Logger.e("", e);
        }
        setContentView(R.layout.activity_full_load_fbr_reason);
        iniViews();
        activity = this;
    }


    @Override
    public void onBackPressed() {

    }

    private void iniViews() {
        mReasonSpinner=findViewById(R.id.xFullLoadFbrReasonSpinner);
        addListenerOnButton();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(FullLoadFBRReason.this);
    }

    public void validateAndClose(View view) {
        if(selectedReason==null || mReasonSpinner.getSelectedItemPosition() == 0){
            SnackBarToast.showMessage(view,getString(R.string.choose_reason_validation));
        }else {
            updateShipmentHeader();
            finish();
        }
    }
    
    public void updateShipmentHeader() {
        SHIPMENT_HEADER shipmentRow = DBHelper.getInstance().getShipmentRow();
        shipmentRow.setFULL_FBR_RSN_CODE(selectedReason.getRSN_CODE());
        DBHelper.getInstance().updateShipmentStatus(shipmentRow);
        Logger.i("User in FullLoadFBRReason. SHIPMENT_NO: "+shipmentRow.getSHIP_NO());
    }

    public void closeActivity(View view) { finish(); }

    public void addListenerOnButton() {

        mradioFBRReason = (RadioGroup) findViewById(R.id.FBRReason);
        mradioFBRReason.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener()
        {
            public void onCheckedChanged(RadioGroup group, int checkedId)
            {
                RadioButton warehouseFBR = (RadioButton)group.findViewById(R.id.warehouseFullFBR);
                RadioButton logisticFBR = (RadioButton)group.findViewById(R.id.logisticFullFBR);
                boolean isCheckedWarehouseFull = warehouseFBR.isChecked();
                boolean isCheckedLogisticsFull = logisticFBR.isChecked();
                if (isCheckedWarehouseFull)
                {
                    getData(Constants.REASON_TYPE_WAREHOUSE_FULL);
                } else if (isCheckedLogisticsFull) {
                    getData(Constants.REASON_TYPE_LOGISTIC_FULL);
                }
            }
        });
    }

    private void getData(String type) {
        reason_headerList = new ArrayList<>();
        Loader.showLoader(this,"Please wait");
        reason_headerList= ReasonController.getInstance().getReasons(type);
        Loader.hideLoader();
        if(reason_headerList==null || reason_headerList.size()<=0){
            Toast.makeText(this, "Reasons not available", Toast.LENGTH_SHORT).show();
            finish();
        }
        setupDropDown();
    }

    private void setupDropDown() {

        ReasonDropDownAdapter reasonDropDownAdapter = new ReasonDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item,reason_headerList,true);
        mReasonSpinner.setAdapter(reasonDropDownAdapter);
        mReasonSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if(position!=0){
                    reasonDropDownAdapter.setSelectionPosition(position);
                    selectedReason = reason_headerList.get(position-1);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }
}
