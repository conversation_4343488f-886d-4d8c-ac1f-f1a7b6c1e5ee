package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.adapter.CheckInFullsTableAdapter;
import com.abinbev.oasis.adapter.MaterialDroDownAdapter;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CheckInController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.FontAwesomeView;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CheckInFullsActivity extends AppCompatActivity implements CheckInFullsTableAdapter.TableClickListner {
    List<MATERIAL_HEADER> materialHeaderList;
    List<STOCK> stockList;
    private Spinner mMaterialSpinner;
    private EditText mQtyEt;
    private CheckBox mBitCheckBox;
    private FontAwesomeView mMinus;
    private TextView mNoData;
    private RecyclerView mRecyclerView;
    private MATERIAL_HEADER selectedMaterial = null;
    private STOCK selectedSmartGrid = null;
    CheckInFullsTableAdapter adapter = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_in_fulls);
        iniViews();
        getData();

    }

    @Override
    public void onBackPressed() {

    }

    private void iniViews() {
        mMaterialSpinner = findViewById(R.id.xCheckInFullsMaterialSpinner);
        mQtyEt = findViewById(R.id.xCheckInFullsQuantity);
        mBitCheckBox = findViewById(R.id.xCheckInFullsBitCheckBox);
        mMinus = findViewById(R.id.xCheckInFullsMinusIcon);
        mNoData = findViewById(R.id.xCheckInFullsNoDataAvailable);
        mRecyclerView = findViewById(R.id.xCheckInFullsRecyclerView);
    }

    private void getData() {
        materialHeaderList = MaterialController.getInstance().getAllFullMaterials();
        stockList = CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE);
        if (materialHeaderList != null) {
            setUpSpinner();
        }
        loadSmartGrid();
    }

    private void loadSmartGrid() {
        List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE);
        if (list != null && list.size() > 0) {
            List<STOCK> stockListTemp = new ArrayList<>();
            for (STOCK stock : list) {
                if (stock.getCHCK_IN_QTY() != null && stock.getCHCK_IN_QTY() > 0) {
                    stockListTemp.add(stock);
                }
            }
            if (stockListTemp.size() > 0) {
                mNoData.setVisibility(View.GONE);
                mRecyclerView.setVisibility(View.VISIBLE);
                adapter = new CheckInFullsTableAdapter(this, stockListTemp, this);
                mRecyclerView.setHasFixedSize(true);
                mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
                mRecyclerView.setAdapter(adapter);
            } else {
                mRecyclerView.setVisibility(View.GONE);
                mNoData.setVisibility(View.VISIBLE);
            }
        }
    }

    private void setUpSpinner() {
        final MaterialDroDownAdapter materialDroDownAdapter = new MaterialDroDownAdapter(this, R.layout.support_simple_spinner_dropdown_item, materialHeaderList, false, false);
        mMaterialSpinner.setAdapter(materialDroDownAdapter);
        mMaterialSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedMaterial = materialHeaderList.get(position);
                materialDroDownAdapter.setSelectionPosition(position);
                List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE);
                if (list != null && list.size() > 0) {
                    for (STOCK stock : list) {
                        if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                            selectedSmartGrid = stock;
                        }
                        if (stock.getCHCK_IN_QTY() != null && stock.getCHCK_IN_QTY() > 0) {
                            if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                                if (stock.getTYPE().equals(Constants.BIT_TYPE)) {
                                    mBitCheckBox.setChecked(true);
                                } else {
                                    mBitCheckBox.setChecked(false);
                                }
                                if (stock.getCHCK_IN_QTY() != null && !stock.getCHCK_IN_QTY().toString().isEmpty()) {
                                    // mQtyEt.setText(String.valueOf(stock.getCHCK_IN_QTY().intValue()));
                                    if (adapter != null) {
                                        adapter.setSelectedPos(stock);
                                    }
                                    break;
                                }
                            }
                        } else {
                            mQtyEt.setText("");
                            mBitCheckBox.setChecked(false);
                            if (adapter != null) {
                                adapter.resetSelectedPos();
                            }
                        }
                    }


                } else {
                    mQtyEt.setText("");
                    mBitCheckBox.setChecked(false);
                    if (adapter != null) {
                        adapter.resetSelectedPos();
                    }
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(CheckInFullsActivity.this);
    }

    public void minus_click(View view) {
        if (selectedSmartGrid != null) {
            if (selectedSmartGrid.getCHCK_IN_QTY() == null) {
                selectedSmartGrid.setCHCK_IN_QTY((double) 0);
            } else {
                selectedSmartGrid.setCHCK_IN_QTY((double) 0);
            }
            CheckInController.getInstance().updateStock(selectedSmartGrid);
        }
        clearAll();
        loadSmartGrid();
    }

    private void clearAll() {
        if (mBitCheckBox.isChecked()) {
            mBitCheckBox.setChecked(false);
        }
        mQtyEt.setText("");
    }

    public void ReduceStockItems(View view) {
        if (validate(view)) {
            if (selectedSmartGrid != null) {
                if (selectedSmartGrid != null) {
                    if (selectedSmartGrid.getCHCK_IN_QTY() == null) {
                        selectedSmartGrid.setCHCK_IN_QTY((double) 0);
                    } else {
                        selectedSmartGrid.setCHCK_IN_QTY((selectedSmartGrid.getCHCK_IN_QTY() - Integer.parseInt(mQtyEt.getText().toString())) <= 0 ? 0 : (selectedSmartGrid.getCHCK_IN_QTY() - Integer.parseInt(mQtyEt.getText().toString())));
                        CheckInController.getInstance().updateStock(selectedSmartGrid);
                        loadSmartGrid();
                    }
                }
            }
            //TO set the value of selectedSmartGrid to the selected material spinner position
            updateSelectedStock();
            clearAll();
        }
    }

    private boolean validate(View view) {
        if (selectedMaterial == null) {
            SnackBarToast.showMessage(view, "Please select a Product");
            return false;
        }

        if (mQtyEt.getText().toString().isEmpty()) {
            SnackBarToast.showMessage(view, "Please enter a Quantity");
            return false;
        }
        int value = 0;
        try {
            value = Integer.parseInt(mQtyEt.getText().toString());
        } catch (Exception e) {
            SnackBarToast.showMessage(view, "Please enter a valid quantity");
            return false;
        }

        if (value <= 0) {
            SnackBarToast.showMessage(view, "Please enter a valid value");
            return false;
        }

        return true;
    }

    public void add_button_click(View view) {
        addItemToGrid(view);
        loadSmartGrid();
    }

    private void addItemToGrid(View view) {
        String type;
        if (validate(view)) {
            if (mBitCheckBox.isChecked()) {
                type = Constants.BIT_TYPE;
            } else {
                type = Constants.FBR_TYPE;
            }
            List<STOCK> stockList = CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE);

            List<STOCK> stocks = new ArrayList<>();
            for (STOCK stock : stockList) {
                if (stock.getMAT_NO() != null && !stock.getMAT_NO().isEmpty() && stock.getMAT_NO().equals(selectedMaterial.getMAT_NO()) && stock.getTYPE() != null && !stock.getTYPE().isEmpty() && stock.getTYPE().equals(type)) {
                    stocks.add(stock);
                }
            }
            STOCK stockRow = null;
            if (stocks.size() > 0 && stocks.get(0) != null) {
                stockRow = stocks.get(0);
                stockRow.setCHCK_IN_QTY((stockRow.getCHCK_IN_QTY() == null ? 0 : stockRow.getCHCK_IN_QTY()) + Integer.parseInt(mQtyEt.getText().toString()));
            } else {
                try {
                    stockRow = new STOCK();
                    stockRow.setCHCK_IN_QTY(Double.valueOf(mQtyEt.getText().toString()));
                    stockRow.setFid(DBHelper.getInstance().getShipmentRow().getLid());
                    stockRow.setLid(stockRow.getLid());
                    stockRow.setMAT_DESC(selectedMaterial.getMAT_DESC());
                    stockRow.setMAT_NO(selectedMaterial.getMAT_NO());
                    stockRow.setSHIP_NO(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
                    stockRow.setTYPE(type);
                    stockRow.setUOM(selectedMaterial.getBASE_UOM());
                } catch (DBException e) {
                    Logger.e("", e);
                }
            }
            if (stockRow != null) {
                CheckInController.getInstance().insertOrUpdateStock(stockRow);
            }
            updateSelectedStock();
            clearAll();
        }
    }

    private void updateSelectedStock() {
        List<STOCK> list = CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE);
        if (list != null && list.size() > 0) {
            for (STOCK stock : list) {
                if (stock.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                    selectedSmartGrid = stock;
                }
            }
        }
    }

    @Override
    public void onTableClick(int position, STOCK stock) {
        selectedSmartGrid = stock;
        if (selectedSmartGrid != null) {
            if (selectedSmartGrid.getTYPE().equals(Constants.BIT_TYPE)) {
                mBitCheckBox.setChecked(true);
            } else {
                mBitCheckBox.setChecked(false);
            }

            if (selectedSmartGrid.getMAT_NO() == null || selectedSmartGrid.getMAT_NO().isEmpty()) {
                selectedMaterial = null;
                clearAll();
                return;
            }
//            if(selectedSmartGrid.getCHCK_IN_QTY()!=null && !selectedSmartGrid.getCHCK_IN_QTY().toString().isEmpty()){
//                mQtyEt.setText(String.valueOf(selectedSmartGrid.getCHCK_IN_QTY().intValue()));
//            }
            for (int i = 0; i < materialHeaderList.size(); i++) {
                if (materialHeaderList.get(i).getMAT_NO().equals(selectedSmartGrid.getMAT_NO())) {
                    mMaterialSpinner.setSelection(i);
                }
            }
        }
    }

    public void check_in_empties_click(View view) {
        if (!CheckInController.getInstance().checkDiscrepancy(CheckInController.getInstance().getStockMaterials(Constants.FBR_TYPE))) {
            DialogMessage dialogMessage = new DialogMessage(this);
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Some Discrepancy is noticed\nDo you want to Recount?", "Proceed", "Recount", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    startActivity(new Intent(CheckInFullsActivity.this, CheckInEmptiesActivity.class));
                    finish();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        } else {
            startActivity(new Intent(CheckInFullsActivity.this, CheckInEmptiesActivity.class));
            finish();
        }
    }

    public void goBackToPreviousActivity(View view) {
        finish();
    }
}