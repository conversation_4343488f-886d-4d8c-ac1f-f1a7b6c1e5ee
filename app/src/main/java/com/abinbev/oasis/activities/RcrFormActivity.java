package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TableLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.MaterialDroDownAdapter;
import com.abinbev.oasis.adapter.RcrTableAdapter;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RcrFormActivity extends AppCompatActivity implements RcrTableAdapter.TableClickListner {
    /*
        Form views
     */
    private TextView splitCasesResult, fullPalletsResult, totalCasesResult, clearForm, totalBottlesResult;
    private EditText splitCasesEntry, fullPalletsEntry;
    private ImageButton splitCasesAdd, fullPalletsAdd, splitCasesReduce, fullPalletsReduce;
    private Spinner mDeliverySpinner;
    private Spinner mProductSpinner;
    private TableLayout mTableLayout;
    private Button next;
    private LinearLayout bottlesLayout;
    /*
    Activity views
     */
    private TextView mNoDataAvailable;
    private RecyclerView mRecyclerView;
    private List<DELIVERY> deliveryList;
    private List<DELIVERY_ITEM>  delivery_itemList = new ArrayList<>();
    private DELIVERY_ITEM deliveryItemRow;
    private List<MATERIAL_HEADER> materialHeaderList;
    private DELIVERY selectedDelivery;
    private String shipmentLid = "";
    long casesPerPallet = 0;
    int rcrLimit = 0;
    private boolean truckQuantityExceeded = false;
    enum RCR_FIELDS {
        SPLIT_CASES,
        FULL_PALLETS
    }
    int x = 0;
    int totalCases = 0;
    private RcrTableAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_rcr_form);
        iniViews();
        try {
            getData();
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    @Override
    public void onBackPressed() {

    }

    @Override
    protected void onResume() {
        if(null != bottlesLayout) {
            bottlesLayout.setVisibility(View.GONE);
        }
        super.onResume();
    }

    private void getData() throws DBException {
        selectedDelivery = null;
        deliveryList = new ArrayList<>();
        deliveryList = DeliveryController.getInstance().getOpenDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.RCR);

        // Get customer header to check attributes
        CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());

        // Check if customer has SPLITBILLENABLED attribute
        boolean splitBillEnabled = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.SPLITBILLENABLED);

        // Check if customer has OSB2INVENABLED attribute
        boolean osb2InvEnabled = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.OSB2INVENABLED);

        String emptyOrdDelvNo = DeliveryController.getInstance().getFirstReturnsCollectionOrderDeliveryNo();

        // For both SPLITBILLENABLED and OSB2INVENABLED, show only Returns Collection Delivery Number
        if (splitBillEnabled || osb2InvEnabled) {
            if (emptyOrdDelvNo != null && !emptyOrdDelvNo.isEmpty()) {
                for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                    if (!deliveryList.get(i).getDELV_NO().equals(emptyOrdDelvNo)) {
                        deliveryList.remove(i);
                    }
                }
            }
        }
        materialHeaderList = new ArrayList<>();
        materialHeaderList = MaterialController.getInstance().getMaterials(DeliveryController.Quantity.RCR);
        Integer rcrLimitCust = CustomizingController.getInstance().getIntKeyValue(Constants.KEY_RCR_LIMIT);
        rcrLimit = rcrLimitCust == null ? Constants.RCR_LIMIT_DEFAULT : rcrLimitCust;
        setUpSpinner();
    }

    private void setUpSpinner() {
        final DeliveryDropDownAdapter deliveryDropDownAdapter = new DeliveryDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, deliveryList,true);
        mDeliverySpinner.setAdapter(deliveryDropDownAdapter);
        mDeliverySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                deliveryDropDownAdapter.setSelectionPosition(position);
                if(position!=0) {
                    if (deliveryList == null || deliveryList.size() == 0) {
                        selectedDelivery = null;
                    } else {
                        selectedDelivery = deliveryList.get(position-1);
                        LoadRCRSmartGrid();
                    }
                }else {
                    clearFormFields();
                    mRecyclerView.setVisibility(View.GONE);
                    mNoDataAvailable.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });


        if(deliveryList.size()==1){
            mDeliverySpinner.setSelection(1);
        }
        final MaterialDroDownAdapter materialDroDownAdapter = new MaterialDroDownAdapter(this, R.layout.support_simple_spinner_dropdown_item, materialHeaderList, true,true);
        mProductSpinner.setAdapter(materialDroDownAdapter);
        mProductSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                MATERIAL_HEADER selectedMaterialHeader;
                if (position == 0) {
                    selectedMaterialHeader = null;
                } else {
                    selectedMaterialHeader = materialHeaderList.get(position - 1);
                    materialDroDownAdapter.setSelectionPosition(position);
                }
                if (selectedMaterialHeader != null) {
                    boolean deliveryItemFound = false;
                    if (delivery_itemList != null && delivery_itemList.size() > 0) {
                        List<DELIVERY_ITEM> selectedDeliveries = new ArrayList<>();
                        for (DELIVERY_ITEM delvItem : delivery_itemList) {
                            if (delvItem.getMAT_NO() != null && !delvItem.getMAT_NO().isEmpty() && delvItem.getMAT_NO().equals(selectedMaterialHeader.getMAT_NO())) {
                                selectedDeliveries.add(delvItem);
                            }
                        }
                        if (selectedDeliveries.size() > 0 && selectedDeliveries.get(0) != null) {
                            deliveryItemRow = selectedDeliveries.get(0);
                            deliveryItemFound = true;
                        }
                    }
                    if (!deliveryItemFound) {
                        if (selectedDelivery == null) {
                            SnackBarToast.showMessage(view, "Please select a Delivery");
                            return;
                        }
                        String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
                        //String ship_no = "11312033";
                        //create delivery item
                        try {
                            deliveryItemRow = new DELIVERY_ITEM();
                            deliveryItemRow.setLid(deliveryItemRow.getLid());
                            deliveryItemRow.setSHIP_NO(ship_no);
                            deliveryItemRow.setDELV_NO(selectedDelivery.getDELV_NO());
                            deliveryItemRow.setMAT_NO(selectedMaterialHeader.getMAT_NO());
                            deliveryItemRow.setMAT_DESC(selectedMaterialHeader.getMAT_DESC() == null ? "" : selectedMaterialHeader.getMAT_DESC());
                            deliveryItemRow.setITM_NO(DeliveryController.getInstance().getHighestItemNoOfDeliveryItems(selectedDelivery.getDELV_NO()));
                            deliveryItemRow.setSLS_UOM(selectedMaterialHeader.getBASE_UOM() == null ? "" : selectedMaterialHeader.getBASE_UOM());
                            if (shipmentLid.isEmpty()) {
                                shipmentLid = String.valueOf(DBHelper.getInstance().getCurrentShipment().getLid());
                            }
                            deliveryItemRow.setFid(shipmentLid);
                            deliveryItemRow.setIS_RETURN("X");
                            deliveryItemRow.setIS_EMPTY("X");
                            deliveryItemRow.setHH_CREATED("X");
                        } catch (DBException e) {
                            Logger.e("", e);
                        }
                    }

                    if (selectedMaterialHeader.getCS_PER_PAL() != null) {
                        casesPerPallet = selectedMaterialHeader.getCS_PER_PAL();
                    } else {
                        casesPerPallet = 0;
                    }
                    populateDeliveryItemRow();
                    showHideBottlesSection(selectedMaterialHeader);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void showHideBottlesSection(MATERIAL_HEADER selectedMaterialHeader){

        if(null != bottlesLayout) {
            bottlesLayout.setVisibility(View.GONE);
        }

        if(null != totalBottlesResult && null != selectedMaterialHeader && null != bottlesLayout) {
            CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());
            if(null != customer_header) {
                bottlesLayout.setVisibility(
                        Utils.isAttributeEnabled(customer_header, Constants.AttributeType.BOTTLESENABLED) &&
                        selectedMaterialHeader.getMAT_DESC().startsWith("B&CR") ? View.VISIBLE : View.GONE);
            }
        }
    }
    private void populateDeliveryItemRow() {
        double RCR_Quantity = deliveryItemRow.getRCR_QTY() == null || deliveryItemRow.getRCR_QTY().toString().isEmpty() || deliveryItemRow.getRCR_QTY() <= 0 ? 0 : deliveryItemRow.getRCR_QTY();
        if (RCR_Quantity > 0 && casesPerPallet > 0) {
            fullPalletsResult.setText(String.valueOf((int) (RCR_Quantity / casesPerPallet)));
            splitCasesResult.setText(String.valueOf((int) RCR_Quantity % casesPerPallet));
            totalCasesResult.setText(String.valueOf((int) RCR_Quantity));
            totalBottlesResult.setText(String.valueOf(((int) RCR_Quantity * Constants.B_CR_BOTTLES_PER_CASE_VALUE)));
        } else {
            fullPalletsResult.setText("");
            splitCasesResult.setText("");
            totalCasesResult.setText("");
            totalBottlesResult.setText("");
        }
    }


    private void LoadRCRSmartGrid() {
        if(delivery_itemList!=null){
            if (delivery_itemList.size() > 0) {
                delivery_itemList.clear();
            }
        }
        delivery_itemList = DeliveryController.getInstance().getDeliveryItems(selectedDelivery.getDELV_NO(), DeliveryController.Quantity.RCR);
        if (delivery_itemList == null || delivery_itemList.size() <= 0) {
            clearFormFields();
            mRecyclerView.setVisibility(View.GONE);
            mNoDataAvailable.setVisibility(View.VISIBLE);
            return;
        }

        String[] palletMats = MaterialController.getInstance().getWoodenPalletMaterials();
        List<DELIVERY_ITEM> smartGridRows = new ArrayList<>();
         for (DELIVERY_ITEM delivery_item : delivery_itemList) {
            if (delivery_item.getRCR_QTY() != null && delivery_item.getRCR_QTY() > 0 && !Arrays.asList(palletMats).contains(delivery_item.getMAT_NO())) {
                smartGridRows.add(delivery_item);
            }
        }
        if (smartGridRows.size() > 0) {
            mNoDataAvailable.setVisibility(View.GONE);
            mRecyclerView.setVisibility(View.VISIBLE);
            adapter = new RcrTableAdapter(this, smartGridRows, this);
            mRecyclerView.setHasFixedSize(true);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            mRecyclerView.setAdapter(adapter);
            clearFormFields();
        } else {
            clearFormFields();
            mRecyclerView.setVisibility(View.GONE);
            mNoDataAvailable.setVisibility(View.VISIBLE);
        }
    }
    private void iniViews() {
        mNoDataAvailable = findViewById(R.id.xRcrNoDataAvailable);
        splitCasesResult = findViewById(R.id.xRcrBottomSheetSplitCasesResult);
        fullPalletsResult = findViewById(R.id.xRcrBottomSheetFullPalletsResult);
        totalCasesResult = findViewById(R.id.xRcrBottomSheetTotalCasesResult);
        clearForm = findViewById(R.id.xRcrBottomSheetClearAll);
        splitCasesEntry = findViewById(R.id.xRcrBottomSheetSplitCasesEntry);
        fullPalletsEntry = findViewById(R.id.xRcrBottomSheetFullPalletsEntry);
        next = findViewById(R.id.xRcrBottomSheetNextButton);
        splitCasesAdd = findViewById(R.id.xRcrBottomSheetSplitCasesAdd);
        fullPalletsAdd = findViewById(R.id.xRcrBottomSheetFullPalletsAdd);
        splitCasesReduce = findViewById(R.id.xRcrBottomSheetSplitCasesReduce);
        fullPalletsReduce = findViewById(R.id.xRcrBottomSheetFullPalletsReduce);
        mDeliverySpinner = findViewById(R.id.xRcrBottomSheetDeliverySpinner);
        mProductSpinner = findViewById(R.id.xRcrBottomSheetProductSpinner);
        mTableLayout = findViewById(R.id.xRcrTableLayout);
        mRecyclerView = findViewById(R.id.xRcrRecyclerView);
        totalBottlesResult = findViewById(R.id.xRcrBottomSheetTotalBottlesCount);
        bottlesLayout = findViewById(R.id.bottlesLayout);
    }

    private void validateCases(View view, RCR_FIELDS rcrField) {
        if (deliveryItemRow == null) {
            SnackBarToast.showMessage(view, "Please select a Product");
            return;
        }
        switch (rcrField) {
            case SPLIT_CASES:
                if(splitCasesEntry.getText().toString().equals("0")){
                    splitCasesEntry.setText("");
                    SnackBarToast.showMessage(view, "Cannot add 0 quantity");
                }else{
                    if (!splitCasesEntry.getText().toString().isEmpty()) {
                        updateCases(view, rcrField);
                        splitCasesEntry.setText("");
                        if (!truckQuantityExceeded) {
                            if (casesPerPallet > 0 && x >= casesPerPallet) {
                                fullPalletsResult.setText(String.valueOf((int) (totalCases / casesPerPallet)));
                                splitCasesResult.setText(String.valueOf((int) totalCases % casesPerPallet));
                            } else {
                                splitCasesResult.setText(String.valueOf(x));
                            }
                            totalCasesResult.setText(String.valueOf((int) totalCases));
                        }
                    }
                }

                break;
            case FULL_PALLETS:
                if(fullPalletsEntry.getText().toString().equals("0")){
                    fullPalletsEntry.setText("");
                    SnackBarToast.showMessage(view, "Cannot add 0 quantity");
                }else{
                    if (!fullPalletsEntry.getText().toString().isEmpty()) {
                        updateCases(view, rcrField);
                        fullPalletsEntry.setText("");
                        if (!truckQuantityExceeded) {
                            fullPalletsResult.setText(String.valueOf(x));
                            totalCasesResult.setText(String.valueOf((int) totalCases));
                        }
                    }
                }

                break;
        }
    }

    private void updateCases(View view, RCR_FIELDS rcrField) {
        x = 0;
        totalCases = 0;
        switch (rcrField) {
            case SPLIT_CASES:
                x = splitCasesResult.getText().toString().isEmpty() ? 0 : Integer.parseInt(splitCasesResult.getText().toString());
                x = x + Integer.parseInt(splitCasesEntry.getText().toString());
                int tempX = fullPalletsResult.getText().toString().isEmpty() ? 0 : Integer.parseInt(fullPalletsResult.getText().toString());
                totalCases = (int) ((tempX * casesPerPallet) + x);
                break;

            case FULL_PALLETS:
                x = fullPalletsResult.getText().toString().isEmpty() ? 0 : Integer.parseInt(fullPalletsResult.getText().toString());
                x = x + Integer.parseInt(fullPalletsEntry.getText().toString());
                int tempXy = splitCasesResult.getText().toString().isEmpty() ? 0 : Integer.parseInt(splitCasesResult.getText().toString());
                totalCases = (int) (tempXy + (x * casesPerPallet));
                break;
        }
        double emptyTruckStock = DeliveryController.getInstance().getEmptyStockInTruck(deliveryItemRow.getDELV_NO());
        long shipmentLimit = DBHelper.getInstance().getCurrentShipment().getRCR_LIMIT();
        List<DELIVERY_ITEM> delivery_itemListTemp = DeliveryController.getInstance().getDeliveryItems(deliveryItemRow.getDELV_NO(), DeliveryController.Quantity.ALL);
        if (delivery_itemListTemp != null && delivery_itemListTemp.size() > 0) {
            for (int i = 0; i < delivery_itemListTemp.size(); i++) {
                DELIVERY_ITEM delvItem = delivery_itemListTemp.get(i);
                if (delvItem != null) {
                    double fbrQty = delvItem.getFBR_QTY() == null ? 0 : delvItem.getFBR_QTY();
                    double bitQty = delvItem.getBIT_QTY() == null ? 0 : delvItem.getBIT_QTY();
                    double rcrQty = delvItem.getRCR_QTY() == null ? 0 : delvItem.getRCR_QTY();
                    double ubrQty = delvItem.getUBR_QTY() == null ? 0 : delvItem.getUBR_QTY();
                    emptyTruckStock = emptyTruckStock + (fbrQty + bitQty + rcrQty + ubrQty);
                }
            }
        }
        emptyTruckStock = emptyTruckStock - (deliveryItemRow.getRCR_QTY() == null ? 0 : deliveryItemRow.getRCR_QTY());
        if (totalCases + emptyTruckStock > shipmentLimit) {
            SnackBarToast.showMessage(view, "Please reduce quantity - total truck capacity exceeded");
            truckQuantityExceeded = true;
        } else {
            truckQuantityExceeded = false;
        }
        if(null != totalBottlesResult) {
            totalBottlesResult.setText(String.valueOf((totalCases * Constants.B_CR_BOTTLES_PER_CASE_VALUE)));
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(RcrFormActivity.this);
    }

    public void full_pallets_add_click(View view) {
        validateCases(view, RCR_FIELDS.FULL_PALLETS);
    }

    public void split_cases_add_click(View view) {
        validateCases(view, RCR_FIELDS.SPLIT_CASES);
    }

    public void split_cases_minus_click(View view) {
        reduceCases(view, RCR_FIELDS.SPLIT_CASES);
    }

    public void full_pallets_minus_click(View view) {
        reduceCases(view, RCR_FIELDS.FULL_PALLETS);
    }


    public void rcr_next_button_click(View view) {
        if (validate(view)) {
            saveData();
            LoadRCRSmartGrid();
            clearFormFields();
        }
    }

    private void clearFormFields() {
        fullPalletsResult.setText("");
        fullPalletsEntry.setText("");
        splitCasesResult.setText("");
        splitCasesEntry.setText("");
        totalCasesResult.setText("");
        totalBottlesResult.setText("");
        mProductSpinner.setSelection(0);
    }

    private void clearFormFieldsOnNextClick() {
        fullPalletsResult.setText("");
        fullPalletsEntry.setText("");
        splitCasesResult.setText("");
        splitCasesEntry.setText("");
        totalCasesResult.setText("");
        mProductSpinner.setSelection(0);
    }

    private void saveData() {
        if (deliveryItemRow == null) {
            return;
        }

        if (delivery_itemList != null && deliveryItemRow != null) {
            if (!delivery_itemList.contains(deliveryItemRow)) {
                delivery_itemList.add(deliveryItemRow);
            }
        }

        if (totalCasesResult.getText().toString() != null && !totalCasesResult.getText().toString().isEmpty()) {
            float rcr = Float.parseFloat(totalCasesResult.getText().toString());
            if (rcr > 0) {
                deliveryItemRow.setRCR_QTY((double) rcr);
            } else {
                deliveryItemRow.setRCR_QTY((double) 0);
            }
        } else {
            deliveryItemRow.setRCR_QTY((double) 0);
        }
        //DeliveryController.getInstance().getDelvItems();
        DeliveryController.getInstance().insertOrUpdateDeliveryItem(deliveryItemRow);
        //DeliveryController.getInstance().getDelvItems();
        DBHelper.getInstance().createNewTime(String.valueOf(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO()), Constants.TIME_TYPE_CUSTOMER_RCR_UPDATE);
    }

    private boolean validate(View view) {
        if (selectedDelivery == null) {
            SnackBarToast.showMessage(view, "Please select a Delivery");
            return false;
        }
        if (mProductSpinner.getSelectedItemPosition() == 0) {
            SnackBarToast.showMessage(view, "Please select a Product");
            return false;
        }
        return true;
    }


    private void reduceCases(View view, RCR_FIELDS rcrFields) {
        TextView t1 = null;
        EditText t2 = null;
        switch (rcrFields) {
            case FULL_PALLETS:
                t1 = fullPalletsResult;
                t2 = fullPalletsEntry;
                break;
            case SPLIT_CASES:
                t1 = splitCasesResult;
                t2 = splitCasesEntry;
                break;
        }
        if (t2.getText().length() > 0) {
            int x = 0;
            int inputValue = t2.getText().toString().isEmpty() ? 0 : Integer.parseInt(t2.getText().toString());
            x = t1.getText().toString() == null || t1.getText().toString().isEmpty() ? 0 : Integer.parseInt(t1.getText().toString());
            x = (x - inputValue) <= 0 ? 0 : x - inputValue;
            t2.setText("");
            t1.setText(String.valueOf((int) x));
            int totalCases = totalCasesResult.getText().toString() == null || totalCasesResult.getText().toString().isEmpty() ? 0 : Integer.parseInt(totalCasesResult.getText().toString());
            if (rcrFields.equals(RCR_FIELDS.FULL_PALLETS)) {
                totalCases = (totalCases - (inputValue * casesPerPallet)) <= 0 ? 0 : (int) (totalCases - (inputValue * casesPerPallet));
            } else {
                totalCases = (totalCases - inputValue) <= 0 ? 0 : (totalCases - inputValue);
            }
            if (totalCases >= casesPerPallet && casesPerPallet > 0) {
                fullPalletsResult.setText(String.valueOf((int) (totalCases / casesPerPallet)));
                splitCasesResult.setText(String.valueOf((int) (totalCases % casesPerPallet)));
            } else {
                fullPalletsResult.setText("0");
                splitCasesResult.setText(String.valueOf((int) totalCases));
            }
            totalCasesResult.setText(String.valueOf((int) totalCases));

            if(null != totalBottlesResult) {
                totalBottlesResult.setText(String.valueOf((totalCases * Constants.B_CR_BOTTLES_PER_CASE_VALUE)));
            }
        }
    }

    @Override
    public void onTableClick(int position, DELIVERY_ITEM delivery_item) {
        int pos = -1;
        for (int i=0;i<materialHeaderList.size();i++){
            MATERIAL_HEADER material_header = materialHeaderList.get(i);
            if(material_header.getMAT_NO().equals(delivery_item.getMAT_NO())){
                pos=i;
            }
        }
        if(pos!=-1){
            mProductSpinner.setSelection(pos + 1);
        }

    }

    public void rcr_clear_onClick(View view) {

        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Clear All RCR", "Clear All", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                if(deliveryList!=null){
                    DeliveryController.getInstance().clearAllQuantities(deliveryList, DeliveryController.Quantity.RCR);
                    clearFormFields();
                    mDeliverySpinner.setSelection(0);
                    InvoiceController.DeliveryItemPricingDictionary=null;
                    recreate();
                }
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                if(null != dialogMessage)
                    dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) { }

        });
    }


    public void rcr_proceed_click(View view) {
        if(mProductSpinner.getSelectedItemPosition()!=0){
            if(!validate(view)){
                return;
            }
            saveData();
        }

            //Delete items if its HH Created with 0 quantities.
            DeliveryController.getInstance().deleteHHCreatedEmptyItemsIfExist(DeliveryController.Quantity.RCR);
        try {
            InvoiceController.getInstance().deleteInvoiceItemsIfExistForZeroReturns(DeliveryController.getInstance().getOpenDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.RCR));
        } catch (DBException e) {
            Logger.e("", e);
        }

        Logger.i("User in RcrFormActivity. VISIT_NO: "+TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());

        finish();
    }
}