package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.DrawingView;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.BaseFont;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;
import com.unvired.pdf.writer.PDFDocument;
import com.unvired.ui.Home;

import java.io.File;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class PreTripInspectionSecurityActivity extends AppCompatActivity implements IPrintCallBack {
    private RelativeLayout mDrawingView;
    private DrawingView drawingView;
    private TextInputEditText mCardNumber;
    private static String cardNumber;
    private Constants.SCREEN_NAVIGATION_MODE currentNavigationMode;
    private SHIPMENT_HEADER currentShipmentHeader;
    PreTripInspectionSecurityActivity activity;
    private TextView titleText;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        currentNavigationMode = (Constants.SCREEN_NAVIGATION_MODE) getIntent().getSerializableExtra(Constants.SCREEN_NAVIGATION);
        if(currentNavigationMode == null) {
            currentNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;
        }        currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
        setContentView(R.layout.activity_pre_trip_inspection_security);
        iniViews();

        setUpDrawingView();
        activity = this;

    }
    private void setUpDrawingView() {
        drawingView = new DrawingView(this);
        mDrawingView.addView(drawingView);
    }

    private void iniViews() {
        mDrawingView=findViewById(R.id.xPreTripSecuritySignatureDrawingView);
        mCardNumber=findViewById(R.id.xPreTripInspectionSecurityCardNumberET);
        titleText = findViewById(R.id.pretrip_title);
        String currentInspectionType = doInspectionTypeSettings();

    }

    private String doInspectionTypeSettings() {

        String currentInspectionType;

        switch (currentNavigationMode)
        {
            case PostTripInspection:
                titleText.setText(getResources().getString(R.string.title_post_trip_security));
                currentInspectionType = Constants.TRIP_INSP_TYPE_POSTTRIP;
                break;
            case PostForkLiftInspection:
                titleText.setText(getResources().getString(R.string.title_post_trip_security));
                currentInspectionType = Constants.POST_FORK_LIFT_INSPECTION;
                break;
            case PreForkLiftInspection:
                titleText.setText(getResources().getString(R.string.title_pre_trip_security));
                currentInspectionType = Constants.PRE_FORK_LIFT_INSPECTION;
                break;
            default:
                titleText.setText(getResources().getString(R.string.title_pre_trip_security));
                currentInspectionType = Constants.TRIP_INSP_TYPE_PRETRIP;
                break;
        }
        return currentInspectionType;
    }

    public void resetDrawingView(View view) {
        Animation rotateClk = AnimationUtils.loadAnimation(getApplicationContext(),R.anim.clockwise_rotation);
        view.startAnimation(rotateClk);
        drawingView.clear();
    }
    public void navigateToSettings(View view) {
        Utils.showSettingDialog(PreTripInspectionSecurityActivity.this);
    }

    public void closeActivity(View view) {
        finish();
    }


    public void validate(View view) {
        Loader.showLoader(this,"please wait");
        cardNumber=mCardNumber.getText().toString();
        if(cardNumber.isEmpty()){
            Loader.hideLoader();
            SnackBarToast.showMessage(view,"Please enter the card number");
        }else if(drawingView.isPathEmpty()){
            Loader.hideLoader();
            SnackBarToast.showMessage(view,getString(R.string.please_sign));
        }else {
            Loader.hideLoader();
            DialogMessage dialogMessage = new DialogMessage(PreTripInspectionSecurityActivity.this);
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", getResources().getString(R.string.proceed_alert_message), "YES", "NO", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                    Loader.showLoader(PreTripInspectionSecurityActivity.this,"Please wait...");
                    processData();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }

    }

    private void processData() {

        ATTACHMENT attachmentRow;
        int attachment_type;
        Bitmap image = drawingView.getBitmap();
        String hexValue = ImageUtil.getBase64FromBitmap(image);
        if(currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection)){
            attachmentRow=DBHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_PRETRIP_SEC,currentShipmentHeader.getSHIP_NO(),cardNumber,Constants.IMAGE_TYPE,currentShipmentHeader.getSHIP_NO());
        }else {
            attachmentRow=DBHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_POSTTRIP_SEC,currentShipmentHeader.getSHIP_NO(),cardNumber,Constants.IMAGE_TYPE,currentShipmentHeader.getSHIP_NO());
        }

        if(attachmentRow==null){
            try {
                attachmentRow = new ATTACHMENT();
                attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE);
                attachmentRow.setLid(attachmentRow.getLid());
                attachmentRow.setSHIP_NO(currentShipmentHeader.getSHIP_NO());
                attachmentRow.setSIGNED_BY(cardNumber);
                attachmentRow.setDATA(hexValue);
                attachmentRow.setFid(currentShipmentHeader.getLid());
                attachmentRow.setREF_NO(currentShipmentHeader.getSHIP_NO());
                if(currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection)){
                    attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_PRETRIP_SEC);
                }else {
                    attachmentRow.setTYPE((long) Constants.ATTACH_TYPE_POSTTRIP_SEC);

                }
                DBHelper.getInstance().insertOrUpdateAttachment(attachmentRow);
            } catch (DBException e) {
                Loader.hideLoader();
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                Logger.e("", e);
            }
        }else {
            attachmentRow.setDATA(hexValue);
            DBHelper.getInstance().insertOrUpdateAttachment(attachmentRow);
        }

        if(currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection)){
             setCardNumber(cardNumber);
            //Print PreInspection
            try {
                printPreInspectionPrint(Constants.TRIP_INSP_TYPE_PRETRIP);
            } catch (DocumentException | IOException e) {
                Loader.hideLoader();
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                Logger.e("", e);
            }
        }else {
            try {
                 printPreInspectionPrint(Constants.TRIP_INSP_TYPE_POSTTRIP);
            } catch (DocumentException | IOException e) {
                Loader.hideLoader();
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                Logger.e("", e);
                Logger.e("", e);
            }

        }
    }

    private void navigateToEndOfTripMenuForm() {
           startActivity(new Intent(PreTripInspectionSecurityActivity.this,EndOfTripMenuActivity.class));
    }

    private void navigateToOdometerActivity() {
        startActivity(new Intent(PreTripInspectionSecurityActivity.this,OdometerActivity.class));
        finish();
    }

    public static String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }
    private void printPreInspectionPrint(String type) throws IOException, DocumentException {
        Font font = Utils.getFont(10f);
        PDFDocument pdfDocument = new PDFDocument(getFilesDir() + "", PageSize.A6.getWidth(), 10.0f, 10.0f, 25.0f, 5.0f, font, getBaseContext());
        ReportHelper.getInstance().setPDFDocument(pdfDocument);
        InvoiceReport invoiceReport = new InvoiceReport(font, false);
        invoiceReport.printReport(type);
        String pdfPath = pdfDocument.generatePdf();
        if (!Utils.isNullOrEmpty(pdfPath))
            PrinterUtils.print(pdfPath, activity, this,null,null,false,false);
    }

    @Override
    public void onPrintSuccess(Enum<Constants.DocType> type,Constants.CopyType copyType,boolean isLast,String filePath,boolean reprint) {
        //save in storage for testing purpose
        File file = new File(filePath);
        if(!PrinterUtils.printFlag){
            try {
                InvoiceReport.exportFile(file,file.getName(),this);
            } catch (IOException e) {
                Logger.e("", e);
                Logger.e("", e);
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
        navigateToOdometerActivity();
        Loader.hideLoader();
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        if(currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection)){
            currentShipmentHeader.setSTAT((long) Constants.SHIP_STATUS_PRE_FORKLIFT_COMPL);
            DBHelper.getInstance().updateShipmentStatus(currentShipmentHeader);
            DBHelper.getInstance().createNewTime(currentShipmentHeader.getSHIP_NO(),Constants.TRIP_INSP_TYPE_PRETRIP);
            navigateToOdometerActivity();
        }else {
            currentShipmentHeader.setSTAT((long) Constants.SHIP_STATUS_END_TRIP);
            DBHelper.getInstance().updateShipmentStatus(currentShipmentHeader);
            DBHelper.getInstance().createNewTime(currentShipmentHeader.getSHIP_NO(),Constants.TRIP_INSP_TYPE_POSTTRIP);
            navigateToEndOfTripMenuForm();
        }

    }

    @Override
    public void onPrintError(Exception e, Enum<Constants.DocType> type) {
        Loader.hideLoader();
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        {
            Log.e("TAG", "onPrintError: " + e.getMessage());
            Logger.e("", e);
        }
    }
}
