package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.abinbev.oasis.ModelClasses.DeliveryItemPricing;
import com.abinbev.oasis.adapter.PODClaimSummaryAdapter;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CheckInController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class PODClaimSummaryActivity extends AppCompatActivity {
    private RecyclerView mRecyclerView;
    private TextView mDeliveryNo;
    private TextView mSapOrderNo;
    private List<String> keyList;
    private int current_dlv = 0;
    private List<DELIVERY> delivery;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_podclaim_list);
    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        getData();
    }

    private void getData() {
        delivery = DeliveryController.getInstance().getDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.ALL);
        if (!delivery.isEmpty() && delivery != null) {
            PODClaimSummaryAdapter adapter = new PODClaimSummaryAdapter(this, delivery);
            mRecyclerView.setHasFixedSize(true);
            mRecyclerView.setAdapter(adapter);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            if (adapter.areAllItemsDone()) {
                Intent intent = new Intent(PODClaimSummaryActivity.this, EndOfVisitMenuActivity.class);
                startActivity(intent);
                finish();
            }
        } else {
            Log.e("Error", "No adapter is being attached.");
        }

    }

    private void iniViews() {
        mRecyclerView = findViewById(R.id.xScanPodClaimRecyclerview);
    }

    public void closeActivity(View view) {
        finish();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(PODClaimSummaryActivity.this);
    }

}