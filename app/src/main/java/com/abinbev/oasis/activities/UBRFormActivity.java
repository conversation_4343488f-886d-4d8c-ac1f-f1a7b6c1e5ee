package com.abinbev.oasis.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.DeliveryItemDropDownAdapter;
import com.abinbev.oasis.adapter.RcrTableAdapter;
import com.abinbev.oasis.adapter.UbrTableAdapter;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class UBRFormActivity extends AppCompatActivity implements RcrTableAdapter.TableClickListner {
    private Spinner mDeliverySpinner,mProductSpinner;
    private EditText mReturnQty;
    private ImageButton mReturnQtyClear;
    private Button mNext;
    private TextView mNoDataAvailable;
    private RecyclerView mRecyclerView;

    private List<DELIVERY> deliveryList=new ArrayList<>();
    private List<DELIVERY_ITEM> deliveryItemList=new ArrayList<>();
    private DELIVERY_ITEM deliveryItemRow;
    private  DELIVERY deliveryRow;

     @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_u_b_r_form);
        iniViews();
        try {
            getData();
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    private void getData() throws DBException {
        if(deliveryList!=null){
            if(deliveryList.size()>0){
                deliveryList.clear();
            }
        }
        deliveryList = DeliveryController.getInstance().getOpenDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.UBR);
        final DeliveryDropDownAdapter deliveryDropDownAdapter = new DeliveryDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, deliveryList,true);
        mDeliverySpinner.setAdapter(deliveryDropDownAdapter);
        mDeliverySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                deliveryDropDownAdapter.setSelectionPosition(position);
                if (deliveryList != null || deliveryList.size() > 0) {
                    DELIVERY selectedDelivery = deliveryList.get(position-1);
                    deliveryRow = selectedDelivery;
                    if(deliveryItemList!=null){
                        if(deliveryItemList.size()>0){
                            deliveryItemList.clear();
                        }
                    }
                    deliveryItemList = DeliveryController.getInstance().getDeliveryItems(selectedDelivery.getDELV_NO(), DeliveryController.Quantity.UBR);
                    if(deliveryItemList!=null && deliveryItemList.size()>0){
                        final DeliveryItemDropDownAdapter deliveryItemDropDownAdapter = new DeliveryItemDropDownAdapter(UBRFormActivity.this, android.R.layout.simple_spinner_dropdown_item, deliveryItemList,true,false);
                        mProductSpinner.setAdapter(deliveryItemDropDownAdapter);
                        loadUBRSmartGrid();
                        mProductSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                            @Override
                            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                                if(position!=0){
                                    deliveryItemDropDownAdapter.setSelectionPosition(position);
                                    if(deliveryItemRow==null || !deliveryItemRow.getITM_NO().equals(deliveryItemList.get(position-1).getITM_NO())){
                                        deliveryItemRow = deliveryItemList.get(position-1);
                                        mReturnQty.setText(deliveryItemRow.getUBR_QTY()==null || deliveryItemRow.getUBR_QTY().intValue()<=0 || String.valueOf(deliveryItemRow.getUBR_QTY().intValue()).isEmpty() ? "" : String.valueOf(deliveryItemRow.getUBR_QTY().intValue()));
                                    }
                                }
                            }

                            @Override
                            public void onNothingSelected(AdapterView<?> parent) {

                            }
                        });
                    }

                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        if(deliveryList.size()==1){
            mDeliverySpinner.setSelection(1);
        }
    }

    private void loadUBRSmartGrid() {
        if(deliveryItemList!=null && deliveryItemList.size()>0){
            List<DELIVERY_ITEM> smartGridRows = new ArrayList<>();
            for (DELIVERY_ITEM delivery_item : deliveryItemList) {
                if (delivery_item.getUBR_QTY() != null ) {
                    smartGridRows.add(delivery_item);
                }
            }

            if(smartGridRows.size()>0){
                mNoDataAvailable.setVisibility(View.GONE);
                mRecyclerView.setVisibility(View.VISIBLE);
                UbrTableAdapter adapter = new UbrTableAdapter(this, smartGridRows, this);
                mRecyclerView.setHasFixedSize(true);
                mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
                mRecyclerView.setAdapter(adapter);
            }else {
                mRecyclerView.setVisibility(View.GONE);
                mNoDataAvailable.setVisibility(View.VISIBLE);
            }
        }
    }

    private void iniViews() {
        mDeliverySpinner = findViewById(R.id.xUBRBottomSheetDeliverySpinner);
        mProductSpinner = findViewById(R.id.xUBRBottomSheetProductSpinner);
        mReturnQty = findViewById(R.id.xUBRBottomSheetReturnQtyEntry);
        mReturnQtyClear = findViewById(R.id.xUBRBottomSheetReturnQtyClear);
        mNext = findViewById(R.id.xUBRFormNextButton);
        mNoDataAvailable=findViewById(R.id.xUbrNoDataAvailable);
        mRecyclerView=findViewById(R.id.xUbrRecyclerView);
    }

    public void navigateToSettings(View view) { Utils.showSettingDialog(UBRFormActivity.this); }

    @Override
    public void onTableClick(int position, DELIVERY_ITEM delivery_item) {
        int pos = -1;
        for (int i=0;i<deliveryItemList.size();i++){
            DELIVERY_ITEM material_header = deliveryItemList.get(i);
            if(material_header.getMAT_NO().equals(delivery_item.getMAT_NO())){
                pos=i;
            }
        }
        if(pos!=-1){
            mProductSpinner.setSelection(pos + 1);
        }
    }

    public void clearReturnQty(View view) {
        mReturnQty.setText("");
    }

    public void ubr_form_next_click(View view) {
        if(validate(view)){
            saveData();
            mProductSpinner.setSelection(0);
            loadUBRSmartGrid();

        }
    }

    private void saveData() {
        if(deliveryItemList==null || deliveryItemList.size() <= 0 || deliveryItemRow == null){
            return;
        }

        if(!mReturnQty.getText().toString().isEmpty()){
            double ubr = Double.parseDouble(mReturnQty.getText().toString());
            // Set/Reset SLS_DEAL flag to indicate whether the user deliberately made the UBR quantity to 0 or server sent the value 0.
            if(ubr>0){
                deliveryItemRow.setUBR_QTY(ubr);
                deliveryItemRow.setSLS_DEAL("");
            }else {
                deliveryItemRow.setUBR_QTY((double) 0);
                deliveryItemRow.setSLS_DEAL("X");
            }

        }else {
            deliveryItemRow.setUBR_QTY((double) 0);
        }

        DeliveryController.getInstance().insertOrUpdateDeliveryItem(deliveryItemRow);
        mReturnQty.setText("");
    }

    private boolean validate(View view) {
        if (deliveryItemRow == null) {
            SnackBarToast.showMessage(view, "Please select a Delivery");
            return false;
        }
        if (mProductSpinner.getSelectedItemPosition() == 0) {
            SnackBarToast.showMessage(view, "Please select a Product");
            return false;
        }

        double returnQty = deliveryItemRow.getQTY()==null ? 0 : deliveryItemRow.getQTY();
        double ubr = 0;
        if(!mReturnQty.getText().toString().isEmpty()){
            ubr = Double.parseDouble(mReturnQty.getText().toString());
            if(ubr>returnQty){
                SnackBarToast.showMessage(view,"Entered qty is too high. Please enter lower value");
                return false;
            }
        }
        int emptyTruckStock = DeliveryController.getInstance().getEmptyStockInTruck(deliveryItemRow.getDELV_NO());
        int shipmentLimit = DBHelper.getInstance().getCurrentShipment().getRCR_LIMIT().intValue();

        List<DELIVERY_ITEM> deliveryItemListTemp = DeliveryController.getInstance().getDeliveryItems(deliveryItemRow.getDELV_NO(), DeliveryController.Quantity.ALL);
        if(deliveryItemListTemp!=null && deliveryItemListTemp.size()>0){
            for (DELIVERY_ITEM delivery_item : deliveryItemListTemp){
                if(delivery_item!=null){
                    int fbrQty = delivery_item.getFBR_QTY()==null ? 0 : delivery_item.getFBR_QTY().intValue();
                    int bitQty = delivery_item.getBIT_QTY()==null ? 0 : delivery_item.getBIT_QTY().intValue();
                    int rcrQty = delivery_item.getRCR_QTY()==null ? 0 : delivery_item.getRCR_QTY().intValue();
                    int ubrQty = delivery_item.getUBR_QTY()==null ? 0 : delivery_item.getUBR_QTY().intValue();
                    emptyTruckStock = emptyTruckStock + (fbrQty+bitQty+rcrQty+ubrQty);
                }
            }
        }

        emptyTruckStock = (int) (emptyTruckStock - (deliveryItemRow.getUBR_QTY() == null ? 0 : deliveryItemRow.getUBR_QTY().intValue()));
        if((ubr+emptyTruckStock) > shipmentLimit){
            SnackBarToast.showMessage(view,"Please reduce quantity – total truck capacity exceeded");
            return false;
        }
        return true;
    }

    public void ubr_proceed_click(View view) {
        if(mProductSpinner.getSelectedItemPosition()!=0){
            if(!validate(view)){
                return;
            }else {
                saveData();
                Logger.i("User in UBRFormActivity. VISIT_NO: "+TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());
            }

        }
        finish();
    }

    @Override
    public void onBackPressed() {

    }
}