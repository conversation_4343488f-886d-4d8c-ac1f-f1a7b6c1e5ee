package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CheckInController;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.Utils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;
import com.unvired.pdf.writer.PDFDocument;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EndOfVisitMenuActivity extends AppCompatActivity implements IPrintCallBack {
    private RelativeLayout mRePrint, mEndVisit, mScanPodClaim;
    //    private  RelativeLayout mCustomerStamp;
//    private  RelativeLayout mCustomerClaim;
    private List<STOCK> stockList;
    private String shipmentLid = "";
    EndOfVisitMenuActivity activity;
    List<DELIVERY> deliveryList = null;
    int position = 0;
    InvoiceReport invoiceReport;
    VISIT visitRow;

    boolean checkNATAndPOD = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_end_of_visit_menu);
        activity = this;
    }

    @Override
    public void onBackPressed() {
        //super.onBackPressed();
    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        try {
            getData();
        } catch (DBException e) {
            throw new RuntimeException(e);
        }
    }

    private void iniViews() {
        mRePrint = findViewById(R.id.xEndOfVisitMenuReprint);
        mEndVisit = findViewById(R.id.xEndOfVisitMenuEndVisit);
        mScanPodClaim = findViewById(R.id.xEndOfVisitMenuPodClaim);


    }

    private void getData() throws DBException {
        if (Constants.NAT_CUSTOMER.equals(TripSummaryController.getInstance().getNAT_CUSTOMER_TYPE()) && DeliveryController.getInstance().isPODClaimScanEnabled()) {
            if (DeliveryController.getInstance().enableEndVisitButton() ) {
                Utils.disableButton(mEndVisit, this);
            } else {
                Utils.enableButton(mEndVisit, this);
            }
        } else {
            Utils.disableButton(mScanPodClaim, this);
            Utils.enableButton(mEndVisit, this);
        }

        Utils.disableButton(mRePrint, this);
        String driver = "";
        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
        if (shipment_header != null) {
            driver = shipment_header.getDRV1() == null ? "" : shipment_header.getDRV1();
        }
        if (!Utils.isNullOrEmpty(driver)) {
            Utils.enableButton(mRePrint, this);
        }
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        if (visit.getSTAT() != Constants.VISIT_STATUS_END_REACHED) {
            reconcileDeliveryAndUpdateStock();
            deletePDFiles();
            visit.setSTAT((long) Constants.VISIT_STATUS_END_REACHED);
            TripSummaryController.getInstance().insertOrUpdateVisit(visit);
        }

    }

    private void deletePDFiles() {
        // TODO: 05-02-2021 method is blank in windows as well
    }

    private void reconcileDeliveryAndUpdateStock() {
        List<DELIVERY> deliveryList = DeliveryController.getInstance().getDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.ALL);
        if (deliveryList != null && deliveryList.size() > 0) {
            stockList = CheckInController.getInstance().getStockMaterials("");
            if (stockList == null) {
                stockList = new ArrayList<>();
            }
            if (Utils.isNullOrEmpty(shipmentLid)) {
                shipmentLid = String.valueOf(DBHelper.getInstance().getShipmentRow().getLid());
            }

            for (DELIVERY delivery : deliveryList) {
                if (delivery.getIS_FBR() != null && !Utils.isNullOrEmpty(delivery.getIS_FBR())) {
                    continue;
                }
                List<DELIVERY_ITEM> delivery_itemList = DeliveryController.getInstance().getDeliveryItems(delivery.getDELV_NO(), DeliveryController.Quantity.ALL);
                if (delivery_itemList != null && delivery_itemList.size() > 0) {
                    for (DELIVERY_ITEM deliveryItem : delivery_itemList) {
                        if (deliveryItem.getMAT_NO() == null || Utils.isNullOrEmpty(deliveryItem.getMAT_NO())) {
                            continue;
                        }
                        createOrUpdateStockRow(deliveryItem);
                    }
                } else {
                    //DeleteHHCreatedDeliveriesIfNoItemsExist
                    if (delivery.getHH_CREATED() != null && !Utils.isNullOrEmpty(delivery.getHH_CREATED())) {
                        DeliveryController.getInstance().deleteDelivery(delivery);
                    }
                }

            }
            for (DELIVERY delivery : deliveryList) {
                DeliveryController.getInstance().updateDelivery(delivery);
            }

            for (STOCK stock : stockList) {
                CheckInController.getInstance().updateStock(stock);
            }

        }
    }

    private void createOrUpdateStockRow(DELIVERY_ITEM deliveryItem) {
        double qty = 0;
        if (deliveryItem.getQTY() != null && deliveryItem.getQTY() > 0 && (deliveryItem.getPROMO_TYPE() == null || !deliveryItem.getPROMO_TYPE_DESC().equals("OSB"))) {
            qty = deliveryItem.getQTY();
        }

        if (deliveryItem.getIS_EMPTY() != null && !Utils.isNullOrEmpty(deliveryItem.getIS_EMPTY())) {
            STOCK stockRowEmpty = getStockRow(deliveryItem, Constants.RCR_TYPE);
            if (deliveryItem.getIS_RETURN() != null && !Utils.isNullOrEmpty(deliveryItem.getIS_RETURN())) {
                if (deliveryItem.getRCR_QTY() != null && deliveryItem.getRCR_QTY() > 0) {
                    //rcr condition
                    stockRowEmpty.setACT_QTY(stockRowEmpty.getACT_QTY() == null ? deliveryItem.getRCR_QTY() : (stockRowEmpty.getACT_QTY() + deliveryItem.getRCR_QTY()));
                } else {
                    //pallet pick
                    stockRowEmpty.setACT_QTY(stockRowEmpty.getACT_QTY() == null ? qty : (stockRowEmpty.getACT_QTY() + qty));
                }
            } else {
                //pallet drop Or Empty material which is planned for delivery
                int fbr = deliveryItem.getFBR_QTY() == null ? 0 : (int) deliveryItem.getFBR_QTY().intValue();
                int bit = deliveryItem.getBIT_QTY() == null ? 0 : deliveryItem.getBIT_QTY().intValue();
                stockRowEmpty.setACT_QTY(stockRowEmpty.getACT_QTY() == null ? 0 : (stockRowEmpty.getACT_QTY() - qty + fbr + bit));
            }
            if (!stockList.contains(stockRowEmpty)) {
                stockList.add(stockRowEmpty);
            }
        } else {
            double fbr = 0;
            if (deliveryItem.getIS_RETURN() != null && !Utils.isNullOrEmpty(deliveryItem.getIS_RETURN())) {
                STOCK stockRowFbr = getStockRow(deliveryItem, Constants.FBR_TYPE);
                //ubr
                double ubrQty = deliveryItem.getUBR_QTY() == null ? 0 : deliveryItem.getUBR_QTY();
                stockRowFbr.setACT_QTY(stockRowFbr.getACT_QTY() == null ? ubrQty : (stockRowFbr.getACT_QTY() + ubrQty));
                if (!stockList.contains(stockRowFbr)) {
                    stockList.add(stockRowFbr);
                }
            } else {
                STOCK stockRowFbr = getStockRow(deliveryItem, Constants.FBR_TYPE);
                double bit = 0;
                if (deliveryItem.getBIT_QTY() != null && deliveryItem.getBIT_QTY() > 0) {
                    //bit
                    STOCK stockRowBit = getStockRow(deliveryItem, Constants.BIT_TYPE);
                    stockRowBit.setACT_QTY(stockRowBit.getACT_QTY() == null ? deliveryItem.getBIT_QTY() : (stockRowBit.getACT_QTY() + deliveryItem.getBIT_QTY()));
                    bit = deliveryItem.getBIT_QTY();
                    if (!stockList.contains(stockRowBit)) {
                        stockList.add(stockRowBit);
                    }
                }
                if (deliveryItem.getFBR_QTY() != null && deliveryItem.getFBR_QTY() > 0) {
                    fbr = deliveryItem.getFBR_QTY();
                }
                stockRowFbr.setACT_QTY(stockRowFbr.getACT_QTY() == null ? 0 : (stockRowFbr.getACT_QTY() - (qty - (fbr))));
                if (!stockList.contains(stockRowFbr)) {
                    stockList.add(stockRowFbr);
                }
            }
        }
    }

    private STOCK getStockRow(DELIVERY_ITEM deliveryItem, String type) {
        STOCK stockRow = null;
        List<STOCK> stocks = new ArrayList<>();
        for (STOCK stock1 : stockList) {
            if (stock1.getMAT_NO().equals(deliveryItem.getMAT_NO()) && stock1.getTYPE().equals(type)) {
                stocks.add(stock1);
            }
        }

        if (stocks.size() <= 0 || stocks.get(0) == null) {
            try {
                stockRow = new STOCK();
                stockRow.setMAT_NO(deliveryItem.getMAT_NO());
                stockRow.setLid(stockRow.getLid());
                stockRow.setMAT_DESC(deliveryItem.getMAT_DESC());
                stockRow.setFid(shipmentLid);
                stockRow.setTYPE(type);
                stockRow.setUOM(deliveryItem.getSLS_UOM());
                stockRow.setSHIP_NO(DBHelper.getInstance().getShipmentRow().getSHIP_NO());
            } catch (DBException e) {
                Logger.e("", e);
            }
        } else {
            stockRow = stocks.get(0);
        }

        return stockRow;
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(EndOfVisitMenuActivity.this);
    }

    public void reprint_invoice_click(View view) {
        try {
            //Process any un-touched UBR - Issue#701
            position = 0;
            preparePdf(Constants.DocType.Invoice);
        } catch (Exception e) {
            Logger.e("", e);
        }
    }

    private void preparePdf(Constants.DocType type) throws Exception {
        Font font = Utils.getFont(10f);
        /*PDFDocument pdfDocument = new PDFDocument(getFilesDir() + "", PageSize.A6.getWidth(), 6.0f, 6.0f, 25.0f, 5.0f, font, getBaseContext());
        ReportHelper.getInstance().setPDFDocument(pdfDocument);*/
        invoiceReport = new InvoiceReport(font, false);
        visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
        invoiceReport.generateInvoice();
        deliveryList = DeliveryController.getInstance().getDeliveryHeaders(visitRow.getVISIT_NO(), DeliveryController.Quantity.ALL);
        if (deliveryList != null && deliveryList.get(position) != null) {
            invoiceReport.generateInvoicePdf(deliveryList.get(position), visitRow, type, true, this, this, null);

        }
    }

    public void print_vehicle_stock_click(View view) throws IOException, DocumentException {
        Loader.showLoader(this, "Please wait while printing");
        Font font = Utils.getFont(10f);
        PDFDocument pdfDocument = new PDFDocument(getFilesDir() + "",
                PageSize.A6.getWidth(), 10.0f, 10.0f, 15, 5.0f, font, getBaseContext());
        ReportHelper.getInstance().setPDFDocument(pdfDocument);
        InvoiceReport invoiceReport = new InvoiceReport(font, false);
        invoiceReport.stockReportPrint(Constants.TRIP_INSP_TYPE_POSTTRIP, true);
        String pdfPath = pdfDocument.generatePdf();
        Loader.hideLoader();

        // if(PrinterUtils.printFlag){
        if (!Utils.isNullOrEmpty(pdfPath))
            PrinterUtils.print(pdfPath, activity, this, null, null, false, false);
        //  }
    }

    public void navigate_to_PODClaim(View view) {
        startActivity(new Intent(EndOfVisitMenuActivity.this, PODClaimSummaryActivity.class));
    }

    public void end_visit_click(View view) {
        //  if(Constants.NAT_CUSTOMER.equals(TripSummaryController.getInstance().NAT_CUSTOMER_TYPE) && !mCustomerStamp.isEnabled()){
        DialogMessage dialogMessage = new DialogMessage(this);

        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Are you sure you want to end the Visit ?", "End Visit", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                TripSummaryController.getInstance().updateSeqNo();
                VISIT visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
                visitRow.setSTAT((long) Constants.VISIT_STATUS_END);
                TripSummaryController.getInstance().insertOrUpdateVisit(visitRow);
                DBHelper.getInstance().createNewTime(visitRow.getVISIT_NO().toString(), Constants.TIME_TYPE_VISIT_END);
                clearVisitCache();
                if (DBHelper.getInstance().isUnplannedCustomer()) {
                    DBHelper.getInstance().setUnplannedCustomer(false);
                }
                Intent intent = new Intent(EndOfVisitMenuActivity.this, TripSummaryActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                finish();
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();

            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
//        }else {
//            SnackBarToast.showMessage(view,"Please scan customer stamp");
//        }


    }

    private void clearVisitCache() {
        InvoiceController.setDeliveryItemPricingDictionary(null);
        OasisCache.setCustomerRating(null);
        CustomizingController.getInstance().addOrUpdateCustomizingRow(Constants.KEY_CUSTOMER_RATING, OasisCache.getCustomerRating());

        List<String> delvList = DeliveryController.getInstance().getDeliveryNos(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.ALL);
        String REF_NO_INStr = "";
        for (String delvNo : delvList) {
            if (REF_NO_INStr.isEmpty()) {
                REF_NO_INStr = "'" + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + delvNo + "'";
            } else {
                REF_NO_INStr = REF_NO_INStr + ", '" + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + delvNo + "'";
            }
        }
        //Clear the sign images as we dont need it to be sent to SAP
        if (!Utils.isNullOrEmpty(REF_NO_INStr)) {
            InvoiceController.getInstance().deleteFromAttachment(REF_NO_INStr);
        }
    }

    @Override
    public void onPrintSuccess(Enum<Constants.DocType> type, Constants.CopyType copyType, boolean isLast, String filePath, boolean reprint) {
        //save in storage for testing purpose
        File file = new File(filePath);
        if (!PrinterUtils.printFlag) {
            try {
                InvoiceReport.exportFile(file, file.getName(), this);
            } catch (IOException e) {
                Logger.e("", e);
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
        if (deliveryList != null && position < (deliveryList.size() - 1) && type == Constants.DocType.Invoice) {
            position++;
            showMessageDialog(type, getString(R.string.print_next_invoice_list), reprint);

        }
    }

    private void showMessageDialog(final Enum<Constants.DocType> type, String string, boolean reprint) {
        DialogMessage dialogMessage = new DialogMessage(EndOfVisitMenuActivity.this);
        dialogMessage.showDialogWithPositive("", string, "OK", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                try {
                    invoiceReport.generateInvoice();
                    invoiceReport.generateInvoicePdf(deliveryList.get(position), visitRow, type, reprint, EndOfVisitMenuActivity.this, EndOfVisitMenuActivity.this, null);
                } catch (DocumentException | InterruptedException | IOException | DBException |
                         ParseException e) {
                    Logger.e("", e);
                } catch (Exception e) {
                    Logger.e("", e);
                }

            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {

            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }

    @Override
    public void onPrintError(Exception e, Enum<Constants.DocType> type) {

    }

    public void navigate_to_scan_customer_pod(View view) {
        Intent intent = new Intent(EndOfVisitMenuActivity.this, ScanCustomerClaimPod.class);
        intent.putExtra("type", "pod");
        startActivity(intent);
    }

    public void navigate_to_scan_customer_claim(View view) {
        Intent intent = new Intent(EndOfVisitMenuActivity.this, ScanCustomerClaimPod.class);
        intent.putExtra("type", "claim");
        startActivity(intent);
    }
}