package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.DeliveryItemDropDownAdapter;
import com.abinbev.oasis.adapter.FbrBitTableAdapter;
import com.abinbev.oasis.adapter.RcrTableAdapter;
import com.abinbev.oasis.adapter.ReasonDropDownAdapter;
import com.abinbev.oasis.adapter.UserNameAdapter;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.ReasonController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class FBR_BITFormActivity extends AppCompatActivity implements RcrTableAdapter.TableClickListner {

    //Views
    private Spinner mDeliverySpinner;
    private Spinner mProductSpinner;
    private Spinner mReasonSpinner;
    private TextInputEditText mFbrQty;
    private TextInputEditText mBitQty;
    private Button mNext;
    private TextView mClear;
    private TextView mNoData;
    private RecyclerView mRecyclerView;


    private List<DELIVERY> deliveryList = null;
    private List<DELIVERY_ITEM> delivery_itemList = null;
    private List<REASON_HEADER> reason_headerList = null;
    private DELIVERY_ITEM deliveryItemRow = null;
    private DELIVERY deliveryRow = null;
    private REASON_HEADER selectedReason = null;
    private int selectedReasonPosition;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_f_b_r__b_i_t_form);
        iniViews();
        try {
            getData();
        } catch (DBException e) {
            Logger.e("", e);
        }
    }

    @Override
    public void onBackPressed() {

    }

    private void getData() throws DBException {
        deliveryList = DeliveryController.getInstance().getOpenDeliveryHeaders(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO(), DeliveryController.Quantity.FBRBIT);
        CUSTOMER_HEADER customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());

        // Check if customer has SPLITBILLENABLED attribute
        boolean splitBillEnabled = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.SPLITBILLENABLED);

        // Check if customer has OSB2INVENABLED attribute
        boolean osb2InvEnabled = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.OSB2INVENABLED);

        String emptyOrdDelvNo = DeliveryController.getInstance().getFirstReturnsCollectionOrderDeliveryNo();

        if (splitBillEnabled) {
            // For SPLITBILLENABLED, keep only the Returns Collection Delivery Number
            if (emptyOrdDelvNo != null && !emptyOrdDelvNo.isEmpty()) {
                for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                    if (!deliveryList.get(i).getDELV_NO().equals(emptyOrdDelvNo)) {
                        deliveryList.remove(i);
                    }
                }
            }
        } else if (osb2InvEnabled) {
            // For OSB2INVENABLED, exclude the Returns Collection Delivery Number
            if (emptyOrdDelvNo != null && !emptyOrdDelvNo.isEmpty()) {
                for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                    if (deliveryList.get(i).getDELV_NO().equals(emptyOrdDelvNo)) {
                        deliveryList.remove(i);
                    }
                }
            }
        }
        reason_headerList = ReasonController.getInstance().getReasons(Constants.REASON_TYPE_DELIVERY);
        setUpSpinner();
    }

    private void setUpSpinner() {
        final DeliveryDropDownAdapter deliveryDropDownAdapter = new DeliveryDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, deliveryList,true);
        mDeliverySpinner.setAdapter(deliveryDropDownAdapter);
        mDeliverySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                deliveryDropDownAdapter.setSelectionPosition(position);
                if(position!=0){
                DELIVERY selectedDelivery = null;
                if (deliveryList == null || deliveryList.size() == 0) {
                    selectedDelivery = null;
                } else {
                    selectedDelivery = deliveryList.get(position - 1);

                }
                if (deliveryRow == null || !deliveryRow.getDELV_NO().equals(selectedDelivery.getDELV_NO())) {
                    deliveryRow = selectedDelivery;
                    if(delivery_itemList!=null && !delivery_itemList.isEmpty()){
                        delivery_itemList.clear();
                    }
                    delivery_itemList = DeliveryController.getInstance().getDeliveryItems(selectedDelivery.getDELV_NO(), DeliveryController.Quantity.FBRBIT);
                    if (delivery_itemList != null && delivery_itemList.size() > 0) {
                        final DeliveryItemDropDownAdapter deliveryItemDropDownAdapter = new DeliveryItemDropDownAdapter(FBR_BITFormActivity.this, android.R.layout.simple_spinner_dropdown_item, delivery_itemList, true, false);
                        mProductSpinner.setAdapter(deliveryItemDropDownAdapter);
                        loadFBRBITSmartGrid();
                        mProductSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                            @Override
                            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

                                if (position != 0) {
                                    deliveryItemDropDownAdapter.setSelectionPosition(position);
                                    DELIVERY_ITEM selectedDeliveryItem = delivery_itemList.get(position - 1);
                                    if (deliveryItemRow == null || !selectedDeliveryItem.getITM_NO().equals(deliveryItemRow.getITM_NO())) {
                                        deliveryItemRow = selectedDeliveryItem;
                                        populateDeliveryItemRow();
                                    }
                                }else {
                                    clearFields();
                                }
                            }

                            @Override
                            public void onNothingSelected(AdapterView<?> parent) {
                            }
                        });
                    }
                }

            }else {
                    clearFormFields();
                    mRecyclerView.setVisibility(View.GONE);
                    mNoData.setVisibility(View.VISIBLE);
                    deliveryRow=null;
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        if(deliveryList.size()==1){
            mDeliverySpinner.setSelection(1);
        }

        ReasonDropDownAdapter reasonDropDownAdapter = new ReasonDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item,reason_headerList,true);
        mReasonSpinner.setAdapter(reasonDropDownAdapter);
        mReasonSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if(position!=0){
                    reasonDropDownAdapter.setSelectionPosition(position);
                    selectedReason = reason_headerList.get(position-1);
                    selectedReasonPosition = position;
                }else {
                    selectedReason = null;
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) { }
        });

    }

    private void populateDeliveryItemRow() {
        if(deliveryItemRow!=null){
            mBitQty.setText(deliveryItemRow.getBIT_QTY()==null || deliveryItemRow.getBIT_QTY().toString().isEmpty() || deliveryItemRow.getBIT_QTY()<=0 ? "" : String.valueOf(deliveryItemRow.getBIT_QTY().intValue()));
            mFbrQty.setText(deliveryItemRow.getFBR_QTY()==null || deliveryItemRow.getFBR_QTY().toString().isEmpty() || deliveryItemRow.getFBR_QTY()<=0 ? "" : String.valueOf(deliveryItemRow.getFBR_QTY().intValue()));
            String selectedFbrReason = deliveryItemRow.getFBR_RSN();
            int position =0;
            if(selectedFbrReason==null || selectedFbrReason.isEmpty()){
                mReasonSpinner.setSelection(position);
                return;
            }
            List<REASON_HEADER> reason_headerListTemp = new ArrayList<>();

            for (int i=0;i<reason_headerList.size();i++){
                REASON_HEADER reason_header = reason_headerList.get(i);
                    if(reason_header.getRSN_CODE().equals(selectedFbrReason)){
                        position = i+1;
                    }
            }
            mReasonSpinner.setSelection(position);
//            if(reason_headerListTemp.size()>0){
//                mReasonSpinner.setSelection(reason_headerList.indexOf(reason_headerListTemp.get(0))-1);
//            }
        }
    }

    private void loadFBRBITSmartGrid() {
        List<DELIVERY_ITEM> delivery_itemListTemp = DeliveryController.getInstance().getDeliveryItems(deliveryRow.getDELV_NO(), DeliveryController.Quantity.FBRBIT);

        if(delivery_itemListTemp!=null && delivery_itemListTemp.size()>0){
            List<DELIVERY_ITEM> smartGridRows = new ArrayList<>();
            for (DELIVERY_ITEM delivery_item : delivery_itemListTemp) {
                if ((delivery_item.getFBR_QTY() != null && delivery_item.getFBR_QTY()>0) || (delivery_item.getBIT_QTY()!=null && delivery_item.getBIT_QTY()>0) ) {
                    smartGridRows.add(delivery_item);
                }
            }
            if(smartGridRows.size()>0){
                mNoData.setVisibility(View.GONE);
                mRecyclerView.setVisibility(View.VISIBLE);
                FbrBitTableAdapter adapter = new FbrBitTableAdapter(this, smartGridRows, this);
                mRecyclerView.setHasFixedSize(true);
                mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
                mRecyclerView.setAdapter(adapter);
            }else {
                mRecyclerView.setVisibility(View.GONE);
                mNoData.setVisibility(View.VISIBLE);
            }
        }
    }

    private void iniViews() {
        mDeliverySpinner=findViewById(R.id.xFbrBitDeliverySpinner);
        mProductSpinner=findViewById(R.id.xFbrBitProductSpinner);
        mReasonSpinner = findViewById(R.id.xFbrBitReasonSpinner);
        mFbrQty=findViewById(R.id.xFbrBitFBRQty);
        mBitQty=findViewById(R.id.xFbrBitBITQty);
        mNext=findViewById(R.id.xFbrBitBottomSheetNextButton);
        mClear=findViewById(R.id.xFbrBitBottomSheetClearAll);
        mNoData=findViewById(R.id.xFbrBitNoDataAvailable);
        mRecyclerView=findViewById(R.id.xFbrBitRecyclerView);
    }

    public void navigateToSettings(View view) { Utils.showSettingDialog(FBR_BITFormActivity.this); }

    public void fbr_bit_next_click(View view) {
        if(validate(view))
        {
            saveData();
            loadFBRBITSmartGrid();
           // populateDeliveryItemRow();
            clearFormFieldsOnNextClick();
        }
    }

    private void saveData() {
            if(delivery_itemList==null || delivery_itemList.size()<=0 || deliveryItemRow == null){ return; }
        if(!mFbrQty.getText().toString().isEmpty()){
            double fbr = Double.parseDouble(mFbrQty.getText().toString());
            if(fbr>0){
                deliveryItemRow.setFBR_QTY(fbr);
                deliveryItemRow.setFBR_RSN(selectedReason.getRSN_CODE());
                deliveryItemRow.setFBR_RSN_POS((long) (selectedReason == null ? 0 : selectedReasonPosition));
            }else {
                deliveryItemRow.setFBR_QTY((double) 0);
                deliveryItemRow.setFBR_RSN("");
            }
        }else {
            deliveryItemRow.setFBR_QTY((double) 0);
            deliveryItemRow.setFBR_RSN("");
        }
        if(!mBitQty.getText().toString().isEmpty()) {
            double bit = Double.parseDouble(mBitQty.getText().toString());
            if(bit>0){
                deliveryItemRow.setBIT_QTY(bit);
                deliveryItemRow.setBIT_RSN("BIT");
            }else {
                deliveryItemRow.setBIT_QTY((double) 0);
                deliveryItemRow.setBIT_RSN("");
            }
        }else {
            deliveryItemRow.setBIT_QTY((double) 0);
            deliveryItemRow.setBIT_RSN("");
        }
        DeliveryController.getInstance().insertOrUpdateDeliveryItem(deliveryItemRow);
        DBHelper.getInstance().createNewTime(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString(),Constants.TIME_TYPE_CUSTOMER_FBR_BIT_UPDATE);
    }

    private boolean validate(View view) {
        if(mDeliverySpinner.getSelectedItem()==null){
            SnackBarToast.showMessage(view,"Please select a Delivery");
            return false;
        }
        if(mProductSpinner.getSelectedItem()==null){
            SnackBarToast.showMessage(view,"Please select a Product");
            return false;
        }

        if(deliveryItemRow==null){
            return  false;
        }
        double orderedQty = deliveryItemRow.getQTY()==null ? 0 : deliveryItemRow.getQTY();
        double fbr = 0;
        double bit = 0;
        if(mFbrQty.getText().toString().isEmpty() && mBitQty.getText().toString().isEmpty()){
            SnackBarToast.showMessage(view,"Please enter a valid FBR or BIT Quantity");
            return false;
        }else {
            if(!mFbrQty.getText().toString().isEmpty()){
                try {
                    fbr = Double.parseDouble(mFbrQty.getText().toString());
                }catch (Exception e){
                    Logger.e("", e);
                    SnackBarToast.showMessage(view,"Please enter a valid FBR Quantity");
                    return false;
                }
                if(fbr>orderedQty){
                    SnackBarToast.showMessage(view,"FBR Qty is too high");
                    return false;
                }
                if(fbr>0 && selectedReason==null){
                    SnackBarToast.showMessage(view,"Select an FBR Reason");
                    return false;
                }
            }

            if(!mBitQty.getText().toString().isEmpty()){

                try {
                    bit = Double.parseDouble(mBitQty.getText().toString());
                }catch (Exception e){
                    Logger.e("", e);
                    SnackBarToast.showMessage(view,"Please enter a valid BIT Quantity");
                    return false;
                }
                if (bit > orderedQty || bit > (orderedQty - fbr))
                {
                    SnackBarToast.showMessage(view,"BIT Quantity is too high");
                    return false;
                }
            }
        }

        int emptyTruckStock = DeliveryController.getInstance().getEmptyStockInTruck(deliveryItemRow.getDELV_NO());
        int shipmentLimit = DBHelper.getInstance().getCurrentShipment().getRCR_LIMIT().intValue();
        List<DELIVERY_ITEM> deliveryItemDataTemp = DeliveryController.getInstance().getDeliveryItems(deliveryItemRow.getDELV_NO(), DeliveryController.Quantity.ALL);
        if(deliveryItemDataTemp!=null && deliveryItemDataTemp.size()>0){
            for (DELIVERY_ITEM delivery_item : deliveryItemDataTemp){
                if(delivery_item!=null){
                    int fbrQty = delivery_item.getFBR_QTY()==null ? 0 : delivery_item.getFBR_QTY().intValue();
                    int bitQty = delivery_item.getBIT_QTY()==null ? 0 : delivery_item.getBIT_QTY().intValue();
                    int rcrQty = delivery_item.getRCR_QTY()==null ? 0 : delivery_item.getRCR_QTY().intValue();
                    int ubrQty = delivery_item.getUBR_QTY()==null ? 0 : delivery_item.getUBR_QTY().intValue();
                    emptyTruckStock = emptyTruckStock + (fbrQty+bitQty+rcrQty+ubrQty);
                }
            }
        }
        int fbrQtyTemp = deliveryItemRow.getFBR_QTY()==null ? 0 : deliveryItemRow.getFBR_QTY().intValue();
        int bitQtyTemp = deliveryItemRow.getBIT_QTY()==null ? 0 : deliveryItemRow.getBIT_QTY().intValue();
        emptyTruckStock = emptyTruckStock - (fbrQtyTemp + bitQtyTemp);
        if (fbr + bit + emptyTruckStock > shipmentLimit)
        {
            SnackBarToast.showMessage(view,"Please reduce quantity – total truck capacity exceeded");
            return false;
        }
        return true;
    }

    public void fbr_bit_complete_click(View view) {
        if(mProductSpinner.getSelectedItem()!=null){
            if(!validate(view)){
                saveData();
            }
        }
        Logger.i("User in FBR_BITFormActivity. VISIT_NO: "+TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());
        finish();
    }

    @Override
    public void onTableClick(int position, DELIVERY_ITEM delivery_item) {
        int pos = -1;
        int delvPos = -1;
        for (int i=0;i<delivery_itemList.size();i++){
            DELIVERY_ITEM material_header = delivery_itemList.get(i);
            if(material_header.getMAT_NO().equals(delivery_item.getMAT_NO())){
                pos=i;
                break;
            }
        }
        if(pos!=-1){
            mProductSpinner.setSelection(pos + 1);
        }

        for (int j=0;j<deliveryList.size();j++){
            DELIVERY delivery = deliveryList.get(j);
            if(delivery.getDELV_NO().equals(delivery_item.getDELV_NO())){
                delvPos=j;
                break;
            }
        }
        if(delvPos!=-1){
            mDeliverySpinner.setSelection(delvPos+1);
        }
    }

    public void fbr_bit_clear_click(View view) {
        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Clear All RCR", "Clear All", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                if(delivery_itemList!=null){
                    DeliveryController.getInstance().clearAllQuantities(deliveryList, DeliveryController.Quantity.FBRBIT);
                    clearFormFields();
                    recreate();
                }
            }
            @Override
            public void onNegativeClick(Dialog dialogMessage) { }

            @Override
            public void onNeutralClick(Dialog dialogMessage) { }

        });
    }

    private void clearFormFields() {
        mFbrQty.setText("");
        mBitQty.setText("");
        mProductSpinner.setSelection(0);
        mReasonSpinner.setSelection(0);
        mDeliverySpinner.setSelection(0);
        deliveryItemRow=null;
    }

    private void clearFormFieldsOnNextClick() {
        mFbrQty.setText("");
        mBitQty.setText("");
        mProductSpinner.setSelection(0);
        mReasonSpinner.setSelection(0);
        deliveryItemRow=null;
    }

    private void clearFields() {
        mFbrQty.setText("");
        mBitQty.setText("");

    }

}
