package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.util.AttachmentHelper;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class ScanCustomerPodClaimActivity extends AppCompatActivity {
    private RelativeLayout mCustomerPod;
    private RelativeLayout mCustomerClaim;

    String deliveryNo;
    private List<DELIVERY> delivery;
    private int totalDeliverySize;


    @Override
    public void onBackPressed() {
        //super.onBackPressed();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan_customer_pod_claim);
        mCustomerPod = findViewById(R.id.xEndOfVisitMenuCustomerPod);
        mCustomerClaim = findViewById(R.id.xEndOfVisitMenuCustomerClaim);


    }

    @Override
    protected void onResume() {
        super.onResume();
        ATTACHMENT attachment = AttachmentHelper.getInstance().getCustomerPod();
        if (attachment != null) {
            Utils.disableButton(mCustomerPod, ScanCustomerPodClaimActivity.this);
        }

        ATTACHMENT attachmentClaim = AttachmentHelper.getInstance().getCustomerClaim();
        if (attachmentClaim != null) {
            Utils.disableButton(mCustomerClaim, ScanCustomerPodClaimActivity.this);
        }

    }


    public void navigateToSettings(View view) {
        Utils.showSettingDialog(ScanCustomerPodClaimActivity.this);
    }

    public void navigate_to_scan_customer_pod(View view) {
        Intent intent = new Intent(ScanCustomerPodClaimActivity.this, ScanCustomerClaimPod.class);
        intent.putExtra("type", "pod");
        startActivity(intent);
    }

    public void navigate_to_scan_customer_claim(View view) {
        Intent intent = new Intent(ScanCustomerPodClaimActivity.this, ScanCustomerClaimPod.class);
        intent.putExtra("type", "claim");
        startActivity(intent);
    }

    public void navigateToEndOfVisitMenu(View view) {
        ArrayList<String> completedList = new ArrayList<>();
        if (Constants.NAT_CUSTOMER.equals(TripSummaryController.getInstance().NAT_CUSTOMER_TYPE) && !mCustomerPod.isEnabled()) {
            if (!mCustomerPod.isEnabled()) {
                DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getDeliveryNoFromAdapter());
                delivery.setBLANK_TYPE("X");
                DeliveryController.getInstance().updateDelivery(delivery);
                completedList.add(delivery.getDELV_NO());
                if (completedList.size() == totalDeliverySize) {
                    Intent intent = new Intent(ScanCustomerPodClaimActivity.this, EndOfVisitMenuActivity.class);
                    startActivity(intent);
                    finish();
                }
            }
            finish();
        } else {
            SnackBarToast.showMessage(view, "Please scan customer POD");
        }
    }
}