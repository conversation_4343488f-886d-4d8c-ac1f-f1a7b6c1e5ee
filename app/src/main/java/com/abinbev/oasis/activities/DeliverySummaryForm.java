package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.abinbev.oasis.ModelClasses.DeliveryItemPricing;
import com.abinbev.oasis.adapter.CheckInFullsTableAdapter;
import com.abinbev.oasis.adapter.DeliverySummaryAdapter;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CheckInController;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.PricingUtil;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.abinbev.oasis.R;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class DeliverySummaryForm extends AppCompatActivity {
    //views
    private TextView mDeliveryNo;
    private TextView mSapOrderNo;
    private RecyclerView mRecyclerView;


    private List<String> keyList;
    private int current_dlv=0;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_delivery_summary_form);

    }

    @Override
    protected void onResume() {
        super.onResume();
        iniViews();
        getData();
    }

    private void getData() {
        if(InvoiceController.DeliveryItemPricingDictionary==null){
            return;
        }
        if(!InvoiceController.DeliveryItemPricingDictionary.isEmpty()){
            keyList=new ArrayList<>(InvoiceController.DeliveryItemPricingDictionary.keySet());

           if(keyList.size()>0){
               List<String> completedDelvList = InvoiceController.getInstance().getCompletedInvoicing(keyList);

               for (int i=0;i<keyList.size();i++){
                   if(completedDelvList.contains(keyList.get(i))){
                       continue;
                   }else {
                       current_dlv=i;
                       break;
                   }
               }
               loadDeliveryList();
           }
        }
    }

    private void loadDeliveryList() {
        float totalInvoiceValue = 0.0f;
        if(InvoiceController.DeliveryItemPricingDictionary!=null && current_dlv<keyList.size() && InvoiceController.DeliveryItemPricingDictionary.keySet().size()>0){
            List<String> fields = new ArrayList<>();
            if(InvoiceController.DeliveryItemPricingDictionary==null || InvoiceController.DeliveryItemPricingDictionary.keySet()==null){
                return;
            }
            mDeliveryNo.setText("Delivery: " + keyList.get(current_dlv));
            DELIVERY delivery = DeliveryController.getInstance().getDeliveryByNo(keyList.get(current_dlv));
            mSapOrderNo.setText("SAP Order: " + delivery.getORD_NO());
            DeliveryController.getInstance().setCurrentDeliveryNo(keyList.get(current_dlv));
            List<DeliveryItemPricing> deliveryItemPricingList= InvoiceController.DeliveryItemPricingDictionary.get(keyList.get(current_dlv));

            for (int i = (deliveryItemPricingList.size() - 1); i >= 0; i--) {
                if(deliveryItemPricingList.get(i).getIsReturn_()==null || CheckInController.getInstance().isDummyMaterial(deliveryItemPricingList.get(i).getMatNo_())){
                    deliveryItemPricingList.remove(i);
                }
            }
            DeliverySummaryAdapter adapter = new DeliverySummaryAdapter(this, deliveryItemPricingList);
            mRecyclerView.setHasFixedSize(true);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
            mRecyclerView.setAdapter(adapter);

        }
    }

    private void iniViews() {
        mDeliveryNo=findViewById(R.id.xDeliverySummaryDeliveryNo);
        mSapOrderNo=findViewById(R.id.xSapOrderNo);
        mRecyclerView=findViewById(R.id.xDeliverySummaryRecyclerView);
    }

    public void closeActivity(View view) { finish(); }

    public void navigateToSettings(View view) { Utils.showSettingDialog(DeliverySummaryForm.this); }

    public void proceed_button_click(View view) {
        calculateSumOfInvoice();
        if(Constants.NAT_CUSTOMER.equals(TripSummaryController.getInstance().NAT_CUSTOMER_TYPE)){
            showGrvDialog();
        }else {
            navigateToCustomerRating();
        }
    }

    private void navigateToCustomerRating() {
        startActivity(new Intent(DeliverySummaryForm.this,CustomerSignatureActivity.class));
    }

    private void showGrvDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.grn_input_dialog,  null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xOdometerReadingDialogClose);
        Button dialogProceed = dialogView.findViewById(R.id.xOdometerReadingDialogProceed);
        TextView mMessage = dialogView.findViewById(R.id.xOdometerReadingDialogMessage);
        TextInputLayout mReadingLayout = dialogView.findViewById(R.id.xOdometerReadingDialogReading);
        final TextInputEditText mReadingEt = dialogView.findViewById(R.id.xOdometerReadingDialogReadingEt);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mReadingEt.setFocusedByDefault(true);
        }
        mMessage.setText("GRN/GRV Number");
        mReadingLayout.setHint("GRN/GRV Number");
        dialogProceed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(validate(mReadingEt)){
                        alertDialog.dismiss();
                        InvoiceController.getInstance().saveGRVNumber(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO(),DeliveryController.getInstance().getCurrentDeliveryNo(),mReadingEt.getText().toString());
                        calculateSumOfInvoice();
                        navigateToCustomerRating();
                }
            }
        });
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE  | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mReadingEt.requestFocus();
        alertDialog.show();
    }
    boolean hasWarned = false;
    private boolean validate(TextInputEditText mReadingEt) {
        if(!hasWarned && mReadingEt.getText().toString().isEmpty()){
            SnackBarToast.showMessage(mReadingEt,"No GRN/GRV Number entered. Are you sure you want to proceed?");
                hasWarned=true;
                return false;
        }else {
                hasWarned=false;
        }
        return true;
    }

    private void calculateSumOfInvoice() {
        if(InvoiceController.DeliveryItemPricingDictionary!=null && InvoiceController.DeliveryItemPricingDictionary.keySet().size()>0){
            float invoiceSum = 0;
            for (Map.Entry<String,List<DeliveryItemPricing>> entry :  InvoiceController.DeliveryItemPricingDictionary.entrySet()){
                float itemSum = 0;
                for (DeliveryItemPricing deliveryItemPricing : entry.getValue()){
                    itemSum = (float) (itemSum + (deliveryItemPricing.getIsReturn_().equals("X") ? (deliveryItemPricing.getNetValue_().intValue() * -1) : deliveryItemPricing.getNetValue_()));
                }
                invoiceSum+=itemSum;
            }
            InvoiceController.InvoiceSum = PricingUtil.DecimalRound(BigDecimal.valueOf(invoiceSum),2).floatValue();
        }
    }
}