package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;

import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TRL_PICK_DRP;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TrailerController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class TrailerDropOffActivity extends AppCompatActivity {
    private EditText trailer1Entry, trailer2Entry;
    private String custId = "";
    private SHIPMENT_HEADER shipment_header = null;
    private List<TRL_PICK_DRP> trl_pick_drpList;
    private TRL_PICK_DRP trailer1 = null;
    private TRL_PICK_DRP trailer2 = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_trailer_drop_off);
        iniViews();
        getData();
    }

    private void getData() {
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        if(visit==null){
            finish();
        }
        if (custId == null || custId.isEmpty()) {
            custId = visit.getCUST_NO();
        }
        shipment_header = DBHelper.getInstance().getShipmentRow();
        if (shipment_header == null) {
            finish();
        }
        loadDroppedTrailers();
    }

    private void loadDroppedTrailers() {
        trl_pick_drpList = new ArrayList<>();
        trl_pick_drpList = TrailerController.getInstance().getPickedOrDroppedTrailersForCustomer(custId, Constants.TRAILERDROP);
        if (trl_pick_drpList != null && trl_pick_drpList.size() > 0) {
            for (TRL_PICK_DRP trl_pick_drp : trl_pick_drpList) {
                if (trl_pick_drp.getTRL_TYPE() != null) {
                    if (trl_pick_drp.getTRL_TYPE() == 1) {
                        trailer1Entry.setText(trl_pick_drp.getTRL_NO()==null || trl_pick_drp.getTRL_NO().isEmpty()?"":trl_pick_drp.getTRL_NO());
                        trailer1 = trl_pick_drp;
                    } else if (trl_pick_drp.getTRL_TYPE() == 2) {
                        trailer2Entry.setText(trl_pick_drp.getTRL_NO()==null || trl_pick_drp.getTRL_NO().isEmpty()?"":trl_pick_drp.getTRL_NO());
                        trailer2 = trl_pick_drp;
                    }
                }
            }
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(TrailerDropOffActivity.this);
    }

    public void closeActivity(View view) {
        finish();
    }

    private void iniViews() {
        trailer1Entry = findViewById(R.id.xTrailerDropOffTrailer1Entry);
        trailer1Entry.setFocusable(false);
        trailer1Entry.setCursorVisible(false);
        trailer2Entry = findViewById(R.id.xTrailerDropOffTrailer2Entry);
        trailer2Entry.setFocusable(false);
        trailer2Entry.setCursorVisible(false);
    }

    public void trailer1_check_clicked(View view) {
        if(trailer1==null){
            trailer1Entry.setText(shipment_header.getTRL1_P()==null || shipment_header.getTRL1_P().isEmpty()?"":shipment_header.getTRL1_P());
        }else {
            trailer1Entry.setText(trailer1.getTRL_NO().isEmpty() || trailer1.getTRL_NO()==null?"":trailer1.getTRL_NO());
        }
    }

    public void trailer2_check_clicked(View view) {
        if(trailer2==null){
            trailer2Entry.setText(shipment_header.getTRL2_P()==null || shipment_header.getTRL2_P().isEmpty()?"":shipment_header.getTRL2_P());
        }else {
            trailer2Entry.setText(trailer2.getTRL_NO().isEmpty() || trailer2.getTRL_NO()==null?"":trailer2.getTRL_NO());
        }
    }

    public void clearTrailer1(View view) {
        trailer1Entry.setText("");
    }

    public void clearTrailer2(View view) {
        trailer2Entry.setText("");
    }

    public void trailer_drop_proceed_click(final View view) {
        if(validate(view)){
            if((trailer1Entry.getText().toString().isEmpty() && trailer1!=null) || (trailer2Entry.getText().toString().isEmpty() && trailer2!=null)){
                DialogMessage dialogMessage= new DialogMessage(this);
                dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Clearing Trailers will undo drop of trailers if any.", "Undo", "cancel", new DialogClickListner() {
                    @Override
                    public void onPositiveClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                        saveData(view);
                    }
                    @Override
                    public void onNegativeClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                    }

                    @Override
                    public void onNeutralClick(Dialog dialogMessage) {

                    }
                });
            }else {
                saveData(view);
            }
        }
    }

    private void saveData(View view) {

        boolean trailer1ToBeDropped = false;
        boolean trailer2ToBeDropped = false;
        boolean trailer1Undo = false;
        boolean trailer2Undo = false;

        if(!trailer1Entry.getText().toString().isEmpty() && trailer1==null){
            trailer1ToBeDropped=true;
        }

        if(!trailer2Entry.getText().toString().isEmpty() && trailer2==null){
            trailer2ToBeDropped=true;
        }

        if(trailer1Entry.getText().toString().isEmpty() && trailer1!=null){
            if(shipment_header.getTRL1_P().isEmpty() || shipment_header.getTRL1_P()==null || shipment_header.getTRL2_P().isEmpty() || shipment_header.getTRL2_P()==null  || trailer1ToBeDropped || trailer2ToBeDropped){
                trailer1Undo=true;
            }else {
                trailer1Entry.setText(trailer1.getTRL_NO());
                SnackBarToast.showMessage(view,"Truck is Full");
                return;
            }
        }

        if(trailer2Entry.getText().toString().isEmpty() && trailer2!=null){

            if(!(!(shipment_header.getTRL1_P().isEmpty() && shipment_header.getTRL1_P()==null) && trailer1Undo)
                    || (!(shipment_header.getTRL2_P().isEmpty() && shipment_header.getTRL2_P()==null) && trailer1Undo)
                    || (!trailer1ToBeDropped && trailer1Undo)
                    || (!trailer2ToBeDropped && trailer1Undo)){
                trailer2Undo=true;
            }else {
                trailer2Entry.setText(trailer2.getTRL_NO());
                SnackBarToast.showMessage(view,"Truck is Full");
                return;
            }
        }

        if(trailer1ToBeDropped){
            updateTrailerPickDrop(trailer1Entry.getText().toString(),1);
            shipment_header.setTRL1_P("");
        }

        if(trailer2ToBeDropped){
            updateTrailerPickDrop(trailer2Entry.getText().toString(),2);
            shipment_header.setTRL2_P("");
        }


        if(trailer1Undo){
            shipment_header.setTRL1_P(trailer1.getTRL_NO());
            try {
                ApplicationManager.getInstance().getDataManager().delete(trailer1);
                this.trl_pick_drpList.remove(trl_pick_drpList.indexOf(trailer1));
            } catch (DBException e) {
                Logger.e("", e);
            }
            trailer1=null;
        }

        if(trailer2Undo){
            shipment_header.setTRL2_P(trailer2.getTRL_NO());
            try {
                ApplicationManager.getInstance().getDataManager().delete(trailer2);
                this.trl_pick_drpList.remove(trl_pick_drpList.indexOf(trailer2));
            } catch (DBException e) {
                Logger.e("", e);
            }
            trailer2=null;
        }

        if(trl_pick_drpList!=null && trl_pick_drpList.size()>0){
            TrailerController.getInstance().updateTrailerPickDrop(trl_pick_drpList);
        }
        DBHelper.getInstance().updateShipmentStatus(shipment_header);
        if(trl_pick_drpList!=null && trl_pick_drpList.size()>0 && trl_pick_drpList.get(0)!=null){
            DBHelper.getInstance().createNewTime(trl_pick_drpList.get(0).getCUST_NO(),Constants.TIME_TYPE_TRAILER);
        }
        finish();
    }

    private boolean validate(View view) {
        return true;
    }


    private void updateTrailerPickDrop(String trailerId, int TrlPosition)
    {

        TRL_PICK_DRP trl1PickDropRow = null;
        try {
            trl1PickDropRow = new TRL_PICK_DRP();
            trl1PickDropRow.setFid(shipment_header.getLid());
            trl1PickDropRow.setSHIP_NO(shipment_header.getSHIP_NO());
            trl1PickDropRow.setTRL_TYPE((long) TrlPosition);
            trl1PickDropRow.setTRL_NO(trailerId);
            trl1PickDropRow.setCUST_NO(custId);
            trl1PickDropRow.setIS_DRP("X");
            TrailerController.getInstance().insertOrUpdateTRLPickDrop(trl1PickDropRow);
        } catch (DBException e) {
            Logger.e("", e);
        }

    }
}