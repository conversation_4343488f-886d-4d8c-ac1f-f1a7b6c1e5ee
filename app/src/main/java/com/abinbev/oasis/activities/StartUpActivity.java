package com.abinbev.oasis.activities;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.PermissionHelper;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;
import com.unvired.database.DBException;
import com.unvired.exception.ApplicationException;
import com.unvired.fcm.RegisterToFCM;
import com.unvired.logger.Logger;
import com.unvired.login.AuthenticationService;
import com.unvired.login.LoginListener;
import com.unvired.login.LoginParameters;
import com.unvired.login.LoginParameters.LOGIN_TYPE;
import com.unvired.login.UnviredAccount;
import com.unvired.model.ApplicationVersion;
import com.unvired.model.InitialURL;
import com.abinbev.oasis.R;
import com.unvired.utils.FrameworkHelper;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

public class StartUpActivity extends AppCompatActivity implements LoginListener {

    private static final String CLASS_NAME = StartUpActivity.class.getName();
    private static final String URL = "https://ump.za.ab-inbev.com/UMP";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.d("StartUpActivity", "onCreate started");
        try {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_start_up);
            Log.d("StartUpActivity", "Layout set successfully");
            
            // Check if Firebase is available before initializing
            if (FirebaseApp.getApps(this).isEmpty()) {
                Log.d("StartUpActivity", "Initializing Firebase");
                FirebaseApp.initializeApp(this);
            }
            
            //For getting FCM token
            FirebaseMessaging.getInstance().getToken()
                    .addOnCompleteListener(new OnCompleteListener<String>() {
                        @Override
                        public void onComplete(@NonNull Task<String> task) {
                            if (!task.isSuccessful()) {
                                Log.w("StartUpActivity", "Fetching FCM registration token failed", task.getException());
                                return;
                            }

                            String token = task.getResult();
                            String msg = getString(R.string.msg_token_fmt, token);
                            Log.d("StartUpActivity", "FCM Token: " + msg);
                        }
                    });
            Log.d("StartUpActivity", "onCreate completed successfully");
        } catch (Exception e) {
            Log.e("StartUpActivity", "Error in onCreate", e);
            finish();
        }
    }

    @Override
    public void onResume() {
        Log.d("StartUpActivity", "onResume started");
        try {
            super.onResume();
            initialize();
            Log.d("StartUpActivity", "onResume completed");
        } catch (Exception e) {
            Log.e("StartUpActivity", "Error in onResume", e);
            finish();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == PermissionHelper.GENERAL_PERMISSION) {
            boolean permitted = true;
            for (int i : grantResults) {
                if (i == PackageManager.PERMISSION_DENIED) {
                    permitted = false;
                }
            }

            if (permitted) {
                initializeFramework(this);
            } else {
                finish();
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    // private void initialize() {
    //     /*
    //      * Check and obtain basic user permissions
    //      */

    //     List<String> permissionList = new ArrayList<>();

    //     if (!PermissionHelper.hasPhonePermission(this)) {
    //         permissionList.add(Manifest.permission.READ_PHONE_STATE);
    //     }
    //     if (!PermissionHelper.hasStoragePermission(this)) {
    //         permissionList.add(Manifest.permission.READ_EXTERNAL_STORAGE);
    //         permissionList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
    //     }
    //     if (permissionList.size() > 0) {
    //         PermissionHelper.requestPermissions(this, permissionList);
    //     } else {
    //         initializeFramework(StartUpActivity.this);
    //     }
    // }


    /* -------------------------------------------------------- */
    /* 1.  Replace your current  initialize()  with this        */
    /* -------------------------------------------------------- */
    private void initialize() {
        final List<String> permissionsNeeded = permissionsStillNeeded();

        if (permissionsNeeded.isEmpty()) {            // we already have everything
            initializeFramework(this);
            return;
        }

        // Show rationale only the first time we really have to ask
        if (shouldShowRequestPermissionRationale(permissionsNeeded)) {
            new AlertDialog.Builder(this)
                    .setTitle("Permission required")
                    .setMessage("This app needs storage & phone access to operate offline "
                            + "and notifications to keep you updated.")
                    .setPositiveButton("OK", (d, w) -> requestPermissions(
                            permissionsNeeded.toArray(new String[0]),
                            PermissionHelper.GENERAL_PERMISSION))
                    .setCancelable(false)
                    .show();
        } else {   // no rationale needed (first ask or user ticked “don’t ask again”)
            requestPermissions(
                    permissionsNeeded.toArray(new String[0]),
                    PermissionHelper.GENERAL_PERMISSION);
        }
    }

    /* -------------------------------------------------------- */
    /* 2.  Helper that builds the correct list for every API    */
    /* -------------------------------------------------------- */
    private List<String> permissionsStillNeeded() {
        final List<String> list = new ArrayList<>();

        // 1. Phone (IMEI) – dangerous on every API level
        if (!PermissionHelper.hasPhonePermission(this))
            list.add(Manifest.permission.READ_PHONE_STATE);

        // 2. Storage / Media
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {   // 33+
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES)
                    != PackageManager.PERMISSION_GRANTED)
                list.add(Manifest.permission.READ_MEDIA_IMAGES);
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_VIDEO)
                    != PackageManager.PERMISSION_GRANTED)
                list.add(Manifest.permission.READ_MEDIA_VIDEO);
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_AUDIO)
                    != PackageManager.PERMISSION_GRANTED)
                list.add(Manifest.permission.READ_MEDIA_AUDIO);
        } else {   // 32 and lower – legacy external storage
            if (!PermissionHelper.hasStoragePermission(this)) {
                list.add(Manifest.permission.READ_EXTERNAL_STORAGE);
                list.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        }

        // 3. Notifications (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS)
                    != PackageManager.PERMISSION_GRANTED)
                list.add(Manifest.permission.POST_NOTIFICATIONS);
        }

        return list;
    }

    /* tiny helper */
    private boolean shouldShowRequestPermissionRationale(List<String> perms) {
        for (String p : perms) {
            if (shouldShowRequestPermissionRationale(p)) return true;
        }
        return false;
    }

    public void initializeFramework(Context context) {
        /*
         * Initialize Framework parameters as per Documentation
         */

        String metaDataXml = null;

        try {
            InputStream inputStream = this.getResources().openRawResource(R.raw.metadata);
            metaDataXml = FrameworkHelper.getString(inputStream);
        } catch (ApplicationException e) {
            Logger.e("", e);
        }
        this.setListOfInitialURLs();

        LoginParameters.setUrl(URL);
        LoginParameters.setAppTitle(Constants.APP_NAME);
        LoginParameters.setAppName(Constants.APP_NAME);
        LoginParameters.setCompany(Constants.APP_COMPANY);
        LoginParameters.setMetaDataXml(metaDataXml);
        LoginParameters.setLoginListener(this);
        LoginParameters.setAutoUpgrade(false);
        LoginParameters.setLoginTypes(new LOGIN_TYPE[]{LOGIN_TYPE.UNVIRED_ID});
        LoginParameters.setDemoModeRequired(false);
        LoginParameters.showCompanyField(false);
        LoginParameters.setContext(context);
        LoginParameters.enablePasswordLessLogin(false);
        ApplicationVersion.setBUILD_NUMBER("1");
//        set Logo
        LoginParameters.setLogo(getResources().openRawResource(R.raw.oasis));
//        set custom theme
//        LoginParameters.setCustomThemeResource(getResources().openRawResource(R.raw.theme));
//        UITheme.setCustomTheme(getResources().openRawResource(R.raw.theme));

        try {
            AuthenticationService.login(this.getApplicationContext());
        } catch (ApplicationException e) {
            Logger.e("", e);
        } catch (DBException e) {
            Logger.e("", e);
        } catch (Exception e) {
            Logger.e("", e);
        }


    }

    private void setListOfInitialURLs() {
        Vector<InitialURL> initialURLs = new Vector<InitialURL>();
        initialURLs.addElement(new InitialURL("https://ump.za.ab-inbev.com/UMP", "Production", true));
        initialURLs.addElement(new InitialURL("http://ump.beerdivision.africa.gcn.local:8080/UMP", "Old Prod", false)); //This needs to be removed eventually
        initialURLs.addElement(new InitialURL("https://ump-qa.za.ab-inbev.com/UMP", "Quality", false));
        initialURLs.addElement(new InitialURL("http://*************:8080/UMP", "Quality (IP)", false)); //This needs to be removed eventually
        initialURLs.addElement(new InitialURL("https://umpdev.onunvired.com/UMP", "Unvired Internal", false));
        LoginParameters.setInitialURLs(initialURLs);
    }

    /*
     * Required only for GCM
     */
    private void checkGooglePlayServices() {
        Log.d("StartUpActivity", "checkGooglePlayServices started");
        
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(StartUpActivity.this);
            builder.setTitle(getResources().getString(R.string.playServiceError));
            
            int result = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this);
            Log.d("StartUpActivity", "Google Play Services result: " + result);

            switch (result) {
                case ConnectionResult.SUCCESS:
                    Log.d("StartUpActivity", "Google Play Services available, initializing Firebase");
                    try {
                        if (FirebaseApp.getApps(this).isEmpty()) {
                            FirebaseApp.initializeApp(this);
                        }
                        FirebaseMessaging.getInstance().setAutoInitEnabled(true);
                        RegisterToFCM.sendRegistrationToServer();
                        Log.d("StartUpActivity", "Firebase initialized, navigating to home");
                        navigateToHomeActivity();
                    } catch (Exception e) {
                        Log.e("StartUpActivity", "Error initializing Firebase", e);
                        finish();
                    }
                    break;
                
                case ConnectionResult.SERVICE_MISSING:
                    builder.setPositiveButton(getResources().getString(R.string.install), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            startActivity(new Intent(Intent.ACTION_VIEW,
                                    Uri.parse("https://play.google.com/store/apps/details?id=com.google.android.gms")));
                            finish();
                        }
                    });
                    builder.setMessage(getResources().getString(R.string.installPlayService));
                    builder.show();
                    break;

                case ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED:
                    builder.setPositiveButton(getResources().getString(R.string.update), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            startActivity(new Intent(Intent.ACTION_VIEW,
                                    Uri.parse("https://play.google.com/store/apps/details?id=com.google.android.gms")));
                            finish();
                        }
                    });
                    builder.setMessage(getResources().getString(R.string.updatePlayService));
                    builder.show();
                    break;

                default:
                    builder.setMessage(getResources().getString(R.string.errorPlayService));
                    builder.show();
                    break;
            }
        } catch (Exception e) {
            Log.e("StartUpActivity", "Error in checkGooglePlayServices", e);
            finish();
        }
    }

    private void navigateToHomeActivity() {
        DEPOT_HEADER depots = DepotController.getInstance().getSelectedDepotRow();
        if (depots != null) {
            switch (DBHelper.getInstance().getShipmentStatus()) {
                case -1:
                case Constants.SHIP_STATUS_UPLAOD_END:
                    Intent intent = new Intent(this, ShipmentListActivity.class);
                    intent.putExtra("DEPOT", depots.getDEPOT());
                    startActivity(intent);
                    break;
                case Constants.SHIP_STATUS_NEW:
                    initialSettings();
                    DataHelper.getInstance().setShipmentHeader(DBHelper.getInstance().getCurrentShipment());
                    Intent intent1 = new Intent(this, PreTripInspectionQuestionsActivity.class);
                    intent1.putExtra(Constants.SCREEN_NAVIGATION, Constants.SCREEN_NAVIGATION_MODE.PretripInspection);
                    startActivity(intent1);
                    break;
//                case Constants.SHIP_STATUS_PRE_TRIP_COMPL:
//                     initialSettings();
//                    ScreenHelper.CurrentScreensNavigationMode = ScreensNavigationMode.PreForkLiftInspection;
//                    return new TripInspectionForm();
//
                case Constants.SHIP_STATUS_PRE_FORKLIFT_COMPL:
                    initialSettings();
                    Intent intent2 = new Intent(this, OdometerActivity.class);
                    startActivity(intent2);
                    break;

                case Constants.SHIP_STATUS_INITIAL_ODOMETER_COMPL:
                    initialSettings();
                    Intent intent3 = new Intent(this, TripSummaryActivity.class);
                    startActivity(intent3);
                    break;
//
//                case Constants.SHIP_STATUS_POST_FORKLIFT:
//                     initialSettings();
//                    ScreenHelper.CurrentScreensNavigationMode = ScreensNavigationMode.PostForkLiftInspection;
//                    return new TripInspectionForm();
//
                case Constants.SHIP_STATUS_END_TRIP:
                case Constants.SHIP_STATUS_FINAL_REPORT_PRINTED:
                case Constants.SHIP_STATUS_FINAL_ODOREADING_RECORDED:
                    initialSettings();
                    Intent intent4 = new Intent(this, EndOfTripMenuActivity.class);
                    startActivity(intent4);
                    break;

                case Constants.SHIP_STATUS_START_CHECKIN:
                    initialSettings();
                    Intent intent5 = new Intent(this, CheckInActivity.class);
                    startActivity(intent5);
                    break;
//
                case Constants.SHIP_STATUS_END_CHECKIN:
                    Intent intent6 = new Intent(this, UploadMenuActivity.class);
                    startActivity(intent6);
                    break;

            }
        } else {
            startActivity(new Intent(this, DepoSetupActivity.class));
        }
        this.finish();
    }


    public static void initialSettings() {
        OasisCache.cacheCasesPerPallet();
    }

    @Override
    public void loginSuccessful() {
        checkGooglePlayServices();
//        navigateToHomeActivity();

    }

    @Override
    public void loginCancelled() {

    }

    @Override
    public void loginFailure(String s) {

    }

    @Override
    public void authenticateAndActivationSuccessful() {
        checkGooglePlayServices();
//        navigateToHomeActivity();
    }

    @Override
    public void authenticateAndActivationFailure(String s) {

    }

    @Override
    public void invokeAppLoginActivity(boolean b) {

    }

    @Override
    public void noSDCardFound(String s) {

    }

    @Override
    public void invokeMultiAccountActivity(List<UnviredAccount> list) {

    }


    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
