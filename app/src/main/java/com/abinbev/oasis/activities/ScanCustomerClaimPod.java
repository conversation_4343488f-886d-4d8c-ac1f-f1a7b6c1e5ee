package com.abinbev.oasis.activities;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.Log;
import android.view.OrientationEventListener;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.adapter.ImageListAdapter;
import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.util.AttachmentHelper;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.Loader.ImageRowMoveCallback;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.Utils;
import com.unvired.logger.Logger;
import com.abinbev.oasis.BuildConfig;
import com.abinbev.oasis.R;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;

public class ScanCustomerClaimPod extends AppCompatActivity implements SensorEventListener {
    private static final int MY_CAMERA_REQUEST_CODE = 100;
    private final int PICK_IMAGE_CAMERA = 1, PICK_IMAGE_GALLERY = 2;
    Button selectImageButton;
    LinearLayout editLayout;
    Bitmap bitmap;
    String imagePath;
    String type;
    TextView title;
    RecyclerView recyclerView;
    ArrayList<Uri> imageList;
    ImageListAdapter imageAdapter;
    private SensorManager sensorManager;
    private boolean alertShown = false;

    String deliveryNo;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan_customer_claim);
        type = getIntent().getExtras().getString("type");
        sensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);


        imageList = new ArrayList<>();
        recyclerView = findViewById(R.id.recycler);
        imageAdapter = new ImageListAdapter(imageList, this);
        recyclerView.setLayoutManager(new LinearLayoutManager(ScanCustomerClaimPod.this));
        recyclerView.setAdapter(imageAdapter);

//        ItemTouchHelper.Callback callback = new ImageRowMoveCallback(imageAdapter);
//        ItemTouchHelper touchHelper = new ItemTouchHelper(callback);
//        touchHelper.attachToRecyclerView(recyclerView);

        selectImageButton = findViewById(R.id.selectImageButton);
        editLayout = findViewById(R.id.editLayout);
        title = findViewById(R.id.xScanPodClaimTitle);

        ATTACHMENT attachment = null;
        if (type.equals("claim")) {
            attachment = AttachmentHelper.getInstance().getCustomerClaim();
            title.setText("Customer Claim");
        } else if (type.equals("pod")) {
            attachment = AttachmentHelper.getInstance().getCustomerPod();
            title.setText("Customer POD");
        }
        if (attachment != null) {
            selectImageButton.setVisibility(View.GONE);
            editLayout.setVisibility(View.VISIBLE);
            byte[] decodedString = Base64.decode(attachment.getDATA(), Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
        } else {
            selectImage();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == MY_CAMERA_REQUEST_CODE) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                selectImage();
            } else {
                finish();
            }
        }
    }

    public void requestCameraPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{Manifest.permission.CAMERA}, MY_CAMERA_REQUEST_CODE);
            }
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(ScanCustomerClaimPod.this);
    }

    // Select image from camera and gallery
    void selectImage() {
        try {
            PackageManager pm = getPackageManager();
            int hasPerm = pm.checkPermission(Manifest.permission.CAMERA, getPackageName());
            if (hasPerm == PackageManager.PERMISSION_GRANTED) {
                final CharSequence[] options = {"Take Photo", "Choose From Gallery", "Cancel"};
                AlertDialog.Builder builder = new AlertDialog.Builder(ScanCustomerClaimPod.this);
                builder.setTitle("Select Option");
                builder.setItems(options, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int item) {
                        if (options[item].equals("Take Photo")) {
                            dialog.dismiss();
                            try {
                                imagePath = ImageUtil.getFileUri();
                                File file = new File(imagePath);
                                Uri uri = FileProvider.getUriForFile(
                                        ScanCustomerClaimPod.this,
                                        BuildConfig.APPLICATION_ID + "." + getLocalClassName() + ".provider",
                                        file);
                                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                intent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
                                startActivityForResult(intent, PICK_IMAGE_CAMERA);
                            } catch (Exception e) {
                                Log.e("EXCEP", e.toString());
                            }
                        } else if (options[item].equals("Choose From Gallery")) {
                            dialog.dismiss();
                            Intent pickPhoto = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                            pickPhoto.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
                            startActivityForResult(Intent.createChooser(pickPhoto, "Select images"), PICK_IMAGE_GALLERY);
                        } else if (options[item].equals("Cancel")) {
                            dialog.dismiss();
                        }
                    }
                });
                builder.show();
            } else
                requestCameraPermission();
        } catch (Exception e) {
            Toast.makeText(this, "Camera Permission error", Toast.LENGTH_SHORT).show();
            Logger.e("", e);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Register the orientation sensor
        Sensor orientationSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        if (orientationSensor != null) {
            sensorManager.registerListener(this, orientationSensor, SensorManager.SENSOR_DELAY_NORMAL);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // Unregister the sensor when not needed
        sensorManager.unregisterListener(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Integer scanImageWidth=0;
        try{
            scanImageWidth = CustomizingController.getInstance().getIntKeyValue(Constants.IMAGE_WIDTH);
            if(scanImageWidth == null || scanImageWidth ==0){
                scanImageWidth = Constants.IMAGE_WIDTH_DEFAULT;
            }
        }catch (Exception e){
            Log.e(getClass().getSimpleName(), "Error loading image width", e);
        }

        if (resultCode == RESULT_OK) {
            if (requestCode == PICK_IMAGE_CAMERA) {
                try {
                    File file = new File(imagePath);
                    if (file.exists()) {
                        Uri uri = Uri.parse(String.valueOf("file://" + imagePath));
                        Bitmap bitCamImage = ImageUtil.getBitmapFromUri(this,uri);
                        if(bitCamImage!=null){
                            Bitmap resizedCamBitmap = resizeBitmap(bitCamImage, scanImageWidth);
                            Uri uriImage = ImageUtil.getImageUri(getApplicationContext(), resizedCamBitmap);
                            imageList.add(uriImage);
                            imageAdapter.notifyDataSetChanged();
                            selectImageButton.setVisibility(View.GONE);
                            editLayout.setVisibility(View.VISIBLE);
                        }

                    }
                } catch (Exception e) {
                    Logger.e("", e);
                }
            } else if (requestCode == PICK_IMAGE_GALLERY) {
                if (data.getClipData() != null) {
                    try {
                        int x = data.getClipData().getItemCount();
                        for (int i = 0; i < x; i++) {
                            Bitmap bitImage = ImageUtil.decodeUri(getApplicationContext(), data.getClipData().getItemAt(i).getUri(), 1024);
                            Bitmap resizedBitmap = resizeBitmap(bitImage, scanImageWidth);
                            Uri uriImage = ImageUtil.getImageUri(getApplicationContext(), resizedBitmap);
                            imageList.add(uriImage);
                            imageAdapter.notifyDataSetChanged();
                        }
                    } catch (Exception e) {
                        Logger.e("", e);
                    }
                } else if (data.getData() != null) {
                    String imgurl = data.getData().getPath();
                    imageList.add(Uri.parse(imgurl));
                    imageAdapter.notifyDataSetChanged();
                }
                selectImageButton.setVisibility(View.GONE);
                editLayout.setVisibility(View.VISIBLE);
            }
        }
    }

    private Bitmap resizeBitmap(Bitmap bitmap, int width) {
        float aspectRatio = (float) bitmap.getHeight() / (float) bitmap.getWidth();
        int targetHeight = Math.round(width * aspectRatio);
        return Bitmap.createScaledBitmap(bitmap, width,targetHeight,false);
    }


    public void select_image(View view) {
        selectImage();
    }


    public void submit_pod_claim(View view) {
        Loader.showLoader(ScanCustomerClaimPod.this, "Please wait, Images are saving..");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // Execute mergeAndSaveImages after 4 seconds
                mergeAndSaveImages(imageList);
                // Hide the loader after mergeAndSaveImages completes
                Loader.hideLoader();
            }
        }, 500);
    }

    public void addMoreImages(View view) {
        selectImage();

    }

    public void mergeAndSaveImages(ArrayList<Uri> uriList) {
        try {
            if (uriList.size() >= 4) {
                Toast.makeText(getApplicationContext(), "Upload up to only 3 images", Toast.LENGTH_SHORT).show();
                return; // Return early if more than 3 images are provided
            }

            ArrayList<Bitmap> bitImageList = new ArrayList<>();
            for (Uri uri : uriList) {
                Bitmap bitImage = null;
                try {
                    bitImage = loadBitmapFromUri(uri);
                    if (bitImage != null) {
                        bitImageList.add(bitImage);
                    }
                } catch (IOException e) {
                    Log.e(getClass().getSimpleName(), "Error loading bitmap from URI: " + uri, e);
                }
            }

            if (bitImageList.isEmpty()) {
                Toast.makeText(getApplicationContext(), "No valid images found", Toast.LENGTH_SHORT).show();
                return; // Return early if no valid images are found
            }

            Bitmap mergedImage = ImageUtil.drawBitmapImages(bitImageList);
            Toast.makeText(ScanCustomerClaimPod.this, "Your image is being saved", Toast.LENGTH_SHORT).show();
            Log.i(getClass().getSimpleName(), "Image is getting saved");


            try {
                ImageUtil.saveCapturedImage(mergedImage);
                Log.i(getClass().getSimpleName(), "Save capture image complete");
            } catch (Exception e) {
                Log.e(getClass().getSimpleName(), "Exception in saveCapturedImage", e);
            }

            if ("claim".equals(type)) {
                AttachmentHelper.getInstance().storeCustomerClaim(mergedImage);
            } else if ("pod".equals(type)) {
                AttachmentHelper.getInstance().storeCustomerPod(mergedImage);
            }

            finish();
            Log.i(getClass().getSimpleName(), "ScanCustomerClaimPod");
        } catch (Exception e) {
            Log.e(getClass().getSimpleName(), "Exception in mergeAndSaveImages", e);
        }
    }

    private Bitmap loadBitmapFromUri(Uri uri) throws IOException {
        Bitmap bitImage;
        if (uri.toString().contains("/storage/")) {
            bitImage = MediaStore.Images.Media.getBitmap(getApplicationContext().getContentResolver(), uri);
            int imageWidth = bitImage.getWidth();
            int imageHeight = bitImage.getHeight();
            Matrix matrix = new Matrix();
            if (imageWidth > imageHeight) {
                matrix.postRotate(90);
            }
            return Bitmap.createBitmap(bitImage, 0, 0, imageWidth, imageHeight, matrix, true);
        } else {
            return MediaStore.Images.Media.getBitmap(this.getContentResolver(), uri);
        }
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        float azimuth = event.values[0];
        float pitch = event.values[1];
        float roll = event.values[2];

        // Check for landscape orientation
        if (Math.abs(roll) > 45) {
            // Device is in landscape mode, show alert
            if (!alertShown) {
                showLandscapeAlert();
                alertShown = true;
            }
        } else {
            // Reset the alert flag if the device is back to portrait mode
            alertShown = false;
        }
    }

    private void showLandscapeAlert() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setMessage("Please rotate your device to portrait mode before taking a photo.")
                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        // Close the dialog
                        dialog.dismiss();
                    }
                });
        AlertDialog alert = builder.create();
        alert.show();
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int i) {

    }
}