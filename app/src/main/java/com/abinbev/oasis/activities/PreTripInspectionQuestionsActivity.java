package com.abinbev.oasis.activities;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.abinbev.oasis.adapter.QuestionListAdapter;
import com.abinbev.oasis.be.ANSWER_OPTION_HEADER;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TRIP_INSP;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.oasis.R;

import java.util.List;

public class PreTripInspectionQuestionsActivity extends AppCompatActivity implements View.OnClickListener {

    RecyclerView mRecyclerView;
    Button mNext;
    Constants.SCREEN_NAVIGATION_MODE currentScreensNavigationMode;
    private List<TRIP_INSP> tripInspHeader = null;
    private DialogMessage dialogMessage;
    public SHIPMENT_HEADER shipmentHeader;
    public TextView titleText;
    public ImageView backButton;

    public PreTripInspectionQuestionsActivity() {
        dialogMessage = null;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pre_trip_inspection_questions);
        titleText = (TextView)findViewById(R.id.titleText);
        backButton = (ImageView)findViewById(R.id.xBackButton);
        currentScreensNavigationMode = (Constants.SCREEN_NAVIGATION_MODE) getIntent().getSerializableExtra(Constants.SCREEN_NAVIGATION);
        if(currentScreensNavigationMode == null) {
            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;
        }
        String currentInspectionType = doInspectionTypeSettings();
        tripInspHeader = DBHelper.getInstance().getQuestions(currentInspectionType);
        iniViews();
        iniListners();
        setupRecyclerview();
        this.dialogMessage = new DialogMessage(this);
    }

    private void iniListners() {
        mNext.setOnClickListener(this);
    }

    private void iniViews()
    {
        mRecyclerView=findViewById(R.id.xQuestionsRecyclerview);
        mNext=findViewById(R.id.xPreTripInspectionQuestionsNext);
    }

    private void setupRecyclerview() {
        QuestionListAdapter adapter = new QuestionListAdapter(this, tripInspHeader,currentScreensNavigationMode);
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mRecyclerView.setAdapter(adapter);
    }

    @SuppressLint("SetTextI18n")
    public void navigateToSettings(View view) {
        Utils.showSettingDialog(PreTripInspectionQuestionsActivity.this);
    }

    public void validateAnswers(View view) {
        view.getWidth();

    }

    @Override
    public void onClick(View v) {
        SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
        if(currentShipmentHeader == null) {
            SnackBarToast.showMessage(v, "Shipment data is not complete or incorrect");
            finish();
            return;
        }
        if(v.getId()==R.id.xPreTripInspectionQuestionsNext){
            Loader.showLoader(this,"please wait");
            if(!validate(v)) {
                SnackBarToast.showMessage(v, getString(R.string.pre_trip_insp_error));
                Loader.hideLoader();
                return;
            }

            switch (currentScreensNavigationMode)
            {
                case PretripInspection:

                    if (DBHelper.getInstance().isForkliftInspectionRequired())
                    {
                        showInfo(getResources().getString(R.string.continue_conformation));
                    }
                    else
                    {
                        currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection;
                        CheckAnswersAndNavigate();

                    }
                    break;

                case PostTripInspection:
                    if (DBHelper.getInstance().isForkliftInspectionRequired())
                    {
                        showInfo(getResources().getString(R.string.continue_conformation));
                    }
                    else
                    {
                        currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PostForkLiftInspection;
                        CheckAnswersAndNavigate();

                    }
                    break;

                case PreForkLiftInspection:
                case PostForkLiftInspection:
                    CheckAnswersAndNavigate();
                    break;
            }

        }
    }

    private void CheckAnswersAndNavigate() {

        //Continue with the older implementation
        boolean isSupervisorSignReqd = false;
        ANSWER_OPTION_HEADER answerRow = null;
        TRIP_INSP tripRow = null;
        AUTH_HEADER authHeader = null;
        tripInspHeader.size();
        for( int  i = 0 ; i < tripInspHeader.size(); i++) {
            List<ANSWER_OPTION_HEADER> tempAnswerList = DBHelper.getInstance().getAnswerOptions(tripInspHeader.get(i).getQ_ID());
            for(int j  =  0; j < tempAnswerList.size(); j++) {
                if(tempAnswerList.get(j).getANS_ID() == tripInspHeader.get(i).getANS_ID()) {
                    answerRow = tempAnswerList.get(j);
                    tripRow = tripInspHeader.get(i);
                    break;
                }
            }
            if(answerRow != null && !answerRow.getIS_AUTH_REQD().isEmpty() && answerRow.getIS_AUTH_REQD().equalsIgnoreCase("X")) {
                if(tripRow != null) {
                    authHeader = DBHelper.getInstance().getAuthHeaderByID(tripRow.getAUTH_BY());
                    if(authHeader != null) {
                        DataHelper.getInstance().setAuthDetails(authHeader.getUSER_ID(),  authHeader.getNAME());
                    }
                }
                isSupervisorSignReqd = true;
                break;
            }
        }
        Loader.hideLoader();
        //Current Screen navigation mode is ForkLift Check if any of the pretrip or posttrip answer requires auth.

        if (isSupervisorSignReqd || ((Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection == currentScreensNavigationMode ||
                Constants.SCREEN_NAVIGATION_MODE.PostForkLiftInspection == currentScreensNavigationMode)
                && DBHelper.getInstance().PreTripOrPostTripReqAuth(currentScreensNavigationMode)))
        {
            Intent intent = new Intent(PreTripInspectionQuestionsActivity.this, ManagerSignatureActivity.class);
            intent.putExtra(Constants.SCREEN_NAVIGATION, currentScreensNavigationMode);
            startActivity(intent);
        }
        else
        {
            dialogMessage.showDialogWithPositiveAndNegativeBtn("", getString(R.string.continue_conformation), getString(R.string.yes), getString(R.string.no), new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    Intent intent = new Intent(PreTripInspectionQuestionsActivity.this, DriverSignatureActivity.class);
                    intent.putExtra(Constants.SCREEN_NAVIGATION, currentScreensNavigationMode);
                    startActivity(intent);
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }
    }

    private void showInfo(String msg) {
        Loader.hideLoader();
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", msg, getString(R.string.yes), getString(R.string.no), new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                switch (currentScreensNavigationMode)
                {
                    case PretripInspection:

                            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection;
                            shipmentHeader = DataHelper.getInstance().getShipmentHeader();
                            shipmentHeader.setSTAT((long) Constants.SHIP_STATUS_PRE_TRIP_COMPL);
                            DBHelper.getInstance().updateShipmentStatus(shipmentHeader);
                            recreate();

                    case PostTripInspection:

                            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PostForkLiftInspection;
                            shipmentHeader = DataHelper.getInstance().getShipmentHeader();
                            shipmentHeader.setSTAT((long) Constants.SHIP_STATUS_POST_FORKLIFT);
                            DBHelper.getInstance().updateShipmentStatus(shipmentHeader);
                            recreate();
                }

            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }

    public boolean validate(View v) {
        boolean navigate = true;
        for( int i = 0; i < tripInspHeader.size(); i++) {
            if(tripInspHeader.get(i).getANS_TEXT() == null || tripInspHeader.get(i).getANS_TEXT().isEmpty()) {
                navigate = false;
            }
        }
        return navigate;
    }


    public void updateTripInspList(List<TRIP_INSP> tripInspHeaderList) {
        tripInspHeader = tripInspHeaderList;
    }

    private String doInspectionTypeSettings()
    {
        String currentInspectionType = Constants.TRIP_INSP_TYPE_PRETRIP;
        String title = "Pre Trip Inspection";
        Boolean backEnabled = false;

        switch (currentScreensNavigationMode)
        {
            case PretripInspection:
                titleText.setText(getResources().getString(R.string.pre_trip_insp_title));
                backButton.setVisibility(View.GONE);
                backEnabled = false;
                break;

            case PreForkLiftInspection:
                titleText.setText(getResources().getString(R.string.pre_trip_insp_fl_title));
                backButton.setVisibility(View.GONE);
                backEnabled = false;
                currentInspectionType = Constants.TRIP_INSP_TYPE_PRE_FORKLIFT;
                break;

            case PostTripInspection:
                titleText.setText(getResources().getString(R.string.post_trip_insp_title));
                backButton.setVisibility(View.VISIBLE);
                backEnabled = true;
                currentInspectionType = Constants.TRIP_INSP_TYPE_POSTTRIP;
                break;

            case PostForkLiftInspection:
                titleText.setText(getResources().getString(R.string.post_trip_insp_fl_title));
                backButton.setVisibility(View.GONE);
                backEnabled = false;
                currentInspectionType = Constants.TRIP_INSP_TYPE_POST_FORKLIFT;
                break;

            default:
                titleText.setText(getResources().getString(R.string.pre_trip_insp_title));
                backButton.setVisibility(View.GONE);
                backEnabled = false;
                break;
        }
        return currentInspectionType;
    }

    @Override
    public void onBackPressed() {
    }

    public void closeActivity(View view) {
        finish();
    }
}
