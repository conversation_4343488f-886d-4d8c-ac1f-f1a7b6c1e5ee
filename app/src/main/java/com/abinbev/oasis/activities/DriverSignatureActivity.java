package com.abinbev.oasis.activities;

import android.app.AlertDialog;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatRadioButton;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;


import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.INVOICE;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.reports.InvoiceReport;
import com.abinbev.oasis.util.AttachmentHelper;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.DrawingView;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ScreenHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.io.File;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.io.IOException;
import java.util.Objects;

public class DriverSignatureActivity extends AppCompatActivity implements SnackBarToast.SnackBarActionListner, IPrintCallBack {
    private RelativeLayout mDrawingView;
    private DrawingView drawingView;
    private Constants.SCREEN_NAVIGATION_MODE currentNavigationMode;
    private SHIPMENT_HEADER currentShipmentHeader;
    private TextView mDriverName, customerDecline, deliveryLabel, sapOrderNo;
    private ImageView backButton;
    private AlertDialog reasonMenuDialog;
    String deliveryNo = null;
    List<DELIVERY> deliveryList = null;
    DELIVERY delivery;
    VISIT visitRow;
    boolean prepareSupplier = false;
    boolean checkNATAndPOD = false;


    InvoiceReport invoiceReport;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //currentNavigationMode=getIntent().getStringExtra(Constants.SCREEN_NAVIGATION);
        currentNavigationMode = (Constants.SCREEN_NAVIGATION_MODE) getIntent().getSerializableExtra(Constants.SCREEN_NAVIGATION);
        setContentView(R.layout.activity_driver_signature);
        PrinterUtils.setLocale(this);
        iniViews();
        setUpDrawingView();
        currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
        mDriverName.setText(new StringBuilder().append(currentShipmentHeader.getDRV1()).append(" - ").append(currentShipmentHeader.getDRV1_NAME()).toString());
        customerDecline.setVisibility(View.INVISIBLE);
        backButton.setVisibility(View.GONE);
        deliveryLabel.setVisibility(View.GONE);
        sapOrderNo.setVisibility(View.GONE);
        if (ScreenHelper.getFromCustomerSignature()) {
            customerDecline.setVisibility(View.VISIBLE);
            backButton.setVisibility(View.VISIBLE);
            deliveryLabel.setVisibility(View.VISIBLE);
            deliveryLabel.setText("Delivery: " + DeliveryController.getInstance().getCurrentDeliveryNo());
            sapOrderNo.setVisibility(View.VISIBLE);
            sapOrderNo.setText("SAP Order: " + DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getCurrentDeliveryNo()).getORD_NO());
            mDriverName.setVisibility(View.GONE);
        } else {
            customerDecline.setVisibility(View.INVISIBLE);
            backButton.setVisibility(View.GONE);
            deliveryLabel.setVisibility(View.GONE);
            deliveryLabel.setText("");
            sapOrderNo.setVisibility(View.GONE);
            sapOrderNo.setText("");
            mDriverName.setVisibility(View.VISIBLE);
            mDriverName.setGravity(Gravity.CENTER_HORIZONTAL);
        }
    }


    private void setUpDrawingView() {
        drawingView = new DrawingView(this);
        mDrawingView.addView(drawingView);
    }

    private void iniViews() {
        mDrawingView = findViewById(R.id.xDriverSignatureDrawingView);
        mDriverName = findViewById(R.id.xDriverCodeAndName);
        customerDecline = findViewById(R.id.xCustomerDecline);
        deliveryLabel = findViewById(R.id.xDeliveryNo);
        sapOrderNo = findViewById(R.id.xOrderNo);
        backButton = findViewById(R.id.xBackButton);
    }

    public void resetDrawingView(View view) {
        Animation rotateClk = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.clockwise_rotation);
        view.startAnimation(rotateClk);
        drawingView.clear();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(DriverSignatureActivity.this);
    }

    public void navigateToPreTripSecurity() {
        Loader.hideLoader();
        Intent intent = new Intent(this, PreTripInspectionSecurityActivity.class);
        intent.putExtra(Constants.SCREEN_NAVIGATION, currentNavigationMode);
        startActivity(intent);
        finish();
    }

    public void validateDriverSignature(View view) {
        Loader.showLoader(this, "please wait");
        if (!drawingView.isPathEmpty()) {
            processData();
        } else {
            Loader.hideLoader();
            SnackBarToast.showMessage(view, getString(R.string.please_sign));
        }
    }

    private void processData() {
        Bitmap image = drawingView.getBitmap();
        String hexValue = ImageUtil.getBase64FromBitmap(image);
        String Drv_ID = currentShipmentHeader.getDRV1();
        int attachment_type;
        String attachment_ref_no;
        ATTACHMENT attachmentRow = null;
        if (currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection)) {
            attachment_type = Constants.ATTACH_TYPE_PRETRIP_DRV;
            attachmentRow = DBHelper.getInstance().getAttachment(attachment_type, currentShipmentHeader.getSHIP_NO(), Drv_ID, Constants.IMAGE_TYPE, currentShipmentHeader.getSHIP_NO());
            attachment_ref_no = currentShipmentHeader.getSHIP_NO();
        } else if (currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration)) {

            attachment_type = Constants.ATTACH_TYPE_INVOICE;
            attachmentRow = DBHelper.getInstance().getAttachment(attachment_type, TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString() + DeliveryController.getInstance().getCurrentDeliveryNo().toString(), Drv_ID, Constants.IMAGE_TYPE);
            attachment_ref_no = TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString() + DeliveryController.getInstance().getCurrentDeliveryNo().toString();

        } else {
            attachment_type = Constants.ATTACH_TYPE_POSTTRIP_DRV;
            attachmentRow = DBHelper.getInstance().getAttachment(attachment_type, currentShipmentHeader.getSHIP_NO(), Drv_ID, Constants.IMAGE_TYPE, currentShipmentHeader.getSHIP_NO());
            attachment_ref_no = currentShipmentHeader.getSHIP_NO();
        }

        if (attachmentRow == null) {
            try {
                attachmentRow = new ATTACHMENT();
                attachmentRow.setDATA_TYPE(Constants.IMAGE_TYPE);
                attachmentRow.setLid(attachmentRow.getLid());
                attachmentRow.setSHIP_NO(currentShipmentHeader.getSHIP_NO());
                attachmentRow.setSIGNED_BY(Drv_ID);
                attachmentRow.setDATA(hexValue);
                attachmentRow.setFid(currentShipmentHeader.getLid());
                attachmentRow.setTYPE((long) attachment_type);
                attachmentRow.setREF_NO(attachment_ref_no);
                DBHelper.getInstance().insertOrUpdateAttachment(attachmentRow);
            } catch (DBException e) {
                Loader.hideLoader();
                Logger.e("", e);
            }
        } else {
            attachmentRow.setDATA(hexValue);
            DBHelper.getInstance().insertOrUpdateAttachment(attachmentRow);
        }
        if (currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection) || currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.PostForkLiftInspection)) {
            Loader.hideLoader();
            navigateToPreTripSecurity();
        } else if (currentNavigationMode.equals(Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration)) {
            ScreenHelper.setFromCustomerSignature(false);
            Loader.hideLoader();
            //preparePdf
            try {
                Font font = Utils.getFont(10f);
                invoiceReport = new InvoiceReport(font, false);
                //  ivoiceReport.generateReportPrint(Constants.DocType.Invoice,this,false,this);
                visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
                InvoiceController.generateInvoice(visitRow);
                //deliveryList = DeliveryController.getInstance().getDeliveryHeaders(visitRow.getVISIT_NO(), DeliveryController.Quantity.ALL);
                delivery = DeliveryController.getInstance().getDeliveryByNo(DeliveryController.getInstance().getCurrentDeliveryNo());
                invoiceReport.generateInvoicePdf(delivery, visitRow, Constants.DocType.Invoice, false, this, this, Constants.CopyType.CUSTOMER);

            } catch (Exception e) {
                Logger.e("", e);
            }
        }

    }

    public void navigateToEndOfVisitMenuForm() {
        Intent intent = new Intent(DriverSignatureActivity.this, EndOfVisitMenuActivity.class);
        startActivity(intent);
        finish();
    }


    public void navigateToScanCustomerPodClaim() {
        Intent intent = new Intent(DriverSignatureActivity.this, ScanCustomerPodClaimActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    public void onSnackBarAction(String flag) {

    }

    @Override
    public void onBackPressed() {
        if (ScreenHelper.getFromCustomerSignature()) {
            super.onBackPressed();
        }
    }

    public void customerDeclined(View view) {
        showDropTrailersMenu();

    }

    private void showDropTrailersMenu() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        final View dialogView = LayoutInflater.from(this).inflate(R.layout.customer_decline_reason_menu, null);
        builder.setView(dialogView);
        builder.setCancelable(false);
        reasonMenuDialog = builder.create();
        final List<REASON_HEADER> reasonHeaderList;
        reasonHeaderList = DBHelper.getInstance().getReasonHeaders(Constants.REASON_TYPE_INV_DECLINE);
        setReasonList(dialogView, reasonHeaderList);
        ImageView dialogClose = dialogView.findViewById(R.id.xCustomerDeclineMenuClose);
        Button continueWithSignature = dialogView.findViewById(R.id.continueSignature);
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                reasonMenuDialog.dismiss();
            }
        });
        continueWithSignature.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RadioGroup radioGroup = (RadioGroup) dialogView.findViewById(R.id.radio_group);
                int checkedRadioButtonId = radioGroup.getCheckedRadioButtonId();
                if (checkedRadioButtonId == -1) {
                    SnackBarToast.showMessage(dialogView, getResources().getString(R.string.decline_reason));
                } else {
                    savedata(reasonHeaderList.get(checkedRadioButtonId).getRSN_CODE());
                    reasonMenuDialog.dismiss();
                }
            }
        });
        reasonMenuDialog.show();
    }

    public void setReasonList(View dialogView, List<REASON_HEADER> reasonHeaderList) {
        RadioGroup rgp = (RadioGroup) dialogView.findViewById(R.id.radio_group);
        if (reasonHeaderList != null && reasonHeaderList.size() > 0) {
            int buttons = reasonHeaderList.size();
            AppCompatRadioButton[] rb = new AppCompatRadioButton[buttons];
            for (int i = 0; i <= buttons - 1; i++) {
                RadioButton rbn = new RadioButton(this);
                rbn.setId(i);
                rbn.setText(reasonHeaderList.get(i).getRSN_DESC());
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(RadioGroup.LayoutParams.MATCH_PARENT, RadioGroup.LayoutParams.WRAP_CONTENT, 1f);
                params.setMargins(5, 23, 5, 23);
                rbn.setLayoutParams(params);
                rbn.setTextSize(20);
                rgp.addView(rbn);
            }
        }
    }

    private void savedata(String p) {

        List<DELIVERY> deliveryList = new ArrayList<>();
        List<INVOICE> invoiceList = new ArrayList<>();
        {
            try {
                IDataStructure[] deliveryStructures = ApplicationManager.getInstance().getDataManager().get(DELIVERY.TABLE_NAME, DELIVERY.FIELD_VISIT_NO + " = '" + TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO() + "'");
                for (IDataStructure deliveryStructure : deliveryStructures) {
                    deliveryList.add((DELIVERY) deliveryStructure);
                }
                if (deliveryStructures != null && deliveryStructures.length > 0) {
                    IDataStructure[] invoiceStructures = ApplicationManager.getInstance().getDataManager().get(INVOICE.TABLE_NAME);
                    if (invoiceStructures != null && invoiceStructures.length > 0) {
                        for (IDataStructure invoiceStructure : invoiceStructures) {
                            invoiceList.add((INVOICE) invoiceStructure);
                        }
                    }
                    INVOICE invoiceRow = new INVOICE();
                    for (DELIVERY delivery : deliveryList) {
                        for (INVOICE invoice : invoiceList) {
                            if (invoice.getDELV_NO().equals(delivery.getDELV_NO())) {
                                invoiceRow = invoice;
                                invoiceRow.setDECLN_RSN(p);
                            }
                        }
                    }
                    ApplicationManager.getInstance().getDataManager().update(invoiceRow);
                }
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
    }

    public void closeActivity(View view) {
        finish();
    }


    @Override
    public void onPrintSuccess(final Enum<Constants.DocType> type, Constants.CopyType copyType, boolean isLast, String filePath, boolean reprint) {
        //save in storage for testing purpose
        File file = new File(filePath);
        if (!PrinterUtils.printFlag) {
            try {
                InvoiceReport.exportFile(file, file.getName(), this);
            } catch (IOException e) {
                Logger.e("", e);
            }
        }
        if (type == Constants.DocType.Invoice && copyType == Constants.CopyType.CUSTOMER) {
            String timeType = Constants.TIME_TYPE_CUSTOMER_PRINT_INVOICE;
            if (type == Constants.DocType.InputChecklist)
                timeType = Constants.TIME_TYPE_CUSTOMER_PRINT_INPUT_CHECKLIST;
            VISIT currentVisit = TripSummaryController.getInstance().getCurrentVisitRow();
            if (currentVisit != null) {
                TimeController.createNewTime(currentVisit.getVISIT_NO().toString(), timeType);
            }
            ATTACHMENT attachmentRow = AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_INVOICE, delivery.getINV_NO(), visitRow.getCUST_NO(), Constants.PDF_TYPE);
            AttachmentHelper.getInstance().saveInvoiceAsAttachment(attachmentRow, delivery.getINV_NO(), visitRow.getCUST_NO(), file, delivery);
            DialogMessage dialogMessage = new DialogMessage(this);
            dialogMessage.showDialogWithPositive("", "Click OK to Print SUPPLIER Copy", "OK", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    try {
                        invoiceReport.generateInvoice();
                        invoiceReport.generateInvoicePdf(delivery, visitRow, type, false, DriverSignatureActivity.this, DriverSignatureActivity.this, Constants.CopyType.SUPPLIER);

                    } catch (DocumentException | InterruptedException | IOException |
                             DBException e) {
                        Logger.e("", e);
                    } catch (ParseException e) {
                        Logger.e("", e);
                    } catch (Exception e) {
                        Logger.e("", e);
                    }
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {

                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }
        if (type == Constants.DocType.Invoice && (copyType == Constants.CopyType.SUPPLIER)) {
            boolean isPODClaimScanEnabled = false;

            try {
                isPODClaimScanEnabled = DeliveryController.getInstance().isPODClaimScanEnabled();
            } catch (DBException e) {
                Logger.e("", e);
            }
            if (Constants.NAT_CUSTOMER.equals(TripSummaryController.getInstance().NAT_CUSTOMER_TYPE)) {
                checkNATAndPOD = true;
            } else {
                checkNATAndPOD = false; // navigateToScanCustomerPodClaim();
            }
            List<String> keyList = new ArrayList<>(InvoiceController.DeliveryItemPricingDictionary.keySet());
            List<String> completedDelvNoList = InvoiceController.getInstance().getCompletedInvoicing(keyList);
            if (InvoiceController.DeliveryItemPricingDictionary.size() >= completedDelvNoList.size()) {
                DialogMessage dialogMessage = new DialogMessage(this);
                String message = "Click OK to process the next Invoice";
                if (InvoiceController.DeliveryItemPricingDictionary.size() == completedDelvNoList.size()) {
                    // message = "Click OK to proceed";
                    navigateToEndOfVisitMenuForm();
                }
                dialogMessage.showDialogWithPositive("", message, "OK", new DialogClickListner() {
                    @Override
                    public void onPositiveClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                        finish();
                    }

                    @Override
                    public void onNegativeClick(Dialog dialogMessage) {

                    }

                    @Override
                    public void onNeutralClick(Dialog dialogMessage) {

                    }
                });
            } else {
                navigateToEndOfVisitMenuForm();
            }
        }
        //delete pdf
        Utils.deletePdfFiles(filePath);
    }

    @Override
    public void onPrintError(Exception e, Enum<Constants.DocType> type) {
    }
}