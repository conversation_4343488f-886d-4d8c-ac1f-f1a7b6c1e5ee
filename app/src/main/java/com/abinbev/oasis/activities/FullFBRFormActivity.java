package com.abinbev.oasis.activities;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.ReasonDropDownAdapter;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.ReasonController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class FullFBRFormActivity extends AppCompatActivity {
    private List<DELIVERY> deliveryList;
    private List<REASON_HEADER> reason_headerList;
    private Spinner mDeliverySpinner;
    private Spinner mReasonSpinner;
    private DELIVERY selectedDelivery=null;
    private REASON_HEADER selectedReason = null;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_full_f_b_r_form);
        iniViews();
        getData();
    }

    @Override
    protected void onResume() {
        super.onResume();

    }

    private void iniViews() {
        mDeliverySpinner=findViewById(R.id.xFullFbrDeliverySpinner);
        mReasonSpinner=findViewById(R.id.xFullFbrReasonSpinner);
    }

    private void getData() {
        deliveryList=new ArrayList<>();
        reason_headerList = new ArrayList<>();
        Loader.showLoader(this,"Please wait");
        deliveryList= DeliveryController.getInstance().getOpenFullFBRDeliveryHeaders(Math.round(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO()));
        try {
            DELIVERY placeHolder = new DELIVERY();
            placeHolder.setDELV_NO("");
        } catch (DBException e) {
            Logger.e("", e);
        }
        reason_headerList= ReasonController.getInstance().getReasons(Constants.REASON_TYPE_DELIVERY);
        Loader.hideLoader();
        if(deliveryList==null || deliveryList.size()<=0 || reason_headerList==null || reason_headerList.size()<=0){
            Toast.makeText(this, "Delivery items not available", Toast.LENGTH_SHORT).show();
            finish();
        }
        setupDropDown();
    }

    private void setupDropDown() {

        final DeliveryDropDownAdapter deliveryDropDownAdapter = new DeliveryDropDownAdapter(this,android.R.layout.simple_spinner_dropdown_item,deliveryList,true);
        mDeliverySpinner.setAdapter(deliveryDropDownAdapter);
        ReasonDropDownAdapter reasonDropDownAdapter = new ReasonDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item,reason_headerList,true);
        mReasonSpinner.setAdapter(reasonDropDownAdapter);
        mDeliverySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if(position!=0){
                    deliveryDropDownAdapter.setSelectionPosition(position);
                    selectedDelivery = deliveryList.get(position-1);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        mReasonSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if(position!=0){
                    reasonDropDownAdapter.setSelectionPosition(position);
                    selectedReason = reason_headerList.get(position-1);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(FullFBRFormActivity.this);
    }

    public void closeActivity(View view) { finish(); }

    public void validate(View view) {
        if(selectedDelivery==null || mDeliverySpinner.getSelectedItemPosition() == 0){
            SnackBarToast.showMessage(view,getString(R.string.choose_delivery_validation));
        }else if(selectedReason==null || mReasonSpinner.getSelectedItemPosition() == 0){
            SnackBarToast.showMessage(view,getString(R.string.choose_reason_validation));
        }else {
            showConfirmationDialog();
        }
    }

    private void showConfirmationDialog() {
        DialogMessage dialogMessage =new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Continue with Full FBR", "OK", "Cancel", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                selectedDelivery.setFULL_FBR_RSN(selectedReason.getRSN_CODE());
                selectedDelivery.setIS_FBR("X");
                DeliveryController.getInstance().updateDelivery(selectedDelivery);
                DeliveryController.getInstance().deleteHHCreatedDeliveryItems(selectedDelivery.getDELV_NO());
                DeliveryController.getInstance().updateDeliveryItemsForFullFBR(selectedDelivery.getDELV_NO(),selectedReason.getRSN_CODE());
                InvoiceController.DeliveryItemPricingDictionary=null;
                deliveryList= DeliveryController.getInstance().getOpenFullFBRDeliveryHeaders(Math.round(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO()));
                if (deliveryList == null || deliveryList.size() <= 1)
                {
                    Logger.i("User in FullFBRFormActivity. VISIT_NO: "+TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());
                    finish();
                } else {
                    getData();
                }
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }

    public void validateAndClose(View view) {
        if(selectedDelivery==null || mDeliverySpinner.getSelectedItemPosition() == 0){
            finish();
        }else {
             if(selectedReason==null || mReasonSpinner.getSelectedItemPosition() == 0){
                SnackBarToast.showMessage(view,getString(R.string.choose_reason_validation));
            }else {
                showConfirmationDialog();
            }
        }
    }
}