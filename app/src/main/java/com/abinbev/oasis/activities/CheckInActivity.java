package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.abinbev.oasis.adapter.DepotDropDownAdapter;
import com.abinbev.oasis.adapter.PalletCheckerDropDownAdapter;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.Controllers.TripInspController;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.google.android.material.textfield.TextInputEditText;
import com.abinbev.oasis.R;

import java.util.List;

public class CheckInActivity extends AppCompatActivity {
    private TextView mShipmentNo,mDriverName,mDepotName;
    private RelativeLayout mFbrBit,mPallets;
    private SHIPMENT_HEADER currentShipment;
    private DEPOT_HEADER selectedDepot=null,currentDepot=null;
    List<DEPOT_HEADER> depotHeaderList;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_in);
        iniViews();
        getData();
    }

    @Override
    public void onBackPressed() {

    }

    private void getData() {
        currentShipment= DBHelper.getInstance().getShipmentRow();
        if(currentShipment!=null){
            mShipmentNo.setText(currentShipment.getSHIP_NO());
            mDriverName.setText(currentShipment.getDRV1_NAME());
            setDepot();
        }
    }

    private void setDepot() {

        if(currentShipment.getDEPOT()==null || currentShipment.getDEPOT().isEmpty()){
            currentDepot = DepotController.getInstance().getSelectedDepotRow();
                if(currentDepot!=null){
                    mDepotName.setText(currentDepot.getDEPOT()+" - "+currentDepot.getNAME());
                }
        }else {
            currentDepot = DepotController.getInstance().getDepot(currentShipment.getDEPOT());
            if(currentDepot!=null){
                mDepotName.setText(currentDepot.getDEPOT()+" - "+currentDepot.getNAME());
            }
        }
    }

    private void iniViews() {
        mShipmentNo=findViewById(R.id.xCheckInShipment);
        mDriverName=findViewById(R.id.xCheckInDriverName);
        mDepotName=findViewById(R.id.xCheckInDepotName);
        mFbrBit=findViewById(R.id.xCheckInFBRBIT);
        mPallets=findViewById(R.id.xCheckInPallets);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(CheckInActivity.this);
    }


    public void changeDepot(View view) {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            final View dialogView = LayoutInflater.from(this).inflate(R.layout.pallet_checker_dialog,  null);
            builder.setView(dialogView);
            final AlertDialog alertDialog = builder.create();
            ImageView dialogClose = dialogView.findViewById(R.id.xPalletCheckerDialogClose);
            Button dialogProceed = dialogView.findViewById(R.id.xPalletCheckerDialogProceed);
            Spinner mDepotsSpinner = dialogView.findViewById(R.id.xPalletCheckerDialogSpinner);
            final TextInputEditText mPassword = dialogView.findViewById(R.id.xPalletCheckerDialogPassword);
            TextView mSpinnerLabel = dialogView.findViewById(R.id.xPalletCheckerSpinnerLabel);
            mSpinnerLabel.setText(R.string.change_depot);
            dialogProceed.setText("Change");
            depotHeaderList = DepotController.getInstance().getDepotList();
            final DepotDropDownAdapter depotDropDownAdapter = new DepotDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, depotHeaderList);
            mDepotsSpinner.setAdapter(depotDropDownAdapter);
            for (int i=0;i<depotHeaderList.size(); i++){
                if(depotHeaderList.get(i).getDEPOT().equals(currentDepot.getDEPOT())){
                    mDepotsSpinner.setSelection(i);
                }
            }
            mDepotsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    depotDropDownAdapter.setSelectionPosition(position);
                    selectedDepot=depotHeaderList.get(position);
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) { }
            });

            dialogProceed.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mPassword.getText().toString().isEmpty()){
                        SnackBarToast.showMessage(v,"Please enter password.");
                    }else {
                        String password = CustomizingController.getInstance().getStringKeyValue(Constants.KEY_DEPOT_PASSWORD);
                        password = password == null ? Constants.DEPOT_PASSWORD_DEFAULT : password;
                        String enteredPasswordEncrypted = Utils.getEncryptedText(mPassword.getText().toString());
                        if(!enteredPasswordEncrypted.equalsIgnoreCase(password)){
                            SnackBarToast.showMessage(v,"Password you entered is invalid ");
                        }else {
                            saveData();
                            alertDialog.dismiss();
                            setDepot();
                        }
                    }
                }
            });
            dialogClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    alertDialog.dismiss();
                }
            });
            alertDialog.show();
    }

    private void change_depot_dialog_button_click(View v, String enteredPassword, AlertDialog alertDialog) {

    }

    private void saveData() {
            if(depotHeaderList != null && currentShipment != null){
                currentShipment.setDEPOT(selectedDepot.getDEPOT());
                DBHelper.getInstance().updateShipmentStatus(currentShipment);
            }
    }


    public void checkInFbrBit(View view) {
        startActivity(new Intent(CheckInActivity.this,CheckInFullsActivity.class));
    }


    public void checkInEmpty(View view) {
        startActivity(new Intent(CheckInActivity.this,CheckInEmptiesActivity.class));
    }
}