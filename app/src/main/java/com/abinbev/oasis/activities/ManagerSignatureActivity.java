package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.DrawingView;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.oasis.R;

public class ManagerSignatureActivity extends AppCompatActivity {

    Constants.SCREEN_NAVIGATION_MODE currentScreensNavigationMode;
    String authManagerName, authManagerId = "";
    TextView managerCodeAndName;
    RelativeLayout mDrawingView;
    DrawingView drawingView;
    DialogMessage dialogMessage;
    Context activity = this;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_manager_signature);
        currentScreensNavigationMode = (Constants.SCREEN_NAVIGATION_MODE) getIntent().getSerializableExtra(Constants.SCREEN_NAVIGATION);
        if(currentScreensNavigationMode == null) {
            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;
        }
        mDrawingView=findViewById(R.id.xManagerSignatureDrawingView);
        drawingView = new DrawingView(this);
        mDrawingView.addView(drawingView);
        managerCodeAndName = (TextView) findViewById(R.id.xManagerCodeAndName);
        authManagerId = DataHelper.getInstance().getAuthManagerID();
        authManagerName = DataHelper.getInstance().getAuthManagerName();
        managerCodeAndName.setText(authManagerId + " - " + authManagerName);
        this.dialogMessage = new DialogMessage(this);
    }

    public void resetDrawingView(View view) {
        Animation rotateClk = AnimationUtils.loadAnimation(getApplicationContext(),R.anim.clockwise_rotation);
        view.startAnimation(rotateClk);
        drawingView.clear();
    }

    public void validateManagerSignature(View view) {
        if(!drawingView.isPathEmpty())
        {

            dialogMessage.showDialogWithPositiveAndNegativeBtn("", getString(R.string.continue_conformation), getString(R.string.yes), getString(R.string.no), new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    Bitmap sign = drawingView.getBitmap();
                    if(currentScreensNavigationMode == Constants.SCREEN_NAVIGATION_MODE.PreForkLiftInspection) {
                        DataHelper.getInstance().storeSignature(sign, Constants.ATTACH_TYPE_PRETRIP_MGR, authManagerId);
                    }
                    else {
                        DataHelper.getInstance().storeSignature(sign, Constants.ATTACH_TYPE_POSTTRIP_MGR, authManagerId);
                    }
                    Intent intent = new Intent(activity, DriverSignatureActivity.class);
                    intent.putExtra(Constants.SCREEN_NAVIGATION, currentScreensNavigationMode);
                    startActivity(intent);
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {

                }
            });
        }
        else
        {
            SnackBarToast.showMessage(view, getResources().getString(R.string.signature_error));
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(ManagerSignatureActivity.this);
    }
}