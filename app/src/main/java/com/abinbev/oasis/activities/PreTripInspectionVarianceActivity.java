package com.abinbev.oasis.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatRadioButton;

import com.abinbev.oasis.adapter.UserNameAdapter;
import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.be.TRIP_INSP;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.abinbev.oasis.R;

import java.util.List;

public class PreTripInspectionVarianceActivity extends AppCompatActivity {

    public List<REASON_HEADER> reasonHeaderList;
    public List<AUTH_HEADER> authHeaderList;
    public AUTH_HEADER selectedAuthHeader = null;
    public TRIP_INSP selectedTripInspHeader = null;
    public EditText password;
    Constants.SCREEN_NAVIGATION_MODE currentScreensNavigationMode;
    Spinner mSpinnerUsernames;
    public String title;
    public TextView titleText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pre_trip_inspection_variance);
        titleText = (TextView)findViewById(R.id.titleTextVariance);
        currentScreensNavigationMode = (Constants.SCREEN_NAVIGATION_MODE) getIntent().getSerializableExtra(Constants.SCREEN_NAVIGATION);
        selectedTripInspHeader = (TRIP_INSP) DataHelper.getInstance().getTripInspectionToUpdate();
        if(currentScreensNavigationMode == null) {
            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;
        }
        doInspectionTypeSettings();

        reasonHeaderList = DBHelper.getInstance().getReasonHeaders(Constants.REASON_TYPE_PRETRIP);
        authHeaderList = DBHelper.getInstance().getAuthHeaderList(Constants.AUTH_TYPE_MANAGER);
        RadioGroup rgp = (RadioGroup) findViewById(R.id.radio_group);

        if(reasonHeaderList != null && reasonHeaderList.size() > 0) {
            int buttons = reasonHeaderList.size();
            AppCompatRadioButton[] rb = new AppCompatRadioButton[buttons];
            for (int i = 0; i <= buttons - 1; i++) {
                RadioButton rbn = new RadioButton(this);
                rbn.setId(i);
                rbn.setText(reasonHeaderList.get(i).getRSN_DESC());
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(RadioGroup.LayoutParams.MATCH_PARENT, RadioGroup.LayoutParams.WRAP_CONTENT, 1f);
                params.setMargins(5, 23, 5,23 );
                rbn.setLayoutParams(params);
                rbn.setTextSize(20);
                rgp.addView(rbn);
            }
        }
        if(authHeaderList != null && authHeaderList.size() > 0) {
            mSpinnerUsernames = (Spinner)findViewById(R.id.username_spinner);
            final ArrayAdapter<AUTH_HEADER> adapter = new UserNameAdapter(this,
                    android.R.layout.simple_spinner_item, authHeaderList);
            adapter.setDropDownViewResource(R.layout.username_spinner_dropdown_item);
            mSpinnerUsernames.setAdapter(adapter);
            mSpinnerUsernames.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parentView, View selectedItemView, int position, long id) {
                    ((UserNameAdapter)parentView.getAdapter()).setSelectionPosition(position);
                    selectedAuthHeader = ((AUTH_HEADER)parentView.getItemAtPosition(position));
                }

                @Override
                public void onNothingSelected(AdapterView<?> parentView) {

                }
            });

        }


    }

    public void doInspectionTypeSettings()
    {
        switch (currentScreensNavigationMode)
        {
            case PretripInspection:
                title = "Pre Trip Inspection Variance";
                break;

            case PostForkLiftInspection:
            case PreForkLiftInspection:
                title = "ForkLift Inspection Variance";
                break;

            case PostTripInspection:
                title = "Post Trip Inspection Variance";
                break;
        }
        titleText.setText(title);

    }

    public void continueAuth(View view) {
        if(validate(view)) {
            finish();
        }

    }

    private boolean validate(View v)
    {
        String reasoncode = "";
        boolean actionTaken = false;
        RadioGroup radioGroup = (RadioGroup)findViewById(R.id.radio_group);
        password = (EditText)findViewById(R.id.authPassword);
        String passwordEntered = password.getText().toString();
        int checkedRadioButtonId = radioGroup.getCheckedRadioButtonId();
        if(checkedRadioButtonId == -1) {
            SnackBarToast.showMessage(v, getResources().getString(R.string.select_action_error));
            return false;
        }
        if(selectedAuthHeader == null) {
            SnackBarToast.showMessage(v, getResources().getString(R.string.select_supervisor_error));
            return false;
        }
        if(passwordEntered.isEmpty()) {
            SnackBarToast.showMessage(v, getResources().getString(R.string.password_error));
            password.requestFocus();
            return false;
        }
        if (!passwordEntered.equalsIgnoreCase(Utils.GetPassword(selectedAuthHeader.getUSER_ID())))
        {
            SnackBarToast.showMessage(v, getResources().getString(R.string.password_error));
            password.setText("");
            password.requestFocus();
            return false;
        }
        REASON_HEADER selectedReason = reasonHeaderList.get(checkedRadioButtonId);
        selectedTripInspHeader.setREASON(Long.parseLong(selectedReason.getRSN_CODE()));
        selectedTripInspHeader.setAUTH_BY(selectedAuthHeader.getUSER_ID());
        DBHelper.getInstance().updateTripInspHeader(selectedTripInspHeader);
        DataHelper.getInstance().setAuthDetails(selectedAuthHeader.getUSER_ID(), selectedAuthHeader.getNAME());
        return true;
    }


    @Override
    public void onBackPressed() {
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(PreTripInspectionVarianceActivity.this);
    }
}