package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatRadioButton;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripInspController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class CustomerRatingActivity extends AppCompatActivity {

    public List<REASON_HEADER> reasonHeaderList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_customer_rating);
        reasonHeaderList = DBHelper.getInstance().getReasonHeaders(Constants.REASON_TYPE_DRV_RATING);
        setReasonList();
    }

    public void setReasonList() {
        RadioGroup rgp = (RadioGroup) findViewById(R.id.radio_group);
        if(reasonHeaderList != null && reasonHeaderList.size() > 0) {
            int buttons = reasonHeaderList.size();
            AppCompatRadioButton[] rb = new AppCompatRadioButton[buttons];
            for (int i = 0; i <= buttons - 1; i++) {
                RadioButton rbn = new RadioButton(this);
                rbn.setId(i);
                rbn.setText(reasonHeaderList.get(i).getRSN_DESC());
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(RadioGroup.LayoutParams.MATCH_PARENT, RadioGroup.LayoutParams.WRAP_CONTENT, 1f);
                params.setMargins(5, 23, 5,23 );
                rbn.setLayoutParams(params);
                rbn.setTextSize(20);
                rgp.addView(rbn);
            }
        }
    }

    public void validateCustomerRating(View view) {
        RadioGroup radioGroup = (RadioGroup)findViewById(R.id.radio_group);
        int checkedRadioButtonId = radioGroup.getCheckedRadioButtonId();
        if( checkedRadioButtonId == -1 ) {
            SnackBarToast.showMessage(view, getResources().getString(R.string.customer_rating_error));
            return;
        } else {
            OasisCache.setCustomerRating(reasonHeaderList.get(checkedRadioButtonId).getRSN_CODE());
            CustomizingController.getInstance().addOrUpdateCustomizingRating(Constants.KEY_CUSTOMER_RATING, OasisCache.getCustomerRating());
            InvoiceController.getInstance().saveCustomerRating((TripSummaryController.getInstance().getCurrentVisitRow()).getCUST_NO(), DeliveryController.getInstance().getCurrentDeliveryNo(), reasonHeaderList.get(checkedRadioButtonId).getRSN_CODE());
            radioGroup.clearCheck();
            navigateToCustomerSignatureFrom();
        }
    }

    public void navigateToCustomerSignatureFrom() {
        startActivity(new Intent(CustomerRatingActivity.this, CustomerSignatureActivity.class));
        finish();
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(CustomerRatingActivity.this);
    }
}