package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;


import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.abinbev.oasis.be.REASON_HEADER;
import com.abinbev.oasis.util.Constants;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.RadioGroup;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.CustomizingController;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.DrawingView;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.ScreenHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;

import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class CustomerSignatureActivity extends AppCompatActivity {

    Constants.SCREEN_NAVIGATION_MODE currentScreensNavigationMode;
    RelativeLayout mDrawingView;
    TextView customerCode, orderNo;
    DrawingView drawingView;
    String currentDeliveryNo;
    Context activity = this;
    RatingBar ratingbar;
    List<REASON_HEADER> reasonHeaderList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_customer_signature);
        reasonHeaderList = DBHelper.getInstance().getReasonHeaders(Constants.REASON_TYPE_DRV_RATING);
        currentScreensNavigationMode = ScreenHelper.getCurrentScreensNavigationMode();
        if(currentScreensNavigationMode == null) {
            currentScreensNavigationMode = Constants.SCREEN_NAVIGATION_MODE.PretripInspection;
        }


        mDrawingView=findViewById(R.id.xCustomerSignatureDrawingView);
        drawingView = new DrawingView(this);
        mDrawingView.addView(drawingView);
        customerCode = (TextView) findViewById(R.id.xCustomerCode);
        orderNo = (TextView) findViewById(R.id.xSapOrderCode);
        currentDeliveryNo = DeliveryController.getInstance().getCurrentDeliveryNo();
        customerCode.setText("Delivery: - " + currentDeliveryNo);
        orderNo.setText("SAP Order: - " + DeliveryController.getInstance().getDeliveryByNo(currentDeliveryNo).getORD_NO());
        ratingbar=(RatingBar)findViewById(R.id.ratingBar);
    }

    public void resetDrawingView(View view) {
        Animation rotateClk = AnimationUtils.loadAnimation(getApplicationContext(),R.anim.clockwise_rotation);
        view.startAnimation(rotateClk);
        drawingView.clear();
    }

    public void validateCustomerSignature(View view) {
        if(!drawingView.isPathEmpty())
        {

//            if (currentScreensNavigationMode == Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration)
//            {
//                int scale = ImageUtil.GetImageScaleFactor();
//                inkBoxCustomerSignature.ImageSizeCustom = true;
//                inkBoxCustomerSignature.ImageSize = new Size(scale * 270, scale * 235);
//            }
            Bitmap sign = drawingView.getBitmap();
            DataHelper.getInstance().storeCustomerSignature(sign);
            validateCustomerRating(view);
//            navigateToDriverScreen();
        }
        else
        {
            SnackBarToast.showMessage(view, getResources().getString(R.string.signature_error));
        }



    }

    public void validateCustomerRating(View view) {
        int customerRatingIndex = (int)(ratingbar.getRating()) - 1;
        if( customerRatingIndex < 0 ) {
            SnackBarToast.showMessage(view, getResources().getString(R.string.customer_rating_error));
            return;
        } else {
            OasisCache.setCustomerRating(reasonHeaderList.get(customerRatingIndex).getRSN_CODE());
            CustomizingController.getInstance().addOrUpdateCustomizingRating(Constants.KEY_CUSTOMER_RATING, OasisCache.getCustomerRating());
            InvoiceController.getInstance().saveCustomerRating((TripSummaryController.getInstance().getCurrentVisitRow()).getCUST_NO(), DeliveryController.getInstance().getCurrentDeliveryNo(), reasonHeaderList.get(customerRatingIndex).getRSN_CODE());
            ratingbar.setRating((float) 0.0);
            navigateToDriverScreen();
        }
    }

    public void navigateToDriverScreen() {
        VISIT visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
        visitRow.setSTAT((long) Constants.VISIT_STATUS_CUSTOMER_SIGN_CAPTURED);
        TripSummaryController.getInstance().insertOrUpdateVisit(visitRow);


        Intent intent = new Intent(activity, DriverSignatureActivity.class);
        intent.putExtra(Constants.SCREEN_NAVIGATION, Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration);
        startActivity(intent);
        finish();

        ScreenHelper.setFromCustomerSignature(true);
        ScreenHelper.setCurrentScreensNavigationMode(Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(CustomerSignatureActivity.this);
    }

    public void validateDriverSignature(View view) {
        Intent intent = new Intent(CustomerSignatureActivity.this, DriverSignatureActivity.class);
        intent.putExtra(Constants.SCREEN_NAVIGATION, Constants.SCREEN_NAVIGATION_MODE.InvoiceGeneration);
        startActivity(intent);
        finish();
    }
}