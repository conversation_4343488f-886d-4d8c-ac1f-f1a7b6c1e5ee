package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.AppNotificationListener;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PAHelper;
import com.abinbev.oasis.util.Utils;

import com.unvired.logger.Logger;
import com.unvired.model.AttachmentItem;
import com.unvired.model.InfoMessage;
import com.unvired.model.OutObject;
import com.unvired.oasis.R;
import com.unvired.sync.notifier.NotificationListener;


import java.util.Vector;

public class UploadMenuActivity extends AppCompatActivity implements NotificationListener {


    TextView mShipmentLabel,mDriverLabel;
    boolean uploadStarted;
    SHIPMENT_HEADER selectedShipmentHeader;
    private static boolean UPLOAD_COMPLETE = false;
    private static InfoMessage UPLOAD_INFO_MSG = null;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_upload_menu);
        uploadStarted = false;
        iniViews();
        getData();
        AppNotificationListener.getInstance().addNotificationListener(this);
    }

    private void getData() {
        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getShipmentRow();
        selectedShipmentHeader= shipment_header;
        mShipmentLabel.setText(shipment_header.getSHIP_NO());
        mDriverLabel.setText(shipment_header.getDRV1()+" - "+shipment_header.getDRV1_NAME());
    }

    @Override
    public void onBackPressed() {
        
    }

    private void iniViews() {
        mShipmentLabel=findViewById(R.id.xUploadMenuShipment);
        mDriverLabel=findViewById(R.id.xUploadMenuDriverName);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(UploadMenuActivity.this);
    }

    public void upload_to_sap_btn_click(View view) {
        final String shipNo = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
        DialogMessage dialogMessage = new DialogMessage(this);
        dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Do you want to continue", "Upload " + shipNo + " to SAP", "Do not Upload", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                Loader.showLoader(UploadMenuActivity.this,"Uploading Shipment :"+shipNo+" ...");
                Logger.i("User clicked Upload to SAP Button. Shipment No: " + shipNo);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        PAHelper.uploadShipment(UploadMenuActivity.this);
                        uploadStarted = true;
                    }
                }).start();
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        AppNotificationListener.getInstance().removeNotificationListener(this);
    }

    @Override
    public void notifyDataChange(Vector vector) {
        com.unvired.logger.Logger.log(Logger.LEVEL_ERROR, this.getLocalClassName(), "PA_UPLOAD_SHIPMENT : Exception ", "Data Changed");
//        runOnUiThread(() -> {
//            Loader.hideLoader();
//            navigateToStartUpActivity();
//        });

    }

    private void navigateToStartUpActivity() {
        if(uploadStarted) {
            DialogMessage dialogMessage = new DialogMessage(this);
            Logger.i("Shipment: " + mShipmentLabel.getText().toString() + " Uploaded successfully.");
            dialogMessage.showDialogWithPositive("", "Shipment: " + mShipmentLabel.getText().toString() + " Uploaded successfully.\n\nThe shipment shall be reset on click of OK", "OK", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    Intent intent = new Intent(UploadMenuActivity.this, StartUpActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    startActivity(intent);
                    uploadStarted = false;
                    finish();
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }
            });
        }
    }

    private void showException() {
        if(uploadStarted && UPLOAD_INFO_MSG != null && !UPLOAD_INFO_MSG.getMessage().isEmpty()) {
            DialogMessage dialogMessage = new DialogMessage(this);
            Logger.i("Shipment UPLOAD Exception: " + UPLOAD_INFO_MSG.getMessage());
            dialogMessage.showDialogWithPositive("", "Exception: " + UPLOAD_INFO_MSG.getMessage(), "OK", new DialogClickListner() {
                @Override
                public void onPositiveClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                    uploadStarted = false;
                }

                @Override
                public void onNegativeClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }

                @Override
                public void onNeutralClick(Dialog dialogMessage) {
                    dialogMessage.dismiss();
                }
            });
        }
    }

    @Override
    public void notifyDataReceiveCompletion() {
        com.unvired.logger.Logger.log(Logger.LEVEL_ERROR, this.getLocalClassName(), "PA_UPLOAD_SHIPMENT : Exception ", "Data Recived");
        // Handleing times the DataSent callback not received in time.
        if(uploadStarted && !UPLOAD_COMPLETE) {
            runOnUiThread(() -> {
                Loader.hideLoader();
                navigateToStartUpActivity();
            });
        }
    }

    @Override
    public void notifyDataSend(OutObject outObject) {
        if(uploadStarted) {
            if (outObject != null && selectedShipmentHeader != null)
            {
                if (outObject.getBELid().equals(selectedShipmentHeader.getLid()))
                {
                    UPLOAD_COMPLETE = true;
                    runOnUiThread(() -> {
                        Loader.hideLoader();
                        navigateToStartUpActivity();
                    });
                }
            }
        }
    }

    @Override
    public void notifyException(Exception e) { }

    @Override
    public void notifyServerMessages(Vector vector) { }

    @Override
    public void notifyDemoMode(String s) { }

    @Override
    public void notifyAttachmentDownloadSuccess(AttachmentItem attachmentItem) { }

    @Override
    public void notifyAttachmentDownloadFailure(AttachmentItem attachmentItem, String s) { }

    @Override
    public void notifyApplicationReset() { }

    @Override
    public void notifyOutBoxItemDiscarded(InfoMessage infoMessage) {
        if(uploadStarted) {
            if (infoMessage.getBeLid() != null && infoMessage.getBeLid().equals(selectedShipmentHeader.getLid()) && infoMessage.getCategory() == InfoMessage.CATEGORY_FAILURE) {
                UPLOAD_INFO_MSG = infoMessage;
                UPLOAD_COMPLETE = true;
                runOnUiThread(() -> {
                    Loader.hideLoader();
                    showException();
                });
            }
        }
    }

    @Override
    public void notifyAttachmentOutBoxItemDiscarded(InfoMessage infoMessage) { }

    @Override
    public void notifyDataSendAbort() { }

    @Override
    public void notifyAttachmentSendAbort() { }

    @Override
    public void notifyDataRetreiverAbort() { }

    @Override
    public void notifyIncomingDataProcessingFinished() {

    }

    @Override
    public void notifyJWTTokenUpdated(String s) {

    }


}