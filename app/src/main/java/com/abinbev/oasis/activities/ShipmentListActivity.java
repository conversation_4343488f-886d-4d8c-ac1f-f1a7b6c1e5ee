package com.abinbev.oasis.activities;

import android.content.DialogInterface;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.abinbev.oasis.adapter.ShipmentListAdapter;
import com.abinbev.oasis.be.INPUT_GET_SHIPMENTS_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PAHelper;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;
import com.unvired.model.InfoMessage;
import com.abinbev.oasis.R;
import com.unvired.sync.out.ISyncAppCallback;
import com.unvired.sync.response.ISyncResponse;
import com.unvired.sync.response.SyncBEResponse;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;
import java.util.Vector;

public class ShipmentListActivity extends AppCompatActivity implements View.OnClickListener {

    private ShipmentListActivity context = this;
    private RecyclerView recyclerView;
    private ShipmentListAdapter adapter;
    private TextView shipmentListTitle;
    private String responseCode;
    private String responseText;
    private List<SHIPMENT_HEADER> shipmentHeaderList = null;
    private INPUT_GET_SHIPMENTS_HEADER header;
    private ImageView mRefreshData;
    public String depot;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        depot = getIntent().getStringExtra("DEPOT");
        setContentView(R.layout.activity_shipment_list);
        recyclerView = (RecyclerView) findViewById(R.id.shipmentRecyclerView);
        mRefreshData=findViewById(R.id.xRefresh);
        mRefreshData.setOnClickListener(this);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setItemAnimator(new DefaultItemAnimator());
        recyclerView.setHasFixedSize(true);
        shipmentListTitle = findViewById(R.id.xShipmentListTitle);
        downloadShipmentList();

    }

    public void downloadShipmentList( ) {
        try {
            header = new INPUT_GET_SHIPMENTS_HEADER();
            header.setDEPOT(depot);
        } catch (DBException e) {
            Logger.e("", e);
        }
        Loader.showLoader(context, getString(R.string.downloading_shipments_list));
        downloadShipments(header);
    }

    private void downloadShipments(final INPUT_GET_SHIPMENTS_HEADER header) {

        shipmentHeaderList = new ArrayList<>();

        final ISyncAppCallback callback = new ISyncAppCallback() {
            @Override
            public void onResponse(ISyncResponse iSyncResponse) {

//                Loader.showLoader(context, getString(R.string.downloading_shipments_list));

                SyncBEResponse syncBEResponse;
                responseText = null;

                if (iSyncResponse == null) {
                    responseCode = Constants.RESPONSE_CODE_ERROR;
                    responseText = getResources().getString(R.string.invalidResponse);
                } else {

                    switch (iSyncResponse.getResponseStatus()) {
                        case SUCCESS:

                            if (iSyncResponse instanceof SyncBEResponse) {

                                syncBEResponse = (SyncBEResponse) iSyncResponse;

                                responseCode = Constants.RESPONSE_CODE_SUCCESSFUL;
                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        responseCode = infoMessages.get(i).getCategory().equals(InfoMessage.CATEGORY_SUCCESS) ? Constants.RESPONSE_CODE_SUCCESSFUL : Constants.RESPONSE_CODE_ERROR;

                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage()).append("\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();
                                }

                                if (responseText == null || responseText.trim().isEmpty()) {
                                    responseText = getResources().getString(R.string.DepotListDownloadSuccess);
                                }

                                if (responseCode.equals(Constants.RESPONSE_CODE_SUCCESSFUL)) {
                                    Hashtable<String, Hashtable<IDataStructure, Vector<IDataStructure>>> dataBEs = syncBEResponse.getDataBEs();
                                    Hashtable<IDataStructure, Vector<IDataStructure>> tempCollectionOfHeaderAndItems = null;

                                    if (!dataBEs.isEmpty()) {

                                        Enumeration<String> beKeys = dataBEs.keys();

                                        if (beKeys.hasMoreElements()) {
                                            String customerBEName = beKeys.nextElement();
                                            tempCollectionOfHeaderAndItems = dataBEs.get(customerBEName);

                                            Enumeration<IDataStructure> shipmentHeaderKeys = tempCollectionOfHeaderAndItems.keys();

                                            while (shipmentHeaderKeys.hasMoreElements()) {
                                                SHIPMENT_HEADER shipmentHeader = (SHIPMENT_HEADER) shipmentHeaderKeys.nextElement();
                                                shipmentHeaderList.add(shipmentHeader);
                                            }

                                        }
                                    }
                                }
                            }
                            break;

                        case FAILURE:
                            responseCode = Constants.RESPONSE_CODE_ERROR;

                            if (iSyncResponse instanceof SyncBEResponse) {
                                syncBEResponse = (SyncBEResponse) iSyncResponse;
                                responseText = syncBEResponse.getErrorMessage();

                                if (syncBEResponse.getErrorMessage().contains(getResources().getString(R.string.invalidResponse))) {
                                    responseText = getResources().getString(R.string.invalidResponse);
                                } else {
                                    responseText = syncBEResponse.getErrorMessage();
                                }

                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage()).append("\n");
                                        }
                                    }
                                    responseText = infoMsgs.toString();
                                }

                                if (responseText.trim().isEmpty())
                                    responseText = getResources().getString(R.string.invalidResponse);

                            } else {
                                responseText = getResources().getString(R.string.invalidResponse);
                            }
                            break;
                    }

                    if (responseCode != null && responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Loader.hideLoader();

                                showInfo(responseText);
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Loader.hideLoader();
                                loadShipmentsList();
                            }
                        });
                    }
                }
            }
        };
        new Thread(new Runnable() {
            @Override
            public void run() {
                PAHelper.getShipmentsList(header, callback);
            }
        }).start();
    }

    private void loadShipmentsList() {
        shipmentListTitle.setText(getString(R.string.shipment_list_button) + "(" + shipmentHeaderList.size() + ")");
        adapter = new ShipmentListAdapter(this, shipmentHeaderList, depot);
        recyclerView.setAdapter(adapter);
        adapter.notifyDataSetChanged();

    }

    private void showInfo(String msg) {
        if(Loader.isLoading()){
            Loader.hideLoader();
        }
        new AlertDialog.Builder(this)
                .setCancelable(false)
                .setMessage(msg)
                .setPositiveButton(getString(R.string.ok), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        loadShipmentsList();
                    }
                }).create().show();
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.xRefresh){
            Animation rotateClk = AnimationUtils.loadAnimation(getApplicationContext(),R.anim.clockwise_rotation);
            v.startAnimation(rotateClk);
            downloadShipmentList();
        }
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(ShipmentListActivity.this);
    }

    @Override
    public void onBackPressed() {
    }
}