package com.abinbev.oasis.activities;

import android.app.ProgressDialog;
import android.content.Intent;


import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.TextView;

import com.abinbev.oasis.adapter.DepoListAdapter;
import com.abinbev.oasis.be.DEPOT_HEADER;

import androidx.appcompat.app.AppCompatActivity;


import com.abinbev.oasis.util.Utils;
import com.unvired.oasis.R;

import java.util.List;

public class DepoSetupActivity extends AppCompatActivity {

    private ProgressDialog progressDialog;

    private RecyclerView recyclerView;
    private DepoListAdapter adapter;

    private String responseCode;
    private String responseText;
    TextView textView;
    private List<DEPOT_HEADER> depotHeaderList = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_depo_setup);
    }

    /** Called when the user touches the button */
    public void setupDepot(View view) {
        startActivity(new Intent(this, DepotListActivity.class));
        finish();

    }
    public void navigateToSettings(View view) {
        Utils.showSettingDialog(DepoSetupActivity.this);
    }


    public void navigateToDriverSignatureActivity(View view) {
        startActivity(new Intent(this, DriverSignatureActivity.class));

    }

    public void navigateToOdometerActivity(View view) {
        startActivity(new Intent(DepoSetupActivity.this,OdometerActivity.class));
    }

    public void navigateToQuestionsActivity(View view) {
        startActivity(new Intent(DepoSetupActivity.this,PreTripInspectionQuestionsActivity.class));
    }

    @Override
    public void onBackPressed() {
    }
}