package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;

import com.abinbev.oasis.adapter.DeliveryDropDownAdapter;
import com.abinbev.oasis.adapter.MaterialDroDownAdapter;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.MaterialController;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PalletsDropOffActivity extends AppCompatActivity {
    private Spinner mDeliverySpinner;
    private Spinner mProductSpinner;
    private EditText mPalletsCount;
    private ImageButton mPalletsClear;
    private DELIVERY selectedDelivery = null;
    private DELIVERY_ITEM delivery_item = null;
    private MATERIAL_HEADER selectedMaterial = null;
    private float palletStock = 0;
    private String shipmentLid = "";
    private List<DELIVERY> deliveryList;
    private List<DELIVERY_ITEM> deliveryItems = null;
    private List<MATERIAL_HEADER> materialHeaderList;
    private CUSTOMER_HEADER customer_header;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pallets_drop_off);
        iniViews();
        getData();
    }

    private void getData() {
        deliveryList = new ArrayList<>();
        deliveryList = DeliveryController.getInstance().getOpenDeliveryHeaders(Math.round(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO()), DeliveryController.Quantity.PALLETDROP);
        customer_header = TripSummaryController.getInstance().getCustomer(TripSummaryController.getInstance().getCurrentVisitRow().getCUST_NO());
        boolean flag = Utils.isAttributeEnabled(customer_header, Constants.AttributeType.SPLITBILLENABLED);
        if(flag){
            String emptyOrdDelvNo = DeliveryController.getInstance().getFirstReturnsCollectionOrderDeliveryNo();
            if (emptyOrdDelvNo != null && !emptyOrdDelvNo.isEmpty()) {
                for (int i = (deliveryList.size() - 1); i >= 0; i--) {
                    if (!deliveryList.get(i).getDELV_NO().equals(emptyOrdDelvNo)) {
                        deliveryList.remove(i);
                    }
                }
            }
        }


        palletStock = DBHelper.getInstance().getPalletInTruckByStock();
        setUpDeliverySpinner();
    }

    private void setUpDeliverySpinner() {
        if(deliveryList==null){
            deliveryList = new ArrayList<>();
        }
        final DeliveryDropDownAdapter deliveryDropDownAdapter = new DeliveryDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, deliveryList,true);
        mDeliverySpinner.setAdapter(deliveryDropDownAdapter);


        mDeliverySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    deliveryDropDownAdapter.setSelectionPosition(position);
                    if(position!=0){
                        selectedDelivery = deliveryList.get(position-1);
                        if (deliveryItems == null) {
                            deliveryItems = new ArrayList<>();
                        }
                        if (deliveryItems.size() > 0) {
                            deliveryItems.clear();
                        }
                        deliveryItems = DeliveryController.getInstance().getDeliveryItems(selectedDelivery.getDELV_NO(), DeliveryController.Quantity.PALLETDROP);
                        if(materialHeaderList.size()>0) {
                            mProductSpinner.setSelection(1);
                            updateSelectedValues(view);
                        }
                    }else {
                        mProductSpinner.setSelection(0);
                    }



            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });

        setUpMaterialSpinner();


        if(deliveryList.size()==1){
            mDeliverySpinner.setSelection(1);
        }
    }

    private void setUpMaterialSpinner(){
        materialHeaderList = new ArrayList<>();
        if(deliveryList.size()>0) {
            materialHeaderList = MaterialController.getInstance().getMaterials(DeliveryController.Quantity.PALLETDROP);
        }
        final MaterialDroDownAdapter materialDroDownAdapter = new MaterialDroDownAdapter(this, R.layout.support_simple_spinner_dropdown_item, materialHeaderList,true,true);
        mProductSpinner.setAdapter(materialDroDownAdapter);
        mProductSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                materialDroDownAdapter.setSelectionPosition(position);
                if(position!=0){
                    selectedMaterial = materialHeaderList.get(position-1);
                    updateSelectedValues(view);
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void updateSelectedValues(View view) {
        if (selectedMaterial != null) {
            boolean deliveryItemFound = false;
            if (deliveryItems != null && deliveryItems.size() > 0) {
                List<DELIVERY_ITEM> selectedDeliveries = new ArrayList<>();
                for (DELIVERY_ITEM delvItem : deliveryItems) {
                    if (delvItem.getMAT_NO() != null && !delvItem.getMAT_NO().isEmpty() && delvItem.getMAT_NO().equals(selectedMaterial.getMAT_NO())) {
                        selectedDeliveries.add(delvItem);
                    }
                }
                if (selectedDeliveries.size() > 0 && selectedDeliveries.get(0) != null) {
                    delivery_item = selectedDeliveries.get(0);
                    deliveryItemFound = true;
                }
            }
            if (!deliveryItemFound) {
                if (selectedDelivery == null) {
                    SnackBarToast.showMessage(view, "Please select a delivery");
                    return;
                }
                String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
                //String ship_no = "11312033";

                try {
                    delivery_item = new DELIVERY_ITEM();
                    delivery_item.setLid(delivery_item.getLid());
                    delivery_item.setSHIP_NO(ship_no);
                    delivery_item.setDELV_NO(selectedDelivery.getDELV_NO());
                    delivery_item.setMAT_NO(selectedMaterial.getMAT_NO());
                    delivery_item.setMAT_DESC(selectedMaterial.getMAT_DESC() == null ? "" : selectedMaterial.getMAT_DESC());
                    delivery_item.setITM_NO(DeliveryController.getInstance().getHighestItemNoOfDeliveryItems(selectedDelivery.getDELV_NO()));
                    delivery_item.setSLS_UOM(selectedMaterial.getBASE_UOM() == null ? "" : selectedMaterial.getBASE_UOM());
                    if (shipmentLid.isEmpty()) {
                        shipmentLid = String.valueOf(DBHelper.getInstance().getCurrentShipment().getLid());
                    }
                    delivery_item.setFid(shipmentLid);
                    delivery_item.setIS_EMPTY("X");
                    delivery_item.setHH_CREATED("X");

                } catch (DBException e) {
                    Logger.e("", e);
                }
            }
            mPalletsCount.setText(delivery_item.getQTY() == null || delivery_item.getQTY() < 1 ? "" : String.valueOf(delivery_item.getQTY().intValue()));
        }
    }

    private void iniViews() {
        mDeliverySpinner = findViewById(R.id.xPalletsDropOffDeliverySpinner);
        mProductSpinner = findViewById(R.id.xPalletsDropOffProductSpinner);
        mPalletsCount = findViewById(R.id.xPalletsDropOffReturnEntry);
        mPalletsClear = findViewById(R.id.xPalletsDropOffClearReturn);
    }

    public void navigateToSettings(View view) {
        Utils.showSettingDialog(PalletsDropOffActivity.this);
    }

    public void closeActivity(View view) {
        finish();
    }

    public void clearEditText(View view) {
        mPalletsCount.setText("");
    }


    public void nextButtonClick(View view) {
        if (valid(view)) {
            saveData();
            mProductSpinner.setSelection(0);
            mPalletsCount.setText("");
        }
    }

    private void saveData() {
        if(delivery_item==null){
            return;
        }
        if(deliveryItems==null){
            deliveryItems=new ArrayList<>();
        }

        if(!deliveryItems.contains(delivery_item)){
            deliveryItems.add(delivery_item);
        }

        if(mPalletsCount.getText()!=null && !mPalletsCount.getText().toString().isEmpty()){
            double palletReturnQty= Double.parseDouble(mPalletsCount.getText().toString());
            if(palletReturnQty>=0){
                delivery_item.setQTY(palletReturnQty);
            }
            DeliveryController.getInstance().insertOrUpdateDeliveryItem(delivery_item);
        }


    }

    private boolean valid(View view) {
        if (selectedDelivery == null) {
            SnackBarToast.showMessage(view, "Select a delivery");
            return false;
        }
        if (selectedMaterial == null) {
            SnackBarToast.showMessage(view, "Select a material");
            return false;
        }
        if (mPalletsCount.getText().toString().isEmpty()) {
            return true;
        }
        float palletReturnQuantity = 0;
        try {
            palletReturnQuantity = Float.parseFloat(mPalletsCount.getText().toString());
        } catch (Exception e) {
            SnackBarToast.showMessage(view, "Enter a valid Quantity");
            return false;
        }

        if (palletReturnQuantity == 0) {
            return true;
        }

        double qty = delivery_item.getQTY() == null ? 0 : delivery_item.getQTY();
        double palletsInTruck = DeliveryController.getInstance().getPalletInTruckByDelivery();
        if ((palletsInTruck + palletStock + qty) < palletReturnQuantity) {
            if (delivery_item != null) {
                mPalletsCount.setText(delivery_item.getQTY() == null || delivery_item.getQTY() < 1 ? "" : delivery_item.getQTY().toString());
            }
            SnackBarToast.showMessage(view, "Pallets not available in Truck");
            return false;
        }

        return true;
    }

    public void proceedButtonClick(View view) {
        if (valid(view)) {
            saveData();
            //Delete items if its HH Created with 0 quantities.
            DeliveryController.getInstance().deleteHHCreatedEmptyItemsIfExist(DeliveryController.Quantity.PALLETDROP);
            finish();
        }
    }
}