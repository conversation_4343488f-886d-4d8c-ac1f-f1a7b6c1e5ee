package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Spinner;

import com.abinbev.oasis.adapter.TrailerDropDownAdapter;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.TRL_CUST_MAP_HEADER;
import com.abinbev.oasis.be.TRL_PICK_DRP;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TrailerController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.Utils;
import com.unvired.core.ApplicationManager;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.abinbev.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class TrailerPickupActivity extends AppCompatActivity {
    private Spinner trailer1Spinner, trailer2Spinner;
    private String custId = "";
    private SHIPMENT_HEADER shipment_header = null;
    private List<TRL_PICK_DRP> trl_pick_drpList;
    private List<TRL_CUST_MAP_HEADER> trl_cust_map_headerList;
    private TRL_PICK_DRP trailer1 = null;
    private TRL_PICK_DRP trailer2 = null;
    private TRL_CUST_MAP_HEADER trailer1Selected = null;
    private TRL_CUST_MAP_HEADER trailer2Selected = null;
    private TrailerDropDownAdapter trailerDropDownAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_trailer_pickup);
        iniViews();
        getData();
    }

    private void getData() {
        VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
        if (visit == null) {
            finish();
        }
        if (custId == null || custId.isEmpty()) {
            custId = visit.getCUST_NO();
        }
        shipment_header = DBHelper.getInstance().getShipmentRow();
        if (shipment_header == null) {
            finish();
        }
        loadTrailers();
        setExistingTrailers();
    }

    private void setExistingTrailers() {
        trl_pick_drpList = new ArrayList<>();
        trl_pick_drpList = TrailerController.getInstance().getPickedOrDroppedTrailersForCustomer(custId, Constants.TRAILERPICK);
        if (trl_pick_drpList != null && trl_pick_drpList.size() > 0) {
            for (TRL_PICK_DRP trl_pick_drp : trl_pick_drpList) {
                if (trl_pick_drp.getTRL_TYPE() != null) {
                    if (trl_pick_drp.getTRL_TYPE() == 1) {
                        List<TRL_CUST_MAP_HEADER> trailerRows1 = new ArrayList<>();
                        for (TRL_CUST_MAP_HEADER i : trl_cust_map_headerList) {
                            if (i.getTRAILER_ID().equals(trl_pick_drp.getTRL_NO())) {
                                trailerRows1.add(i);
                            }
                        }
                        if (trailerRows1.size() > 0 && trailerRows1.get(0) != null) {
                            trailer1Spinner.setSelection(trailerDropDownAdapter.getPositionOfObject(trailerRows1.get(0)) + 1);
                        }
                        trailer1 = trl_pick_drp;
                    } else if (trl_pick_drp.getTRL_TYPE() == 2) {
                        List<TRL_CUST_MAP_HEADER> trailerRows2 = new ArrayList<>();
                        for (TRL_CUST_MAP_HEADER i : trl_cust_map_headerList) {
                            if (i.getTRAILER_ID().equals(trl_pick_drp.getTRL_NO())) {
                                trailerRows2.add(i);
                            }
                        }
                        if (trailerRows2.size() > 0 && trailerRows2.get(0) != null) {
                            trailer2Spinner.setSelection(trailerDropDownAdapter.getPositionOfObject(trailerRows2.get(0)) + 1);
                        }
                        trailer2 = trl_pick_drp;
                    }
                }
            }
        }
    }

    private void loadTrailers() {
        trl_cust_map_headerList = new ArrayList<>();
        trl_cust_map_headerList = TrailerController.getInstance().getTrailersForCustomer(custId);
        try {
            TRL_CUST_MAP_HEADER trl_cust_map_header = new TRL_CUST_MAP_HEADER();
            trl_cust_map_header.setTRAILER_ID("");
        } catch (DBException e) {
            Logger.e("", e);
        }
        if (trl_cust_map_headerList != null) {
            setupSpinner();
        }
    }

    private void setupSpinner() {
        trailerDropDownAdapter = new TrailerDropDownAdapter(this, android.R.layout.simple_spinner_dropdown_item, trl_cust_map_headerList,true);
        trailer1Spinner.setAdapter(trailerDropDownAdapter);
        trailer2Spinner.setAdapter(trailerDropDownAdapter);
        trailer1Spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position != 0) {
                    trailerDropDownAdapter.setSelectionPosition(position);
                    trailer1Selected = trl_cust_map_headerList.get(position-1);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        trailer2Spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position != 0) {
                    trailerDropDownAdapter.setSelectionPosition(position);
                    trailer2Selected = trl_cust_map_headerList.get(position-1);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void iniViews() {
        trailer1Spinner = findViewById(R.id.xTrailerPickUpTrailer1Spinner);
        trailer2Spinner = findViewById(R.id.xTrailerPickUpTrailer2Spinner);
    }


    public void navigateToSettings(View view) {
        Utils.showSettingDialog(TrailerPickupActivity.this);
    }

    public void closeActivity(View view) {
        finish();
    }

    public void clearTrailer1(View view) {
        trailer1Spinner.setSelection(0);
        trailer1Selected = null;
    }

    public void clearTrailer2(View view) {
        trailer2Spinner.setSelection(0);
        trailer2Selected = null;
    }

    public void proceed_onClick(final View view) {
        if (validate(view)) {
            if((trailer1Selected==null && trailer1!=null) || (trailer2Selected==null & trailer2!=null)){
                DialogMessage dialogMessage= new DialogMessage(this);
                dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Clearing Trailers will undo pickup of trailers if any.", "Undo", "cancel", new DialogClickListner() {
                    @Override
                    public void onPositiveClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                        saveData(view);
                    }
                    @Override
                    public void onNegativeClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                    }

                    @Override
                    public void onNeutralClick(Dialog dialogMessage) {

                    }
                });
            }else {
                saveData(view);
            }
        }
    }

    private void saveData(View view) {
        TRL_CUST_MAP_HEADER newTrailer1 = null;
        TRL_CUST_MAP_HEADER newTrailer2 = null;

        if(trailer1Selected!=null){
            newTrailer1=trailer1Selected;
        }
        if(trailer2Selected!=null){
            newTrailer2=trailer2Selected;
        }

        boolean trailer1ToBePicked = false;
        boolean trailer2ToBePicked = false;
        boolean trailer1Undo = false;
        boolean trailer2Undo = false;

        if (newTrailer1 == null && trailer1 != null)
        {
            trailer1Undo = true;
        }

        if (newTrailer2 == null && trailer2 != null)
        {
            trailer2Undo = true;
        }

        if (newTrailer1 != null && (trailer1 == null || !trailer1.equals(newTrailer1))){
            if(shipment_header.getTRL1_P().isEmpty() || shipment_header.getTRL1_P()==null || shipment_header.getTRL2_P().isEmpty() || shipment_header.getTRL2_P()==null  || trailer1Undo || trailer2Undo){
                trailer1ToBePicked=true;
            }else {
                trailer1Selected=newTrailer1;
                SnackBarToast.showMessage(view,"Truck is Full");
                return;
            }
        }

        if(newTrailer2!=null && (trailer2==null || !trailer2.equals(newTrailer2))){
            if(!(!(shipment_header.getTRL1_P().isEmpty() || shipment_header.getTRL1_P()==null) && trailer1ToBePicked)
                    || (!(shipment_header.getTRL2_P().isEmpty() || shipment_header.getTRL2_P()==null) && trailer1ToBePicked)
                    || (!trailer1Undo && trailer1ToBePicked)
                    || (!trailer2Undo && trailer1ToBePicked)){
                trailer2ToBePicked=true;
            }else {
                trailer2Selected = newTrailer2;
                SnackBarToast.showMessage(view,"Truck is Full");
                return;
            }
        }


        if (trailer1Undo)
        {
           try {
                ApplicationManager.getInstance().getDataManager().delete(trailer1);
               this.trl_pick_drpList.remove(trl_pick_drpList.indexOf(trailer1));
            } catch (DBException e) {
                Logger.e("", e);
            }
            trailer1 = null;
            shipment_header.setTRL1_P("");
        }

        if (trailer2Undo)
        {
            try {
                ApplicationManager.getInstance().getDataManager().delete(trailer2);
                this.trl_pick_drpList.remove(trl_pick_drpList.indexOf(trailer2));
            } catch (DBException e) {
                Logger.e("", e);
            }
            trailer2 = null;
            shipment_header.setTRL2_P("");
        }

        if (trailer1ToBePicked)
        {
            if (trailer1 != null)
            {
                trailer1.setTRL_NO(newTrailer1.getTRAILER_ID());
                this.trl_pick_drpList.get(trl_pick_drpList.indexOf(trailer1)).setTRL_NO(newTrailer1.getTRAILER_ID());;
            }
            else
            {
                UpdateTrailerPickDrop(newTrailer1.getTRAILER_ID(), 1);
            }
            shipment_header.setTRL1_P(newTrailer1.getTRAILER_ID());
        }

        if (trailer2ToBePicked)
        {
            if (trailer2 != null)
            {
                trailer2.setTRL_NO(newTrailer2.getTRAILER_ID());
                this.trl_pick_drpList.get(trl_pick_drpList.indexOf(trailer2)).setTRL_NO(newTrailer2.getTRAILER_ID());
            }
            else
            {
                UpdateTrailerPickDrop(newTrailer2.getTRAILER_ID(), 2);
            }
            shipment_header.setTRL2_P(newTrailer2.getTRAILER_ID());
        }
        if(trl_pick_drpList!=null && trl_pick_drpList.size()>0){
            TrailerController.getInstance().updateTrailerPickDrop(trl_pick_drpList);
        }
        DBHelper.getInstance().updateShipmentStatus(shipment_header);
        if(trl_pick_drpList!=null && trl_pick_drpList.size()>0 && trl_pick_drpList.get(0)!=null){
            DBHelper.getInstance().createNewTime(trl_pick_drpList.get(0).getCUST_NO(),Constants.TIME_TYPE_TRAILER);
        }
        finish();
    }


    private void UpdateTrailerPickDrop(String trailerId, int TrlPosition)
    {

        TRL_PICK_DRP trl1PickDropRow = null;
        try {
            trl1PickDropRow = new TRL_PICK_DRP();
            trl1PickDropRow.setFid(shipment_header.getLid());
            trl1PickDropRow.setSHIP_NO(shipment_header.getSHIP_NO());
            trl1PickDropRow.setTRL_TYPE((long) TrlPosition);
            trl1PickDropRow.setTRL_NO(trailerId);
            trl1PickDropRow.setCUST_NO(custId);
            trl1PickDropRow.setIS_DRP("");
            TrailerController.getInstance().insertOrUpdateTRLPickDrop(trl1PickDropRow);
        } catch (DBException e) {
            Logger.e("", e);
        }

    }

    private boolean validate(View v) {
        if (trailer1Selected != null && trailer2Selected != null) {
            if (trailer1Selected == trailer2Selected) {
                SnackBarToast.showMessage(v, "Trailer1 and Trailer2 cannot be same");
                return false;
            }
        }
        return true;
    }
}