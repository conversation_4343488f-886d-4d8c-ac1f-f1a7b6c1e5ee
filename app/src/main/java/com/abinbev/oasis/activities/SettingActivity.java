package com.abinbev.oasis.activities;

import androidx.appcompat.app.AppCompatActivity;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.INPUT_GET_SHIPMENTS_HEADER;
import com.abinbev.oasis.be.INPUT_RESET_SHIPMENT_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.PAHelper;
import com.unvired.core.FrameworkVersion;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.login.AuthenticationService;
import com.unvired.message.UNVDataHelper;
import com.unvired.model.InfoMessage;
import com.unvired.oasis.R;
import com.unvired.sync.out.ISyncAppCallback;
import com.unvired.sync.response.ISyncResponse;
import com.unvired.sync.response.SyncBEResponse;
import com.unvired.utils.FrameworkHelper;

import java.util.Vector;

public class SettingActivity extends AppCompatActivity implements View.OnClickListener, DialogClickListner, Logger.ILogSenderCallback {
    private static final String TAG = SettingActivity.class.getName();
    RelativeLayout sendLogBtn,depotSetupBtn,resetShipBtn,resetAppBtn,changePrinterBtn,sendAppDataBtn,exitBtn, simulateCrash, pushDb;
    private DialogMessage dialogMessage;
    private TextView versionNo;

    Constants.settingType settingType;
    private String responseCode="";
    private String responseText="";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting);
        sendLogBtn=findViewById(R.id.setting_send_log);
        depotSetupBtn=findViewById(R.id.setting_setup_depot);
        resetShipBtn=findViewById(R.id.setting_reset_shipment);
        resetAppBtn=findViewById(R.id.setting_reset_app);
        changePrinterBtn=findViewById(R.id.setting_change_printer);
        sendAppDataBtn=findViewById(R.id.setting_send_app_db);
        exitBtn=findViewById(R.id.setting_exit_app);
        simulateCrash=findViewById(R.id.setting_simulate_crash);
        pushDb=findViewById(R.id.setting_push_DB);
        versionNo = findViewById(R.id.versionNo);
        dialogMessage = new DialogMessage(this);
        versionNo.setText(getString(R.string.ver_no)+FrameworkVersion.getApplicationVersion());
        initListener();

    }

    private void initListener() {
        sendLogBtn.setOnClickListener(this);
        depotSetupBtn.setOnClickListener(this);
        resetShipBtn.setOnClickListener(this);
        resetAppBtn.setOnClickListener(this);
        changePrinterBtn.setOnClickListener(this);
        sendAppDataBtn.setOnClickListener(this);
        exitBtn.setOnClickListener(this);
        simulateCrash.setOnClickListener(this);
        pushDb.setOnClickListener(this);

    }

    public void closeActivity(View view) {
        finish();
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        DialogMessage dialogMessage = new DialogMessage(SettingActivity.this);

        if(id==R.id.setting_send_log){
            settingType = Constants.settingType.SEND_LOG;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.send_log_alert_message),"Cancel","Send Logs to Server",SettingActivity.this);
        }
        if(id==R.id.setting_setup_depot){
            settingType = Constants.settingType.SETUP_DEPOT;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.depot_setup_alert_message),"Cancel","Proceed",SettingActivity.this);
        }
        if(id==R.id.setting_reset_shipment){
            settingType = Constants.settingType.RESET_SHIPMENT;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.reset_ship_alert_message),"Do not Reset","Reset on Device and SAP",SettingActivity.this);
        }
        if(id==R.id.setting_reset_app){
            settingType = Constants.settingType.RESET_APP;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.reset_app_alert_message),"Cancel","Ok",SettingActivity.this);
        }
        if(id==R.id.setting_change_printer){
            Intent intent = new Intent(this,PrinterSetupActivity.class);
            startActivity(intent);

        }
        if(id==R.id.setting_send_app_db){
            settingType = Constants.settingType.SEND_APP_DB;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.send_tosap__alert_message),"Cancel","Send App DB to Server",SettingActivity.this);
        }
        if(id==R.id.setting_exit_app){
            settingType = Constants.settingType.EXIT_APP;
            dialogMessage.showDialogWithPositiveAndNegativeBtn("",getResources().getString(R.string.exit_app_alert_message),"Cancel","Exit Application",SettingActivity.this);
        }
        if(id==R.id.setting_simulate_crash){
            settingType = Constants.settingType.EXIT_APP;
            throw new RuntimeException("Test Crash");
        }
        if(id==R.id.setting_push_DB){
            try {
                UNVDataHelper.exportApplicationData();
                Toast.makeText(this, "The DB has been pushed to server", Toast.LENGTH_SHORT).show();
            } catch (DBException e) {
                Logger.e("", e);
            }
        }
    }



    @Override
    public void onPositiveClick(Dialog dialogMessage) {
        dialogMessage.dismiss();

    }

    @Override
    public void onNegativeClick(Dialog dialogMessage) {
        /*todo send log to server*/
        sendLogBtn.setEnabled(false);
        dialogMessage.dismiss();
        if(settingType== Constants.settingType.SEND_LOG) {
            //check connection
            Logger.i("User clicked Send Log Button");
            try
            {
                //UnviredFrameworkUtils.SendLogsToServer();
                Logger.sendLogToServerInSync(this);
            }
            catch (Exception ex)
            {
                Logger.e("", ex);
            }
            sendLogBtn.setEnabled(true);
        }
        if(settingType== Constants.settingType.SETUP_DEPOT){
            Logger.i("User clicked Depot Setup Button");
            setUpDepot(Constants.BE_DEPOT);
        }
        if(settingType== Constants.settingType.RESET_SHIPMENT){
            Logger.i("User clicked Rest Shipment Button");
            resetShipment(Constants.BE_SHIPMENT);
        }
        if(settingType== Constants.settingType.RESET_APP){
            Logger.i("User clicked Rest App Button");
            FrameworkHelper.resetApplication(true);
            this.finishAffinity();
            System.exit(0);
        }

        if(settingType== Constants.settingType.SEND_APP_DB){
            Logger.i("User clicked Send DB to Server Button");
            //check connection
            try
            {
                //UnviredFrameworkUtils.SendAppDBToServer();

            }
            catch (Exception ex)
            {
                Logger.e("", ex);
            }
        }

        if(settingType== Constants.settingType.EXIT_APP) {
            Logger.i("User clicked Exit App Button");
           // AuthenticationService.logout();
            this.finishAffinity();
        }

    }

    private void resetShipment(String beShipment) {
        try
        {
            SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
            INPUT_RESET_SHIPMENT_HEADER header = null;
            if(shipment_header != null) {
                try {
                    header = new INPUT_RESET_SHIPMENT_HEADER();
                    header.setSHIP_NO(shipment_header.getSHIP_NO());
                } catch (DBException e) {
                    Logger.e("", e);
                }
            }
            if(shipment_header!=null && header != null){
                String[] res = resetShipmentAtServer(header,beShipment);
                switch (res[0])
                {
                    case Constants.FAILURE:
                        break;

                    case Constants.SUCCESS:
                        DBHelper.getInstance().clearShipmentAndRelatedBEs();
                        break;
                }
                Logger.i("Reset Shipment Done");
            }else
                Toast.makeText(this, "Shipment does't exist", Toast.LENGTH_SHORT).show();


        }catch (Exception e){
            Logger.e("", e);
        }
    }

    private void setUpDepot(String beDepot) {
        try
        {
            SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();

            SHIPMENT_HEADER header = getShipmentRow(shipment_header);
            INPUT_RESET_SHIPMENT_HEADER inputheader = null;
            if(header!=null) {
                try {
                    inputheader = new INPUT_RESET_SHIPMENT_HEADER();
                    inputheader.setSHIP_NO(shipment_header.getSHIP_NO());
                } catch (DBException e) {
                    Logger.e("", e);
                }
            }
            if(header!=null && inputheader != null) {
                String[] res = resetShipmentAtServer(inputheader,beDepot);
                switch (res[0])
                {
                    case Constants.FAILURE:
                        break;

                    case Constants.SUCCESS:
                        DBHelper.getInstance().clearShipmentAndRelatedBEs();
                        break;
                }
            }else{
                DBHelper.getInstance().clearShipmentAndRelatedBEs();
                Intent intent = new Intent(SettingActivity.this,DepotListActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                finish();
            }
        }
        catch (Exception e)
        {
            Logger.e("", e);
        }
    }

    private SHIPMENT_HEADER getShipmentRow( SHIPMENT_HEADER shipment_header) {
        if (shipment_header != null)
        {
            if(DeliveryController.getInstance().getCurrentVisitCount(shipment_header)==0) {
                shipment_header = null;
            }
        }
        else
        {
            shipment_header = null;
        }
        return  shipment_header;
    }


    @Override
    public void onNeutralClick(Dialog dialogMessage) {
    }
    // Call
    private String[] resetShipmentAtServer(final INPUT_RESET_SHIPMENT_HEADER header, final String type) {
        final ISyncAppCallback callback = new ISyncAppCallback() {
            @Override
            public void onResponse(ISyncResponse iSyncResponse) {

                SyncBEResponse syncBEResponse;
                responseText = null;

                if (iSyncResponse == null) {
                    responseCode = Constants.RESPONSE_CODE_ERROR;
                    responseText = getResources().getString(R.string.invalidResponse);
                } else {

                    switch (iSyncResponse.getResponseStatus()) {
                        case SUCCESS:

                            if (iSyncResponse instanceof SyncBEResponse) {

                                syncBEResponse = (SyncBEResponse) iSyncResponse;

                                responseCode = Constants.RESPONSE_CODE_SUCCESSFUL;
                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        responseCode = infoMessages.get(i).getCategory().equals(InfoMessage.CATEGORY_SUCCESS) ? Constants.RESPONSE_CODE_SUCCESSFUL : Constants.RESPONSE_CODE_ERROR;

                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();


                                }

                                if (responseText==null || responseText.trim().isEmpty())
                                    responseText = getResources().getString(R.string.successful_delete);
                                if(!responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)){
                                    DBHelper.getInstance().clearShipmentAndRelatedBEs();
                                }else{
                                    responseText = getResources().getString(R.string.invalidResponse);
                                }
                            }
                            break;

                        case FAILURE:
                            responseCode = Constants.RESPONSE_CODE_ERROR;
                            if (iSyncResponse instanceof SyncBEResponse) {
                                syncBEResponse = (SyncBEResponse) iSyncResponse;
                                responseText = syncBEResponse.getErrorMessage();

                                if (syncBEResponse.getErrorMessage().contains(getResources().getString(R.string.invalidResponse))) {
                                    responseText = getResources().getString(R.string.invalidResponse);
                                } else {
                                    responseText = syncBEResponse.getErrorMessage();
                                }

                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();
                                }

                                if (responseText.trim().isEmpty())
                                    responseText = getResources().getString(R.string.invalidResponse);

                            } else {
                                responseText = getResources().getString(R.string.invalidResponse);
                            }
                            break;
                    }

                    if (responseCode != null && responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showInfo(true, responseText,type);
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showInfo(false, responseText,type);
                            }
                        });
                    }
                    //DBHelper.getInstance().clearShipmentAndRelatedBEs();
                }
            }
        };

        /*
         * Always execute Process Agent(PA) in thread
         */
        new Thread(new Runnable() {
            @Override
            public void run() {
                PAHelper.resetShipmentFromServer(header, callback);
            }
        }).start();

        return new String[]{responseCode,responseText};

    }

    private void showInfo(boolean error, String responseText,String type) {
       /* new AlertDialog.Builder(this)
                .setCancelable(false)
                .setMessage(responseText)
                .setPositiveButton(getString(R.string.ok), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        if (!error) {
                            dialogInterface.dismiss();
                        }
                    }
                }).create().show();*/
        dialogMessage.showDialogWithPositive(type.equals(Constants.BE_DEPOT) ?"Depot Setup":"Reset Shipment", responseText, "Ok", new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
                if(!error){
                    Intent intent;
                    intent = new Intent(SettingActivity.this,ShipmentListActivity.class);
                    DEPOT_HEADER depots = DepotController.getInstance().getSelectedDepotRow();
                    if(depots != null) {
                        intent.putExtra("DEPOT", depots.getDEPOT());
                    }
                    if(type.equals(Constants.BE_DEPOT))
                        intent = new Intent(SettingActivity.this,DepotListActivity.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    startActivity(intent);
                }
                finish();

            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {

            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }
}