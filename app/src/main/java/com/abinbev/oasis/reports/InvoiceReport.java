package com.abinbev.oasis.reports;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Environment;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;

import com.abinbev.oasis.ModelClasses.STOCK_REPORT;
import com.abinbev.oasis.ModelClasses.TRIP_INSP_PRINT_MODEL;
import com.abinbev.oasis.activities.PreTripInspectionSecurityActivity;
import com.abinbev.oasis.be.ATTACHMENT;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.CUSTOMIZATION_HEADER;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.abinbev.oasis.be.INVOICE;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.AttachmentHelper;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Controllers.DepotController;
import com.abinbev.oasis.util.Controllers.InvoiceController;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.IPrintCallBack;
import com.abinbev.oasis.util.PrinterUtils;
import com.abinbev.oasis.util.ReportHelper;
import com.abinbev.oasis.util.SAPInvFooter;
import com.abinbev.oasis.util.SAPInvHeader;
import com.abinbev.oasis.util.SAPInvItem;
import com.abinbev.oasis.util.SAPInvSummary;
import com.abinbev.oasis.util.Controllers.TimeController;
import com.abinbev.oasis.util.Utils;
import com.google.zxing.BarcodeFormat;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PRStream;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfObject;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.unvired.core.FrameworkVersion;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.pdf.writer.PDFDocument;
import com.unvired.pdf.writer.PDFDocument.Align;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.channels.FileChannel;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.abinbev.oasis.util.Constants.ATTACH_TYPE_POSTTRIP_MGR;
import static com.abinbev.oasis.util.Constants.ATTACH_TYPE_PRETRIP_MGR;
import static com.abinbev.oasis.util.Constants.DocType.InputChecklist;
import static com.abinbev.oasis.util.Constants.DocType.Invoice;


public class InvoiceReport {
    private Font font;
    private Font headerFont;
    private Font subHeaderFont;
    private Font rightHeader;
    private PDFDocument pdfDocument = null;
    private boolean preview;

    private Font grayFont;
    private BaseColor lightGray;
    private LinkedHashMap<String, MATERIAL_HEADER> matHashMap = null;
    SimpleDateFormat simpleDateFormat = null;
    private String FONTS[][] = {{"assets/ReformaGroteskMediumC.ttf", BaseFont.WINANSI}};
    private String CONGEST_FONT[][] = {{"assets/ReformaGroteskMediumC.ttf", BaseFont.WINANSI}};

    BaseFont base = null;
    BaseFont congestBase = null;

    public InvoiceReport(Font font, boolean preview) {
        try {
            base = BaseFont.createFont(FONTS[0][0], BaseFont.CP1252, true);
            congestBase = BaseFont.createFont(CONGEST_FONT[0][0], BaseFont.CP1252, true);
        } catch (DocumentException | IOException e) {
            Logger.e("", e);
        }
        this.font = font;
        this.preview = preview;
        this.pdfDocument = ReportHelper.getInstance().getPDFDocument();
        lightGray = Utils.getColor();
        this.headerFont = new Font(base, 14f, Font.NORMAL);
        this.rightHeader = new Font(congestBase, 14.5f, Font.NORMAL);
        this.subHeaderFont = new Font(base, 13f, Font.NORMAL);
        lightGray = new BaseColor(30,30,30);
        grayFont = new Font(font.getBaseFont(), 12f, Font.BOLD, lightGray);
    }


    public void printReport(String inspectionType) throws DocumentException, MalformedURLException, IOException {

        String shipment_no = "";
        String vehicle_no = "";
        String depotNo = "";
        String depotName = "";

        String trl1_no = "";
        String trl2_no = "";
        String driver1_no = "";
        String driver1_name = "";
        String driver2_no = "";
        String driver2_name = "";
        String downloadDate = "";
        String downloadTime = "";

        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
        DEPOT_HEADER depot_header = DepotController.getInstance().getSelectedDepotRow();
        if (shipment_header == null || depot_header == null)
            return;
        //DEPOT_HEADER depot_header = depot_headers.get(0);
        shipment_no = shipment_header.getSHIP_NO();
        depotNo = depot_header.getDEPOT();
        depotName = depot_header.getNAME();

        vehicle_no = Utils.isNullOrEmpty(shipment_header.getTRUCK()) ? "" : shipment_header.getTRUCK();
        trl1_no = Utils.isNullOrEmpty(shipment_header.getTRL1()) ? "" : shipment_header.getTRL1();
        trl2_no = Utils.isNullOrEmpty(shipment_header.getTRL2()) ? "" : shipment_header.getTRL2();

        driver1_no = Utils.isNullOrEmpty(shipment_header.getDRV1()) ? "" : shipment_header.getDRV1();
        driver1_name = Utils.isNullOrEmpty(shipment_header.getDRV1_NAME()) ? "" : shipment_header.getDRV1_NAME();

        driver2_no = Utils.isNullOrEmpty(shipment_header.getDRV2()) ? "" : shipment_header.getDRV2();
        driver2_name = Utils.isNullOrEmpty(shipment_header.getDRV2_NAME()) ? "" : shipment_header.getDRV2_NAME();
        Date currentDate = new Date();
        simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
        SimpleDateFormat simpleTimeFormat = new SimpleDateFormat("kk:mm:ss");
        downloadDate = simpleDateFormat.format(currentDate);
        downloadTime = simpleTimeFormat.format(currentDate);
        List<TRIP_INSP_PRINT_MODEL> tripInspModels = null;

        String heading = "Pretrip Inspection";

        int driverAttachmentType = Constants.ATTACH_TYPE_PRETRIP_DRV;
        int securityAttachmentType = Constants.ATTACH_TYPE_PRETRIP_SEC;
        int ManagerAttachmentType = ATTACH_TYPE_PRETRIP_MGR;

        switch (inspectionType) {
            case Constants.TRIP_INSP_TYPE_PRETRIP:
                tripInspModels = DBHelper.getInstance().getTripQuestionAndAnswer(shipment_header, inspectionType);
                break;
            case Constants.TRIP_INSP_TYPE_POSTTRIP:
            tripInspModels =  DBHelper.getInstance().getTripQuestionAndAnswer(shipment_header,inspectionType);
            driverAttachmentType = Constants.ATTACH_TYPE_POSTTRIP_DRV;
            securityAttachmentType = Constants.ATTACH_TYPE_POSTTRIP_SEC;
            ManagerAttachmentType = Constants.ATTACH_TYPE_POSTTRIP_MGR;

            heading = "Posttrip Inspection";
            break;

        }


        grayFont = new Font(base, 7f, Font.BOLD, lightGray);
        printHeader(heading,depotNo, depotName, shipment_no, downloadDate, downloadTime, driver1_no, driver1_name, driver2_no, driver2_name, trl1_no, trl2_no,vehicle_no);
        printPreTripInstruction(tripInspModels,inspectionType);
        printQuestionAndAnswer(tripInspModels);
        defectTypeAndDesc();
        printSignature(driver1_name, PreTripInspectionSecurityActivity.getCardNumber(), inspectionType);
        printFooter();
    }

    private void printSignature(String driver1_name, String cardNumber, String inspectionType) throws MalformedURLException, IOException, DocumentException {
        //Driver
        SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();

        //Security
        String cardNo = PreTripInspectionSecurityActivity.getCardNumber();
        ATTACHMENT attachmentRowSec = null;

        //Supervisor
        String authManagerId =  DataHelper.getInstance().getAuthManagerID();
        ATTACHMENT attachmentRowMan = null;

        ATTACHMENT attachmentRow = null;
        if(inspectionType.equals(Constants.TRIP_INSP_TYPE_PRETRIP)){

            attachmentRow = DBHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_PRETRIP_DRV, currentShipmentHeader.getSHIP_NO(), currentShipmentHeader.getDRV1(), Constants.IMAGE_TYPE, currentShipmentHeader.getSHIP_NO());
            attachmentRowSec = AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_PRETRIP_SEC, currentShipmentHeader.getSHIP_NO(), cardNo, Constants.IMAGE_TYPE);
            attachmentRowMan = AttachmentHelper.getInstance().getAttachment(ATTACH_TYPE_PRETRIP_MGR, currentShipmentHeader.getSHIP_NO(), authManagerId, Constants.IMAGE_TYPE);
        } else {
            attachmentRow = DBHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_POSTTRIP_DRV, currentShipmentHeader.getSHIP_NO(), currentShipmentHeader.getDRV1(), Constants.IMAGE_TYPE, currentShipmentHeader.getSHIP_NO());
            attachmentRowSec = AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_POSTTRIP_SEC, currentShipmentHeader.getSHIP_NO(), cardNo, Constants.IMAGE_TYPE);
            attachmentRowMan = AttachmentHelper.getInstance().getAttachment(ATTACH_TYPE_POSTTRIP_MGR, currentShipmentHeader.getSHIP_NO(), authManagerId, Constants.IMAGE_TYPE);
        }

        byte[] driverByte=null;
        byte[] securityByte = null;
        byte[] managerByte = null;
        if(attachmentRow!=null){
            driverByte= Utils.decodeToByte(attachmentRow.getDATA(), Base64.DEFAULT);
        }
        if (attachmentRowSec != null) {
            securityByte = Utils.decodeToByte(attachmentRowSec.getDATA(), Base64.DEFAULT);
        }
        if (attachmentRowMan != null) {
            managerByte = Utils.decodeToByte(attachmentRowMan.getDATA(), Base64.DEFAULT);
        }
        if(driverByte==null && securityByte==null && managerByte==null) {
            pdfDocument.advance(2);
        }
        PdfPTable table = new PdfPTable(5);
        table.setWidths(new float[]{4f, 1f,4f, 1f,4f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_CENTER);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        if (driverByte != null) {
            table.addCell(Image.getInstance(driverByte));
        } else {
            table.addCell(new Paragraph(""));
        }

        table.addCell(new Paragraph(""));
        if (securityByte != null) {
            table.addCell(Image.getInstance(securityByte));
        } else {
            table.addCell(new Paragraph(""));
        }

        table.addCell(new Paragraph(""));
        if (managerByte != null) {
            table.addCell(Image.getInstance(managerByte));
        } else {
            table.addCell(new Paragraph(""));
        }
        pdfDocument.printTable(table);
        PdfPTable table1 = new PdfPTable(5);
        table1.setWidths(new float[]{4f, 1f, 4f, 1f, 4f});
        table1.setWidthPercentage(100);
        table1.setHorizontalAlignment(Element.ALIGN_LEFT);
        table1.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table1.getDefaultCell().setHorizontalAlignment(Element.ALIGN_CENTER);
        table1.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        table1.addCell(new Paragraph("------------------\nDriver\n" + driver1_name, font));
        table1.addCell(new Paragraph(""));
        table1.addCell(new Paragraph("------------------\nSecurity\n" + (cardNumber != null ? "Card Num: "+cardNumber : ""), font));
        table1.addCell(new Paragraph(""));
        if(authManagerId!=null){
            table1.addCell(new Paragraph("------------------\nSupervisor\n" + authManagerId, font));
        }else{
            table1.addCell(new Paragraph(""));
        }

        pdfDocument.printTable(table1);
        pdfDocument.advanceWithDots(2, grayFont);

    }

    private void defectTypeAndDesc() throws DocumentException {
        pdfDocument.addLineSeparator(' ',font);
        PdfPTable table = new PdfPTable(2);
        table.setWidths(new float[]{2f, 7f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        table.addCell(new Paragraph("Defect Type :", font));
        table.addCell(new Paragraph("Description ", font));
        pdfDocument.printTable(table);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);
        PdfPTable tableBody = new PdfPTable(2);
        tableBody.setWidths(new float[]{2f, 7f});
        tableBody.setWidthPercentage(100);
        tableBody.setHorizontalAlignment(Element.ALIGN_LEFT);
        tableBody.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        tableBody.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        tableBody.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_CRITICAL_LABEL1, font));
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_CRITICAL_LABEL21, font));
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_MAJOR_LABEL1, font));
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_MAJOR_LABEL21, font));
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_MINOR_LABEL1, font));
        tableBody.addCell(new Paragraph(PrinterUtils.PRETRIP_REPORT_MINOR_LABEL21, font));
        pdfDocument.printTable(tableBody);

    }

    private void printQuestionAndAnswer(List<TRIP_INSP_PRINT_MODEL> tripInspModels) throws DocumentException {
        pdfDocument.printTextLine("Questions", font, Align.ALIGN_LEFT);

        PdfPTable table = new PdfPTable(4);
        table.setWidths(new float[]{1.3f, 0.7f,8f, 1f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        table.getDefaultCell().setPaddingLeft(0);
        table.getDefaultCell().setPaddingRight(0);
        table.addCell(new Paragraph("Type:", font));
        table.addCell(new Paragraph("No. ", font));
        table.addCell(new Paragraph("Question", font));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("Answ", font)));
        pdfDocument.printTable(table);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));

        //pdfDocument.addLineSeparator('-', font);
        PdfPTable tableBody = new PdfPTable(4);
        tableBody.setWidths(new float[]{1.3f, 0.7f, 8f, 1f});
        tableBody.setWidthPercentage(100);
        tableBody.setHorizontalAlignment(Element.ALIGN_LEFT);
        tableBody.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        tableBody.getDefaultCell().setPaddingTop(3);
        table.getDefaultCell().setPaddingLeft(0);
        table.getDefaultCell().setPaddingRight(0);
        tableBody.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        tableBody.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        int index = 0;
        for (int i = 0; i < tripInspModels.size(); i++) {
            index++;
            tableBody.addCell(new Paragraph(tripInspModels.get(i).getInspectionType(), font));
            tableBody.addCell(new Paragraph(index + ".", font));
            tableBody.addCell(new Paragraph(tripInspModels.get(i).getQuestion(), font));
            tableBody.addCell(getParagraphWithRightAlignCell(new Paragraph(tripInspModels.get(i).getAnswer(),font)));
        }
        pdfDocument.printTable(tableBody);

    }

    private void printPreTripInstruction(List<TRIP_INSP_PRINT_MODEL> tripInspModels, String inspectionType) throws DocumentException {
        if(inspectionType.equals(Constants.TRIP_INSP_TYPE_POSTTRIP)){
            pdfDocument.printTextLine("* A POST TRIP inspection must to be completed by all Drivers before each trip. " + ReportHelper.getFixedWidthString("", 1, ReportHelper.TextAlignment.LEFT), font);
        }else{
            pdfDocument.printTextLine("* A PRE TRIP inspection must to be completed by all Drivers before each trip. " + ReportHelper.getFixedWidthString("", 1, ReportHelper.TextAlignment.LEFT), font);
        }
        //pdfDocument.printTextLine("each trip.", font);
        pdfDocument.printTextLine("* All SAFETY CRITICAL items that are defective or faulty should bemarked as \"NO\" ", font);
        //pdfDocument.printTextLine("\"NO\"", font);
        pdfDocument.printTextLine("* These need to be reported, rectified and signed off by SAB management before the vehicle can depart. ", font);
        //pdfDocument.printTextLine(", font);
        pdfDocument.printTextLine("* It is incumbent on the Driver to ensure that the condition of the vehicle is accurately captured. ", font);
        //pdfDocument.printTextLine(" ", font);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);
    }

    private void printHeader(String heading, String depotNo, String depotName, String shipment_no, String downloadDate, String downloadTime, String driver1_no, String driver1_name, String driver2_no, String driver2_name, String trl1_no, String trl2_no, String vehicle_no) throws DocumentException {
        pdfDocument.printTextLine(heading+" ", headerFont, Align.ALIGN_CENTER);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);
        pdfDocument.printTextLine("Depot: " + ReportHelper.getFixedWidthString("", 16, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(depotNo, 10, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(depotName, 25, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTextLine("Shipment: " + ReportHelper.getFixedWidthString("", 12, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(shipment_no, 15, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTextLine("DownloadTime: " + ReportHelper.getFixedWidthString("", 8, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(downloadDate, 10, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(downloadTime, 8, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTextLine("Driver: " + ReportHelper.getFixedWidthString("", 15, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(driver1_no, 10, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 20, ReportHelper.TextAlignment.LEFT) + driver1_name, font);
        pdfDocument.printTextLine("Driver 2: " + ReportHelper.getFixedWidthString("", 12, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(driver2_no, 10, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 20, ReportHelper.TextAlignment.LEFT) + driver2_name, font);
        pdfDocument.printTextLine("Truck, Trailers: " + ReportHelper.getFixedWidthString("", 7, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(vehicle_no + (Utils.isNullOrEmpty(trl1_no) ? "" : "," + trl1_no)+(Utils.isNullOrEmpty(trl2_no) ? "" : ", " + trl2_no), 30, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTextLine("Mobile User ID: " + ReportHelper.getFixedWidthString("", 6, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(Utils.getUserId(), 10, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTextLine("App Version: " + ReportHelper.getFixedWidthString("", 9, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(FrameworkVersion.getApplicationVersion(), 10, ReportHelper.TextAlignment.LEFT), font);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);


    }

    private void printFooter() throws DocumentException {
        // pdfDocument.advanceWithDots(8, grayFont);
    }

    public void stockReportPrint(String type, boolean isFinal) throws DocumentException, IOException {
        stockReportHeader(isFinal);
        printStockTable();
        printStockSignature();
    }

    private void printStockTable() throws DocumentException {
        String[] dummyMatArr = DBHelper.getInstance().getDummyMaterialNos();
        String excludeClause = "";
        if (dummyMatArr != null && dummyMatArr.length > 0) {
            excludeClause = " WHERE MAT_NO NOT IN (";
            for (int i = 0; i < dummyMatArr.length; i++) {
                if (i == 0)
                    excludeClause = excludeClause + "'" + dummyMatArr[i] + "'";
                else
                    excludeClause = excludeClause + ", '" + dummyMatArr[i] + "'";
            }
            excludeClause = excludeClause + ")";
        }
        Map<String, STOCK_REPORT> stockReportMap = DBHelper.getInstance().getMaterial(excludeClause);
        String MatNo = "";
        String MatDesc = "";
        int OriginalLoad = 0;
        int Offloaded = 0;
        int QTY = 0;
        int RCR = 0;
        int UBR = 0;
        int FBR = 0;
        int BIT = 0;
        int TruckLoad = 0;
        String IS_EMPTY = "";
        String IS_RETURN = "";
        int[] ItemTotal = new int[7];
        PdfPTable table = new PdfPTable(9);
        table.setWidths(new float[]{1f, 4f, 1f, 1f, 1f, 1f, 1f, 1f, 1f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setPaddingLeft(0);
        table.getDefaultCell().setPaddingRight(0);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        table.addCell(new Paragraph("PROD\nCODE", font));
        table.addCell(new Paragraph("DESCRIPTION", font));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("ORIG\nLOAD", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("OFF\nLOAD", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("RCR\nQTY", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("UBR\nQTY", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("FBR\nQTY", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("BIT\nQTY", font)));
        table.addCell(getParagraphWithRightAlignCell(new Paragraph("TRUCK\nSTOCK", font)));
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);

        if(stockReportMap!=null) {
            for (STOCK_REPORT stockBean : stockReportMap.values()) {
                MatNo = stockBean.MAT_NO;
                MatDesc = stockBean.MAT_DESC;
                OriginalLoad = (int) stockBean.ORG_LOAD;
                Offloaded = (int) stockBean.OFFLOADED;
                RCR = (int) stockBean.RCR;
                UBR = (int) stockBean.UBR;
                FBR = (int) stockBean.FBR;
                BIT = (int) stockBean.BIT;
                TruckLoad = (int) stockBean.TRUCK_STOCK;

                ItemTotal[0] += OriginalLoad;
                ItemTotal[1] += Offloaded;
                ItemTotal[2] += RCR;
                ItemTotal[3] += UBR;
                ItemTotal[4] += FBR;
                ItemTotal[5] += BIT;
                ItemTotal[6] += TruckLoad;

                if (MatDesc.length() > 24) {
                    MatDesc = MatDesc.substring(0, 24);
                }

                Log.i("TAG", "printInitialStockTable: " + MatNo + ":::" + MatDesc);
                //
                table.addCell(new Paragraph(MatNo, font));
                table.addCell(new Paragraph(MatDesc, font));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(OriginalLoad, 5, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(Offloaded + "", 5, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(RCR + "", 6, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(UBR + "", 5, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(FBR + "", 5, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(BIT + "", 5, ReportHelper.TextAlignment.RIGHT), font)));
                table.addCell(getParagraphWithRightAlignCell(new Paragraph(ReportHelper.getFixedWidthString(TruckLoad + "", 6, ReportHelper.TextAlignment.RIGHT), font)));
                OriginalLoad = Offloaded = RCR = UBR = FBR = BIT = 0;
            }

        }
        pdfDocument.printTable(table);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);
        PdfPTable sumTable = new PdfPTable(9);
        sumTable.setWidths(new float[]{1f, 4f, 1f, 1f, 1f, 1f, 1f, 1f, 1f});
        sumTable.setWidthPercentage(100);
        sumTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        sumTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        sumTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        sumTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        sumTable.addCell(new Paragraph("", font));
        sumTable.addCell(new Paragraph("PACK TYPE TOTALS", font));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[0]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[1]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[2]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[3]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[4]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[5]),font)));
        sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(ItemTotal[6]),font)));
        pdfDocument.printTable(sumTable);
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);

    }

    private void printStockSignature() throws DocumentException, IOException {
        //Driver
        SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
        ATTACHMENT attachmentRow = DBHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_PRETRIP_DRV, currentShipmentHeader.getSHIP_NO(), currentShipmentHeader.getDRV1(), Constants.IMAGE_TYPE, currentShipmentHeader.getSHIP_NO());
        byte[] driverByte = null;
        if (attachmentRow != null) {
            driverByte = Utils.decodeToByte(attachmentRow.getDATA(), Base64.DEFAULT);
        }
        pdfDocument.addLineSeparator(' ',font);
        PdfPTable signatureTable = new PdfPTable(3);
        signatureTable.setWidths(new float[]{3.5f, 4f, 3.5f});
        signatureTable.setWidthPercentage(100);
        signatureTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        signatureTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        signatureTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        signatureTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        if (driverByte != null) {
            signatureTable.addCell(Image.getInstance(driverByte));
        } else {
            signatureTable.addCell(new Paragraph(""));
        }
        signatureTable.addCell(new Paragraph(""));
        signatureTable.addCell(new Paragraph("", font));
        pdfDocument.printTable(signatureTable);
        PdfPTable table = new PdfPTable(3);
        table.setWidths(new float[]{3.5f, 1f, 3.5f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        table.addCell(new Paragraph("--------------------------\nDriver", font));
        table.addCell(new Paragraph(""));
        table.addCell(new Paragraph("--------------------------\nChecker", font));
        pdfDocument.printTable(table);
        pdfDocument.advanceWithDots(2, grayFont);

    }

    private void stockReportHeader(boolean isFinal) throws DocumentException {
        Date date = new Date();
        pdfDocument.addLineSeparator(' ',font);
        simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
        if (isFinal) {
            pdfDocument.printTextLine("OASIS FINAL TRUCK STOCK REPORT " + simpleDateFormat.format(date) + " ", font, Align.ALIGN_CENTER);
        } else
            pdfDocument.printTextLine("OASIS INITIAL TRUCK STOCK REPORT " + simpleDateFormat.format(date) + " ", font, Align.ALIGN_CENTER);

        SHIPMENT_HEADER row = DBHelper.getInstance().getCurrentShipment();
        if (row != null) {
            pdfDocument.addLineSeparator(' ',font);
            String heading = "DRIVER:" + (row.getDRV1() == null ? "" : row.getDRV1()) + " SHIPMENT:" + (Utils.isNullOrEmpty(row.getSHIP_NO()) ? "" : row.getSHIP_NO()) + " TRUCK:" + (row.getTRUCK() == null ? "" : row.getTRUCK()) + " TRLR1:" + (row.getTRL1() == null ? "" : row.getTRL1()) + " TRLR2:" + (row.getTRL2() == null ? "" : row.getTRL2()+" ");
            pdfDocument.printTextLine(heading, font, Align.ALIGN_CENTER);

            pdfDocument.printTextLine("Mobile User ID: " + ReportHelper.getFixedWidthString("", 5, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(Utils.getUserId(), 16, ReportHelper.TextAlignment.RIGHT), font);
            pdfDocument.printTextLine("App Version:" + ReportHelper.getFixedWidthString("", 18, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(FrameworkVersion.getApplicationVersion(), 16, ReportHelper.TextAlignment.LEFT), font);

        } else {
            pdfDocument.addLineSeparator(' ',font);
            String heading = "DRIVER:" + "" + " SHIPMENT:" + ("") + " TRUCK:" + ("") + " TRLR1:" + ("") + " TRLR2:" + ("");
            pdfDocument.printTextLine(heading, font, Align.ALIGN_CENTER);

            pdfDocument.printTextLine("Mobile User ID: " + ReportHelper.getFixedWidthString("", 5, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(Utils.getUserId(), 16, ReportHelper.TextAlignment.RIGHT), font);
            pdfDocument.printTextLine("App Version:" + ReportHelper.getFixedWidthString("", 18, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(FrameworkVersion.getApplicationVersion(), 16, ReportHelper.TextAlignment.LEFT), font);

        }

    }

    public void generateReportPrint(Enum<Constants.DocType> type, Context context, boolean reprint,IPrintCallBack callBack) throws Exception {
        //callBack.onPrintSuccess(type);
        //generateAndPrintInvoice(type, context, reprint,callBack);
        inputChecklistInitialFooterPrint();
    }

    private void inputChecklistInitialFooterPrint() throws DocumentException {
        pdfDocument.printTextLine("Registered as a manufacturer and distributor of liquor under NLA Reference No. RG0000040", font, Align.ALIGN_LEFT);
        pdfDocument.printTextLine("This Tax Invoice is issued in terms of Section 21(5) of the Value Added Tax Act No 89 of\n" +
                "1991; and Ruling Reference: 2019/166 (28/1/23)", font, Align.ALIGN_LEFT);

    }


    public void generateInvoice() throws Exception {

        VISIT visitRow = TripSummaryController.getInstance().getCurrentVisitRow();
        InvoiceController.generateInvoice(visitRow);
        //GenerateInvoicePdf
        //generateInvoicePdf(visitRow, type, rePrint, context,callBack);

    }

    public void generateInvoicePdf(DELIVERY delivery, VISIT visitRow, final Enum<Constants.DocType> type,
                                   boolean reprint, final Context context, final IPrintCallBack callBack, Constants.CopyType copyType) throws DocumentException, InterruptedException, IOException, DBException, ParseException {
        PrinterUtils.setLocale(context);

        if (visitRow != null) {

            if (delivery == null )
                return;
            DEPOT_HEADER currentDepot = DepotController.getInstance().getSelectedDepotRow();

            CUSTOMER_HEADER customerHeader = DBHelper.getInstance().getCustomer(visitRow.getCUST_NO());

            CUSTOMIZATION_HEADER customizationheader = null;
            if(DBHelper.getInstance().getCustomizationHeader("INV_1P_TEXT")!=null){
                 customizationheader = DBHelper.getInstance().getCustomizationHeader("INV_1P_TEXT");
            }
            //iterate through deliveries

                 if(type==Invoice && !reprint && !delivery.getDELV_NO().equals(DeliveryController.getInstance().getCurrentDeliveryNo())){
                  //  Log.e("CURRENT_DELV_PRINT",DeliveryController.getInstance().getCurrentDeliveryNo());
                    //continue;
                }

                 pdfDocument = new PDFDocument(context.getFilesDir() + "", PageSize.A6.getWidth(), 10f, 10f, 25.0f, 5.0f, font, context.getApplicationContext());
                ReportHelper.getInstance().setPDFDocument(pdfDocument);
                /*if (type == Constants.DocType.Invoice && !reprint && !deliveryList.get(i).getDELV_NO().equals(DeliveryController.getInstance().currentDeliveryNo))
                    continue;*/

                SAPInvHeader sapInvHeader = null;
                try {
                    sapInvHeader = InvoiceController.getInstance().getSAPInvoiceHeader(customerHeader, delivery, currentDepot);

                } catch (DBException e) {
                    Logger.e("", e);
                }
                String SabOrdNo = sapInvHeader.SABOrderNo;
                //print SAPInvHeader
                if (type.equals(InputChecklist)) {
                    pdfDocument.printTextLine("INPUT CHECKLIST" + " ", headerFont, Align.ALIGN_CENTER);
                } else {
                   printCompanyLogoAndFirstHeader(delivery,currentDepot,context,reprint,type);
                }
                //new Doc
                printHeaderAndShipmentDetail(sapInvHeader);


                if (type == Invoice) {
                    pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
                    //pdfDocument.addLineSeparator('-', font);
                    //pdfDocument.printTextLine("--------------------------------------------------------------------------------------------", font);

                    pdfDocument.printTextLine("Order and Payment Terms", subHeaderFont);
                    if (sapInvHeader.payTerm == null) {
                        sapInvHeader.payTerm = "";
                    }

                    //payMethod
                    PdfPTable payMethodTable = getTableForPayMethodAndVatNo();
                    payMethodTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvHeader.paymentTermsMessageLine1, font)));
                    payMethodTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvHeader.payMethodLabel, font)));
                    payMethodTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.payMethod, font)));
                    pdfDocument.printTable(payMethodTable);
                    //Vat no
                    PdfPTable vatTable = getTableForPayMethodAndVatNo();
                    vatTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvHeader.paymentTermsMessageLine2, font)));
                    vatTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvHeader.custVATNoLabel, font)));
                    vatTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.custVATNo, font)));
                    pdfDocument.printTable(vatTable);

                    /*pdfDocument.printTextLine(ReportHelper.getFixedWidthString(sapInvHeader.paymentTermsMessageLine1,65, ReportHelper.TextAlignment.LEFT)+ReportHelper.getFixedWidthString(sapInvHeader.payMethodLabel,15, ReportHelper.TextAlignment.LEFT)+ReportHelper.getFixedWidthString(sapInvHeader.payMethod,30, ReportHelper.TextAlignment.RIGHT),font);
                    pdfDocument.printTextLine(ReportHelper.getFixedWidthString(sapInvHeader.paymentTermsMessageLine2,61, ReportHelper.TextAlignment.LEFT)+ReportHelper.getFixedWidthString(sapInvHeader.custVATNoLabel,15, ReportHelper.TextAlignment.LEFT)+ReportHelper.getFixedWidthString(sapInvHeader.custVATNo,30, ReportHelper.TextAlignment.RIGHT),font);
                    */
                    if (!Utils.isNullOrEmpty(sapInvHeader.paymentTermsMessageLine3)) {
                        pdfDocument.printTextLine(sapInvHeader.paymentTermsMessageLine3, font);
                    }
                    //If line4 exists print line 4
                    if (!Utils.isNullOrEmpty(sapInvHeader.paymentTermsMessageLine4)) {
                        pdfDocument.printTextLine(sapInvHeader.paymentTermsMessageLine4, font);
                    }

                    //If line5 exists print line5
                    if (!Utils.isNullOrEmpty(sapInvHeader.paymentTermsMessageLine5)) {
                        pdfDocument.printTextLine( sapInvHeader.paymentTermsMessageLine5, font);
                    }

                    pdfDocument.printTextLine(sapInvHeader.emptiesMessageLabel1, font);
                    pdfDocument.printTextLine(sapInvHeader.emptiesMessageLabel2, font);
                    pdfDocument.printTextLine(sapInvHeader.emptiesMessageLabel3, font);

                    pdfDocument.addLineSeparator(' ',font);
                    pdfDocument.printTextLine(sapInvHeader.liquorLicenceNoLabel + " " + sapInvHeader.liquorLicenceNo, font);
                    pdfDocument.printTextLine(sapInvHeader.deliveredFromLabel + " " + sapInvHeader.depot, font);
                }
                //print Delivered and Returned Items
                Double deltotalDelQty = 0.00d;
                Double deltotalBeerValue = 0.00d;
                Double deltotalDepositValue = 0.00d;
                Double deltotalTotalValue = 0.00d;
                Double deltotalTotalNonSABValue = 0.00d;

                Double rettotalDelQty = 0.00d;
                Double rettotalBeerValue = 0.00d;
                Double rettotalDepositValue = 0.00d;
                Double rettotalTotalValue = 0.00d;
                Double ZPRORunningTotal = 0.00d;
                //Delivered Item
                //pdfDocument.addLineSeparator(' ',font);
            pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
            //pdfDocument.addLineSeparator('-',font);
                String prodHeirachy = "";
                List<SAPInvItem> sapInvDeliveredItemList = InvoiceController.getSAPInvDeliveredItems(delivery.getDELV_NO());
                if (sapInvDeliveredItemList != null && sapInvDeliveredItemList.size() > 0) {
                    pdfDocument.printTextLine("Delivered ", headerFont, Align.ALIGN_LEFT);
                    PdfPTable itemTable = new PdfPTable(7);
                    itemTable.setWidths(new float[]{5.5f, 1f, 1.6f, 2f, 1.5f, 1.5f, 2f});
                    itemTable.setWidthPercentage(100);
                    itemTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                    itemTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
                    itemTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
                    itemTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);

                    itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("PROD   Description\nCODE", font)));
                    //itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("Description", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Del\nQty", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Unit\nPrice", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Beer\nValue", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Unit\nDep.", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Deposit\nValue", font)));
                    itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Total\nValue", font)));


                    for (SAPInvItem sapInvItem : sapInvDeliveredItemList) {
                        MATERIAL_HEADER material_header = DBHelper.getInstance().getMaterialList(sapInvItem.getProdCode());
                        if(material_header!=null && customizationheader!=null){
                            if(material_header.getPROD_HIER().startsWith(customizationheader.getKEY_VALUE())){
                                itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+ "*" +sapInvItem.prodDesc,31), font)));
                                prodHeirachy = material_header.getPROD_HIER();

                            }else{
                                itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+sapInvItem.prodDesc,31), font)));
                            }
                        }else{
                            itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+sapInvItem.prodDesc,31), font)));
                        }
//                        itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+sapInvItem.prodDesc,31), font)));
                       // itemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString(sapInvItem.prodDesc,24), font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.delQty != null ? sapInvItem.delQty.intValue() + "" : "", font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.unitPrice != null ? Utils.formatDouble(sapInvItem.unitPrice, 2): "", font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.beerValue != null ? Utils.formatDouble(sapInvItem.beerValue, 2) : "", font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.unitDeposit != null ?  Utils.formatDouble(sapInvItem.unitDeposit, 2): "", font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.depositValue != null ?  Utils.formatDouble(sapInvItem.depositValue, 2): "", font)));
                        itemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.totalValue != null ?  Utils.formatDouble(sapInvItem.totalValue, 2): "", font)));


                        if (sapInvItem.isZPROSubLine)
                            continue;

                        if (sapInvItem.isZPROLine) {
                            //deltotalBeerValue += sapInvItem.beerValue + sapInvItem.beerValue;
                            //deltotalTotalValue += sapInvItem.totalValue + sapInvItem.beerValue;
                            deltotalBeerValue = deltotalBeerValue + (sapInvItem.beerValue);
                            deltotalTotalValue = deltotalTotalValue + (sapInvItem.totalValue);
                            ZPRORunningTotal = ZPRORunningTotal + (sapInvItem.beerValue);
                        } else {
                            deltotalDepositValue = deltotalDepositValue + (sapInvItem.depositValue);
                            deltotalDelQty = deltotalDelQty + (sapInvItem.delQty);
                            deltotalBeerValue = deltotalBeerValue + (sapInvItem.beerValue);
                            deltotalTotalValue = deltotalTotalValue + (sapInvItem.totalValue);

                        }
                        if(sapInvItem.totalValueOfNonSAB !=0.0){
                            deltotalTotalNonSABValue = deltotalTotalNonSABValue + (sapInvItem.totalValueOfNonSAB);
                        }

                    }
                    pdfDocument.printTable(itemTable);
                    //Fix for Duplicate invoice item in print.
                    //AddToPrintedList(sapInvItem);
                    //pdfDocument.addLineSeparator(' ',font);
                    pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
                    //pdfDocument.addLineSeparator('-', font);
                    //sumTable
                    PdfPTable sumTable = new PdfPTable(8);
                    sumTable.setWidths(new float[]{1.7f, 4.5f, 1.5f, 1.5f, 2.5f, 1.0f, 2.5f, 2.5f});
                    sumTable.setWidthPercentage(100);
                    sumTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                    sumTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
                    sumTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
                    sumTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
                    sumTable.addCell(new Paragraph("", font));
                    sumTable.addCell(new Paragraph(SAPInvSummary.delRetSubTotalLabel, font));
                    sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph( deltotalDelQty.intValue()+ "", font)));
                    sumTable.addCell(new Paragraph("", font));
                    sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " +  Utils.formatDouble(deltotalBeerValue, 2), font)));
                    sumTable.addCell(new Paragraph("", font));
                    sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " +  Utils.formatDouble(deltotalDepositValue, 2), font)));
                    sumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " +  Utils.formatDouble(deltotalTotalValue, 2), font)));
                    pdfDocument.printTable(sumTable);
                    pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
                    //pdfDocument.addLineSeparator('-', font);
                }

                //Return Item detail
                List<SAPInvItem> sapInvReturnedItemList = InvoiceController.getSAPInvReturnedItems(delivery.getDELV_NO(), (type == Constants.DocType.InputChecklist));
                if (sapInvReturnedItemList != null && sapInvReturnedItemList.size() > 0) {
                    pdfDocument.printTextLine("Returned ", headerFont, Align.ALIGN_LEFT);
                    PdfPTable returnItemTable = new PdfPTable(8);
                    returnItemTable.setWidths(new float[]{7.5f, 1.3f,1f, 1.9f, 2.3f, 1.5f,2.2f, 2.2f});
                    returnItemTable.setWidthPercentage(100);
                    returnItemTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                    returnItemTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
                    returnItemTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
                    returnItemTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
                    returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("PROD   Description\nCODE", font)));
                    //returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("PROD\nCODE", font)));
                    //returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("Description", font)));
                    returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("Ret.\nCode", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Ret\nQty", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Unit\nPrice", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Beer\nValue.", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Unit\nDept.", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Deposit\nValue", font)));
                    returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph("Total\nValue", font)));


                    for (SAPInvItem sapInvItem : sapInvReturnedItemList) {
                        MATERIAL_HEADER material_header = DBHelper.getInstance().getMaterialList(sapInvItem.getProdCode());
                        if(material_header!=null && customizationheader!=null){
                            if(material_header.getPROD_HIER().startsWith(customizationheader.getKEY_VALUE())){
                                returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+"*"+sapInvItem.prodDesc,31), font)));
                                prodHeirachy = material_header.getPROD_HIER();
                            }else{
                                returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+sapInvItem.prodDesc,31), font)));
                            }
                        }else{
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString((sapInvItem.prodCode==null?ReportHelper.getFixedWidthString(" ",5, ReportHelper.TextAlignment.LEFT):sapInvItem.prodCode)+ReportHelper.getFixedWidthString(" ",2, ReportHelper.TextAlignment.LEFT)+sapInvItem.prodDesc,31), font)));
                        }

                        //returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvItem.prodCode, font)));
                        //returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(Utils.truncateString(sapInvItem.prodDesc,24), font)));
                        returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(sapInvItem.returnCode, font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.delQty != null ? (int)(double)sapInvItem.delQty + "" : "", font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.unitPrice != null ? Utils.formatDouble(sapInvItem.unitPrice, 2) + "" : "", font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.beerValue != null ? Utils.formatDouble(sapInvItem.beerValue, 2) + "" : "", font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.unitDeposit != null ? Utils.formatDouble(sapInvItem.unitDeposit, 2) + "" : "", font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.depositValue != null ? Utils.formatDouble(sapInvItem.depositValue, 2) + "" : "", font)));
                        returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvItem.totalValue != null ? Utils.formatDouble(sapInvItem.totalValue, 2) + "" : "", font)));


                        if (sapInvItem.isZPROSubLine)
                            continue;

                        if (sapInvItem.isZPROLine) {
                            //deltotalBeerValue += sapInvItem.beerValue + sapInvItem.beerValue;
                            //deltotalTotalValue += sapInvItem.totalValue + sapInvItem.beerValue;
                            rettotalBeerValue += sapInvItem.beerValue;
                            rettotalTotalValue += sapInvItem.totalValue;
                            ZPRORunningTotal += sapInvItem.beerValue;
                        } else {
                            rettotalDelQty += (sapInvItem.delQty);
                            rettotalDepositValue += (sapInvItem.depositValue);
                            rettotalBeerValue += (sapInvItem.beerValue);
                            rettotalTotalValue += (sapInvItem.totalValue);
                        }
                        if(sapInvItem.totalValueOfNonSAB !=0.0){
                            deltotalTotalNonSABValue = deltotalTotalNonSABValue + (sapInvItem.totalValueOfNonSAB);
                        }

                        //Bottles and Crates count
                        if(material_header.getMAT_DESC().startsWith("B&CR") && Utils.isAttributeEnabled(customerHeader, Constants.AttributeType.BOTTLESENABLED)) {

                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("        Bottles", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(sapInvItem.delQty.intValue() * Constants.B_CR_BOTTLES_PER_CASE_VALUE), font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));

                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("        Crates", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithRightAlignCell(new Paragraph(String.valueOf(sapInvItem.delQty.intValue()), font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph(" ", font)));
                            returnItemTable.addCell(getParagraphWithLeftAlignCell(new Paragraph("", font)));
                        }

                    }
                    pdfDocument.printTable(returnItemTable);
                    //Fix for Duplicate invoice item in print.
                    //AddToPrintedList(sapInvItem);
                    //pdfDocument.addLineSeparator(' ',font);
                    pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
                    //pdfDocument.addLineSeparator('-', font);
                    //sumTable
                    PdfPTable returnSumTable = new PdfPTable(9);
                    returnSumTable.setWidths(new float[]{ 1.5f,3.7f, 0f,2f, 0f, 2.9f,0f,2.8f, 2f});
                    returnSumTable.setWidthPercentage(100);
                    returnSumTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                    returnSumTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
                    returnSumTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
                    returnSumTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
                    returnSumTable.addCell(new Paragraph("", font));
                    returnSumTable.addCell(new Paragraph(SAPInvSummary.delRetSubTotalLabel, font));
                    returnSumTable.addCell(new Paragraph("", font));
                    returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph(Utils.formatDouble(rettotalDelQty, 2) + "", font)));
                    returnSumTable.addCell(new Paragraph("", font));
                    returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(rettotalBeerValue, 2), font)));
                    returnSumTable.addCell(new Paragraph("", font));
                    returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(rettotalDepositValue, 2), font)));
                    returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(rettotalTotalValue, 2), font)));
                    pdfDocument.printTable(returnSumTable);
                    pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
                    //pdfDocument.addLineSeparator('-', font);
                }

                PdfPTable returnSumTable = new PdfPTable(8);
                returnSumTable.setWidths(new float[]{8f, 2f,1.5f, 0f, 3.2f, 0f,3.9f, 3f});
                returnSumTable.setWidthPercentage(100);
                returnSumTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                returnSumTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);

                returnSumTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
                returnSumTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
                returnSumTable.addCell(new Paragraph(SAPInvSummary.totalSubTotalLabel, font));
                returnSumTable.addCell(new Paragraph("", font));
                returnSumTable.addCell(new Paragraph("", font));
                returnSumTable.addCell(new Paragraph("", font));
                returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble((deltotalBeerValue + rettotalBeerValue), 2), font)));
                returnSumTable.addCell(new Paragraph("", font));
                returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble((deltotalDepositValue + rettotalDepositValue), 2), font)));
                returnSumTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble((deltotalTotalValue + rettotalTotalValue), 2), font)));
                //del d

                pdfDocument.printTable(returnSumTable);

                SAPInvFooter sapInvFooter = InvoiceController.getSAPInvFooter(customerHeader, delivery, deltotalTotalValue + (rettotalTotalValue), currentDepot, ZPRORunningTotal, rettotalBeerValue + (deltotalBeerValue), (type == InputChecklist),deltotalTotalNonSABValue);

                printSAPInvoiceFooter(sapInvFooter);

                if (sapInvFooter.delRetSubTotal.intValue() > 0) {

                    if (sapInvFooter.FLXMsgArray != null && sapInvFooter.FLXMsgArray.length > 0) {
                        if (type == Constants.DocType.InputChecklist) {
                            for (String item : sapInvFooter.FLXMsgArray) {
                                pdfDocument.printTextLine(item, font);
                            }
                        } else {
                            printDiscountTable(sapInvFooter);
                        }
                    }
                }

                if(customizationheader!=null && !prodHeirachy.isEmpty()){
                    if(customizationheader.getKEY_VALUE()!=null){
                        printMsgFor1PMaterials(customizationheader.getKEY_DESC());
                    }
                }

                if (type == Constants.DocType.Invoice) {
                    printInvoiceDriverSignature(sapInvFooter,delivery,customerHeader);
                }

                printNationalMessage(sapInvFooter);

                printRegisteredDistributerMsg(sapInvFooter);

                printTaxInvoiceMsg(sapInvFooter);

                printDisclaimerArray(sapInvFooter);

                //pdfDocument.advance(3);

                //doc.GeneratePdf();
                if (type == Constants.DocType.Invoice) {
                    TimeController.createNewTime(delivery.getINV_NO(), Constants.TIME_TYPE_INV_PDFGEN_START);

                    //Check for if REPRINT from EndofVist AND PDF missing in table for this invoice.
                    ATTACHMENT attachmentRow = AttachmentHelper.getInstance().getAttachment(Constants.ATTACH_TYPE_INVOICE, delivery.getINV_NO(), visitRow.getCUST_NO(), Constants.PDF_TYPE);
                    if (!reprint || (reprint && attachmentRow == null)) {
                        //UnviredDocService docToPDF = doc.DeepCopy();

                    }
                    TimeController.createNewTime(delivery.getINV_NO(), Constants.TIME_TYPE_INV_PDFGEN_END);
                    TimeController.createNewTime(visitRow.getVISIT_NO().toString(), Constants.TIME_TYPE_INV_PRN_START);

                    if (!reprint)
                        TimeController.createNewTime(delivery.getINV_NO(), Constants.TIME_TYPE_INV_PRN_START);
                }


                if (type == Constants.DocType.Invoice && !reprint)
                    TimeController.createNewTime(delivery.getINV_NO(), Constants.TIME_TYPE_INV_PRN_END);


                pdfDocument.advanceWithDots(3, grayFont);

                String pdfPath =pdfDocument.generatePdf();

            // for invoice printing with customer and supplier copy
            if (type == Constants.DocType.Invoice && !reprint && copyType== Constants.CopyType.SUPPLIER) {

                pdfDocument = new PDFDocument(context.getFilesDir() + "", PageSize.A6.getWidth(), 10.0f, 10.0f, 25.0f, 5.0f, font, context.getApplicationContext());
                String supplierCopyPath = pdfDocument.generatePdf();

                try {
                    PdfReader reader = new PdfReader(pdfPath);
                    PdfDictionary dictionary = reader.getPageN(1);
                    PdfObject object = dictionary.getDirectObject(PdfName.CONTENTS);
                    if (object instanceof PRStream) {
                        PRStream stream = (PRStream)object;
                        byte[] data = PdfReader.getStreamBytes(stream);
                        stream.setData(new String(data).replace("CUSTOMER", "SUPPLIER").getBytes());
                    }
                    PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(supplierCopyPath));
                    stamper.close();
                    reader.close();
                } catch (IOException | DocumentException e) {
                    Logger.e("", e);
                }
                if (!Utils.isNullOrEmpty(supplierCopyPath)){
                    PrinterUtils.print(supplierCopyPath, context, callBack,type, Constants.CopyType.SUPPLIER,false,reprint);
                    return;
                }


            }
                    if (!Utils.isNullOrEmpty(pdfPath))
                        PrinterUtils.print(pdfPath, context, callBack,type,copyType,false,reprint);


        }
    }

    private PdfPTable getTableForPayMethodAndVatNo() throws DocumentException {
        PdfPTable table=new PdfPTable(3);
        table.setWidths(new float[]{5f,2f,3f});
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setPaddingLeft(0);
        table.getDefaultCell().setPaddingRight(0);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        return table;

    }

    public static File exportFile(File src, String newFileName, Context context) throws IOException {
        File dst = new File(Environment.getExternalStorageDirectory()+File.separator+"PDF");
        if(!dst.exists())  dst.mkdirs();
        //if folder does not exist
        if (!dst.exists()) {
            if (!dst.mkdir()) {
                return null;
            }
        }

        File expFile = new File(dst.getPath() + File.separator +newFileName );
        FileChannel inChannel = null;
        FileChannel outChannel = null;

        try {
            inChannel = new FileInputStream(src).getChannel();
            outChannel = new FileOutputStream(expFile).getChannel();
        } catch (FileNotFoundException e) {
            Logger.e("", e);
        }

        try {
            inChannel.transferTo(0, inChannel.size(), outChannel);
        } finally {
            if (inChannel != null)
                inChannel.close();
            if (outChannel != null)
                outChannel.close();
        }
        if(expFile!=null)
            Toast.makeText(context, "File has been saved. Path: "+expFile.getAbsolutePath(), Toast.LENGTH_LONG).show();
        return expFile;
    }

    private PdfPCell getParagraphWithRightAlignCell(Paragraph paragraph) {

        PdfPCell cell = new PdfPCell(paragraph);
        cell.setBorderColor(BaseColor.WHITE);
        //cell.setBorderColor(BaseColor.GREEN);
        //cell.setBorder(Rectangle.BOX);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setPaddingLeft(0);
        cell.setPaddingRight(0);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        return cell;

    }
    private PdfPCell getParagraphWithLeftAlignCell(Paragraph paragraph) {

        PdfPCell cell = new PdfPCell(paragraph);
        cell.setBorderColor(BaseColor.WHITE);
        //cell.setBorderColor(BaseColor.GREEN);
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setPaddingLeft(0);
        cell.setPaddingRight(0);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        return cell;

    }

    private boolean isDeclined(String inv_no) throws DBException {
        String query = INVOICE.FIELD_INV_NO + " = '" + inv_no + "'";
        List<INVOICE> invoiceDataTable = DBHelper.getInstance().getInvoiceList(query);
        INVOICE invoice = new INVOICE();
        if (invoiceDataTable != null)
            invoice = invoiceDataTable.get(0);
        if (invoice == null || invoice.getDECLN_RSN() == null || Utils.isNullOrEmpty(invoice.getDECLN_RSN()))
            return false;
        else
            return true;
    }
    void printDisclaimerArray(SAPInvFooter sapInvFooter) throws DocumentException {
        if (sapInvFooter.DisclaimerMsgArray != null && sapInvFooter.DisclaimerMsgArray.length > 0) {
            pdfDocument.addLineSeparator(' ',font);
            for (int k = 0; k < sapInvFooter.DisclaimerMsgArray.length; k++) {
                pdfDocument.printTextLine(sapInvFooter.DisclaimerMsgArray[k], Utils.getFont(9f));
                if (k == 0 || k == sapInvFooter.DisclaimerMsgArray.length - 2)
                    pdfDocument.addLineSeparator(' ',font);
            }
        }
    }
    private void printTaxInvoiceMsg(SAPInvFooter sapInvFooter) throws DocumentException {
        if (sapInvFooter.SARSMsgArray != null && sapInvFooter.SARSMsgArray.length > 0) {
            pdfDocument.addLineSeparator(' ',font);
            for (String item : sapInvFooter.SARSMsgArray) {
                pdfDocument.printTextLine(item, Utils.getFont(9f));
            }
        }
    }
    private void printRegisteredDistributerMsg(SAPInvFooter sapInvFooter) throws DocumentException {
        if (!Utils.isNullOrEmpty(sapInvFooter.NLLAMsg)) {
            pdfDocument.addLineSeparator(' ',font);
            PdfPTable NLLAMsgTable = new PdfPTable(1);
            NLLAMsgTable.setWidthPercentage(100);
            NLLAMsgTable.setHorizontalAlignment(Element.ALIGN_LEFT);
            NLLAMsgTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            NLLAMsgTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
            NLLAMsgTable.addCell(new Paragraph(sapInvFooter.NLLAMsg,font));
            pdfDocument.printTable(NLLAMsgTable);
        }
    }
    private void printNationalMessage(SAPInvFooter sapInvFooter) throws DocumentException {
        if (!Utils.isNullOrEmpty(sapInvFooter.nationalMsg)) {
            pdfDocument.addLineSeparator(' ',font);
            String[] nationalMsgArray = SAPInvFooter.getNationalMsgArray(sapInvFooter.nationalMsg);
            if (nationalMsgArray != null && nationalMsgArray.length > 0) {
                for (String item : nationalMsgArray) {
                    pdfDocument.printTextLine(item, font);
                }
            }
        }
    }

    private void printMsgFor1PMaterials(String keyDescription) throws DocumentException {
            pdfDocument.addLineSeparator(' ',font);
            if(!keyDescription.isEmpty()){
                pdfDocument.printTextLine(keyDescription, font);
            }
    }


    private void printInvoiceDriverSignature(SAPInvFooter sapInvFooter, DELIVERY delivery, CUSTOMER_HEADER customerHeader) throws DocumentException, DBException, IOException {

//      pdfDocument.printTextLine(InvoiceController.getDriverName(sapInvFooter.driver) + "," + SAPInvFooter.customerRatingLabel + (sapInvFooter.customerRatingValue == null ? "" : sapInvFooter.customerRatingValue) + (sapInvFooter.customerRatingDesc == null ? "" : (SAPInvFooter.customerRatingHyphen + sapInvFooter.customerRatingDesc)), font);
//      pdfDocument.addLineSeparator(' ',font);
        if (delivery!=null && !Utils.isNullOrEmpty(delivery.getINV_NO()))
            pdfDocument.printTextLine("Invoice No : " + delivery.getINV_NO(), font);
        if (!Utils.isNullOrEmpty(sapInvFooter.custOrderNo))
            pdfDocument.printTextLine("Customer Order No : " + sapInvFooter.custOrderNo, font);
        if (delivery != null) {
            if ((isDeclined(delivery.getINV_NO()) && !Constants.NOT_NAT_CUSTOMER.equals(customerHeader.getIS_NAT_CUST())) || Constants.NAT_CUSTOMER_NO_GRV.equals(customerHeader.getIS_NAT_CUST())) {
                //doc.PrintTextLine("Date Received :     ", UniFont.Large);
                if (customerHeader.getIS_NAT_CUST() != null && !Utils.isNullOrEmpty(customerHeader.getIS_NAT_CUST())) {
                    pdfDocument.printTextLine("GRN/GRV No :       ", font);
                }
            } else {
                //doc.PrintTextLine("Date Received :     " + DateTime.Now.ToString("dd/MM/yyyy"), UniFont.Small);
                if (customerHeader.getIS_NAT_CUST() != null && !Utils.isNullOrEmpty(customerHeader.getIS_NAT_CUST())
                        && !Constants.NOT_NAT_CUSTOMER.equals(customerHeader.getIS_NAT_CUST())) {
                    pdfDocument.printTextLine("GRN/GRV No :       " + sapInvFooter.GRNNo, font);
                }
            }
        }
        pdfDocument.addLineSeparator(' ',font);
        /*todo signature*/
        if (sapInvFooter.combinedSignatureImageByte != null)
            pdfDocument.printImage(sapInvFooter.combinedSignatureImageByte, 40f, 190f);
        pdfDocument.printTextLine("___________________________         _______________________________", font);
        pdfDocument.printTextLine("Drivers Signature" +ReportHelper.getFixedWidthString(" ",35, ReportHelper.TextAlignment.LEFT)+"Customer Stamp and Signature", font);
        if (isDeclined(delivery.getINV_NO()) || Constants.NAT_CUSTOMER_NO_GRV.equals(customerHeader.getIS_NAT_CUST()))
            pdfDocument.printTextLine("Date Delivered: " + Utils.getStringFromDate(new Date(), "dd/MM/yyyy") +ReportHelper.getFixedWidthString(" ",24, ReportHelper.TextAlignment.LEFT)+ "Date Received: ", font);
        else
            pdfDocument.printTextLine("Date Delivered: " + Utils.getStringFromDate(new Date(), "dd/MM/yyyy") +ReportHelper.getFixedWidthString(" ",24, ReportHelper.TextAlignment.LEFT)+ "Date Received: " + Utils.getStringFromDate(new Date(), "dd/MM/yyyy"), font);
    }

    private void printDiscountTable(SAPInvFooter sapInvFooter) throws DocumentException {

        PdfPTable disTable = new PdfPTable(4);
        disTable.setWidths(new float[]{3f,1.8f,4f,4});
        disTable.setWidthPercentage(68);
        disTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        disTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        disTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        disTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);

        PdfPTable infoTable = new PdfPTable(1);
        infoTable.setWidths(new float[]{10f});
        infoTable.setWidthPercentage(100);
        infoTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        infoTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        infoTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        infoTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);

        int counter = 0;

        for (String item : sapInvFooter.FLXMsgArray) {
            if (counter == 1) {
                //Print table header part

                disTable.addCell(new Paragraph(SAPInvFooter.duedateLabel, font));
                disTable.addCell(new Paragraph(SAPInvFooter.discountPercentageLabel, font));
                disTable.addCell(getParagraphWithRightAlignCell(new Paragraph(SAPInvFooter.discountAmountLabel, font)));
                disTable.addCell(getParagraphWithRightAlignCell(new Paragraph(SAPInvFooter.payableAmountLabel, font)));
                //int padleft = InvoicePrintingStructures.SAPInvFooter.lenDueDate + InvoicePrintingStructures.SAPInvFooter.lenDiscountPercentage + (InvoicePrintingStructures.SAPInvFooter.lenDiscountAmount * -1) + 2;
                //doc.AdvanceLine(1);
                //doc.PrintTextLine(string.Format(new TruncateFormatProvider(), "{0," + (padleft * -1) + "}" + "{1," + (InvoicePrintingStructures.SAPInvFooter.lenPayableAmount + 1) + "}", InvoicePrintingStructures.SAPInvFooter.amountLabel, InvoicePrintingStructures.SAPInvFooter.amountLabel), UniFont.Small);

            }
            if (counter == 0) {
                infoTable.addCell(new Paragraph(item, font));
                pdfDocument.printTable(infoTable);
            } else {
                String[] innerItem = item.split(" ");
                disTable.addCell(new Paragraph(innerItem[0], font));
                disTable.addCell((new Paragraph(Utils.formatDouble(Double.parseDouble(innerItem[1]),2), font)));
                disTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R "+Utils.formatDouble(Double.parseDouble(innerItem[2].replace("R", "")),2), font)));
                disTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R "+Utils.formatDouble(Double.parseDouble(innerItem[3].replace("R", "")),2), font)));
            }

            //pdfDocument.printTextLine(item, font);
            counter++;
        }
        pdfDocument.printTable(disTable);
    }
    private void printSAPInvoiceFooter(SAPInvFooter sapInvFooter) throws DocumentException {
        //print SAPInvFooter
        PdfPTable sapFooterTable = new PdfPTable(6);
        sapFooterTable.setWidths(new float[]{3f, 1f, 1.5f, 4f, 5.5f, 3f});
        sapFooterTable.setWidthPercentage(100);
        sapFooterTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        sapFooterTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        sapFooterTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        sapFooterTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);

        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph(sapInvFooter.delRetSubTotalLabel, font));
        sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.delRetSubTotal, 2), font)));
        if (sapInvFooter.paymentMethodDiscount != 0) {
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph(sapInvFooter.paymentMethodDiscountLabel, font));
            sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.paymentMethodDiscount, 2), font)));
        }

        if (sapInvFooter.cashWithOrderDiscount != 0) {
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph(sapInvFooter.cashWithOrderDiscountLabel, font));
            sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.cashWithOrderDiscount, 2), font)));
        }

        if (sapInvFooter.bulkDiscount != 0) {
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph(sapInvFooter.bulkHandlingLabel, font));
            sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.bulkDiscount, 2), font)));
        }
        //Added NonSchedule Delivery

        //condition for ZFDD type
        if (sapInvFooter.nonScheduleCharges != 0) {
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph("", font));
            sapFooterTable.addCell(new Paragraph(sapInvFooter.nonScheduleDeliveryLabel, font));
            sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.nonScheduleCharges, 2), font)));
        }

        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph(sapInvFooter.subTotalLabel, font));
        sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.subTotal, 2), font)));

        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph(SAPInvFooter.VATAmountLabel+ReportHelper.getFixedWidthString(" ",1, ReportHelper.TextAlignment.LEFT) + sapInvFooter.VATPerc + "%", font));
        sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.VATAmount, 2), font)));

        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph("", font));
        sapFooterTable.addCell(new Paragraph(SAPInvFooter.totalInvoiceValueLabel, font));
        sapFooterTable.addCell(getParagraphWithRightAlignCell(new Paragraph("R " + Utils.formatDouble(sapInvFooter.totalInvoiceValue, 2), font)));

        pdfDocument.printTable(sapFooterTable);
    }

    private void printCompanyLogoAndFirstHeader(DELIVERY delivery, DEPOT_HEADER currentDepot, Context context, boolean reprint, Enum<Constants.DocType> type) throws DocumentException {
        PdfPTable outer = new PdfPTable(3);
        outer.setWidths(new float[]{1.5f,4.5f,4.5f});
        outer.setWidthPercentage(100);
        outer.setHorizontalAlignment(Element.ALIGN_LEFT);
        outer.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        outer.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        outer.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        int w = 42; //This is the max width for company info block we support today.

        String COMP_NAME = DBHelper.getInstance().getStringKeyDesc("COMP_NAME");
        //COMP_NAME = Util.GetPaddedString(COMP_NAME == null ? "" : COMP_NAME, w);

        String COMP_ADR1 = DBHelper.getInstance().getStringKeyDesc("COMP_ADR1");
        //COMP_ADR1 = Util.GetPaddedString(COMP_ADR1 == null ? "" : COMP_ADR1, w);

        String COMP_ADR2 = DBHelper.getInstance().getStringKeyDesc("COMP_ADR2");
        //COMP_ADR2 = Util.GetPaddedString(COMP_ADR2 == null ? "" : COMP_ADR2, w);

        String COMP_REGNO = DBHelper.getInstance().getStringKeyDesc("COMP_REGNO");
        //COMP_REGNO = Util.GetPaddedString(COMP_REGNO == null ? "" : COMP_REGNO, w);

        String COMP_VAT = DBHelper.getInstance().getStringKeyDesc("COMP_VAT");
        //COMP_VAT = Util.GetPaddedString(COMP_VAT == null ? "" : COMP_VAT, w);

        w = 23;
        String hdrLine1 = "CUSTOMER COPY";
        String pdfHdrLine2 = reprint ? "COPY TAX INVOICE" : "TAX INVOICE";
        // String pdfHdrLine2 = "COPY TAX INVOICE"; //always COPY TAX INVOICE for PDF as PDF can be printed again
        if (type == Constants.DocType.ProForma) {
            hdrLine1 = "CUSTOMER";
            //   hdrLine2 = "PRO FORMA";
            pdfHdrLine2 = "PRO FORMA";
        }
        String emptyHdrSpace = " ";
        String hdrLine3 = "Invoice No: " + delivery.getINV_NO();
        //********** PDF START ************************//
        int BORDER_WIDTH = 0;
        //SAB Logo
        //image

        PdfPTable imageTable = new PdfPTable(1);
        imageTable.setWidths(new float[]{2});
        imageTable.setWidthPercentage(15);
        imageTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        imageTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        imageTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        imageTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        try {
            InputStream ims = Utils.getLogoImage(currentDepot,context);
            Bitmap bmp = BitmapFactory.decodeStream(ims);
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            bmp.compress(Bitmap.CompressFormat.PNG, 100, stream);
            imageTable.addCell(Image.getInstance(stream.toByteArray()));
        } catch (IOException e) {
            Logger.e("", e);
        }
        outer.addCell(imageTable);

        PdfPTable table = new PdfPTable(1);
        table.setWidths(new float[]{9f});
        table.setWidthPercentage(60);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);


        table.addCell(new Paragraph(COMP_NAME, font));

        table.addCell(new Paragraph(COMP_ADR1, font));

        table.addCell(new Paragraph(COMP_ADR2, font));

        table.addCell(new Paragraph(COMP_REGNO + "" + emptyHdrSpace, font));

        table.addCell(new Paragraph(COMP_VAT + "" + emptyHdrSpace, font));
        outer.addCell(table);

        PdfPTable rightTable = new PdfPTable(1);
        rightTable.setWidths(new float[]{4f});
        rightTable.setWidthPercentage(35);
        rightTable.setHorizontalAlignment(Element.ALIGN_RIGHT);
        rightTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        rightTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_RIGHT);
        rightTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);
        rightTable.getDefaultCell().setPaddingBottom(6f);

        rightTable.addCell(new Paragraph(ReportHelper.getFixedWidthString(hdrLine1, 25, ReportHelper.TextAlignment.RIGHT), rightHeader));
        rightTable.addCell(new Paragraph(ReportHelper.getFixedWidthString(pdfHdrLine2, 25, ReportHelper.TextAlignment.RIGHT), rightHeader));
        rightTable.addCell(new Paragraph(ReportHelper.getFixedWidthString(hdrLine3, 25, ReportHelper.TextAlignment.RIGHT), rightHeader));

        // Add Barcode below Invoice Number
        try {
            // Generate barcode using the invoice number
            Bitmap barcodeBitmap = ReportHelper.encodeAsBitmap(delivery.getINV_NO(), BarcodeFormat.CODE_128, 200, 40);

            // Convert Bitmap to byte array and create Image instance
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            barcodeBitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
            Image barcodeImage = Image.getInstance(stream.toByteArray());

            barcodeImage.setAlignment(Image.ALIGN_LEFT);
            barcodeImage.scaleToFit(100f, 30f);

            // Add barcode image to the table
            rightTable.addCell(barcodeImage);
        } catch (Exception e) {
            Logger.log(Logger.LEVEL_ERROR, this.getClass().getName(), "printBarcode", Utils.getExceptionMessage(e));
        }

        outer.addCell(rightTable);
        pdfDocument.printTable(outer);
        //********** Printer END ************************//
    }

    private void printHeaderAndShipmentDetail(SAPInvHeader sapInvHeader) throws DocumentException {
        pdfDocument.printTable(Utils.getSpaceSeparatorTable(font));
        //pdfDocument.addLineSeparator('-', font);

        PdfPTable soldToTable = new PdfPTable(3);
        soldToTable.setWidths(new float[]{5.5f,2.3f,3f});
        soldToTable.setWidthPercentage(100);
        soldToTable.setHorizontalAlignment(Element.ALIGN_LEFT);
        //soldToTable.getDefaultCell().setPadding(0);
        soldToTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        soldToTable.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        soldToTable.getDefaultCell().setVerticalAlignment(Element.ALIGN_TOP);

        soldToTable.addCell(new Paragraph(sapInvHeader.legalEntityLabel + " " + sapInvHeader.legalEntAccountNo, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.invDateLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.invDate, font)));
        //2
        soldToTable.addCell(new Paragraph(sapInvHeader.custName, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.accountNoLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.accountNo, font)));
        //3
        soldToTable.addCell(new Paragraph(sapInvHeader.custAddress1, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.bankingRefLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.bankingRef, font)));
        //4

        if (!Utils.isNullOrEmpty(sapInvHeader.custOrderNo)) {
            soldToTable.addCell(new Paragraph(sapInvHeader.custAddress2, font));
            soldToTable.addCell(new Paragraph(sapInvHeader.custOrderNoLabel, font));
            soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.custOrderNo, font)));

        }else{
            soldToTable.addCell(new Paragraph(sapInvHeader.custAddress2, font));
            soldToTable.addCell(new Paragraph(sapInvHeader.custOrderNoLabel, font));
            soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph("", font)));

        }
        //add space
        soldToTable.addCell(new Paragraph(" ", font));
        soldToTable.addCell(new Paragraph(" ", font));
        soldToTable.addCell(new Paragraph(" ", font));

        soldToTable.addCell(new Paragraph(sapInvHeader.custAddress3, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.SABOrderNoLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.SABOrderNo, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.custAddress4, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryNoLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.deliveryNo, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.custAddress5, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.shipmentNoLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.shipmentNo, font)));

        //add space
        soldToTable.addCell(new Paragraph(" ", font));
        soldToTable.addCell(new Paragraph(" ", font));
        soldToTable.addCell(new Paragraph(" ", font));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveredToLabel+" "+sapInvHeader.legalEntAccountNo, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.depotLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.issueDepot, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.custName, font));
        soldToTable.addCell(new Paragraph("", font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.depotAddress, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryAddress1, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.faxTelLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.faxTel, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryAddress2, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.ordersTelLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.ordersTel, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryAddress3, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.accQueriesLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.accQueries, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryAddress4, font));
        soldToTable.addCell(new Paragraph(sapInvHeader.creditControlLabel, font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.creditControl1, font)));

        soldToTable.addCell(new Paragraph(sapInvHeader.deliveryAddress4, font));
        soldToTable.addCell(new Paragraph(" ", font));
        soldToTable.addCell(getParagraphWithRightAlignCell(new Paragraph(sapInvHeader.creditControl2, font)));


        pdfDocument.printTable(soldToTable);

        //id
        pdfDocument.printTextLine(ReportHelper.getFixedWidthString("Mobile User ID: ", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(Utils.getUserId(), 21, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 17, ReportHelper.TextAlignment.RIGHT) + " ", font);
        //version
        pdfDocument.printTextLine(ReportHelper.getFixedWidthString("App Version: ", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(FrameworkVersion.getApplicationVersion(), 21, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString("", 17, ReportHelper.TextAlignment.RIGHT) + " ", font);
        //Driver 1 & Driver 2 already present in /BSA/O2_SHIPMENT.
        String driver1_no = "";
        String driver1_name = "";
        String driver2_no = "";
        String driver2_name = "";

        SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
        driver1_no = Utils.isNullOrEmpty(shipment_header.getDRV1()) ? "" : shipment_header.getDRV1();
        driver1_name = Utils.isNullOrEmpty(shipment_header.getDRV1_NAME()) ? "" : shipment_header.getDRV1_NAME();

        driver2_no = Utils.isNullOrEmpty(shipment_header.getDRV2()) ? "" : shipment_header.getDRV2();
        driver2_name = Utils.isNullOrEmpty(shipment_header.getDRV2_NAME()) ? "" : shipment_header.getDRV2_NAME();
        if(driver1_no != "" && driver1_no != null) {
            pdfDocument.printTextLine(ReportHelper.getFixedWidthString("Driver 1: ", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(driver1_no, 10, ReportHelper.TextAlignment.LEFT) + driver1_name, font);
        }
        if(driver2_no != "" && driver2_no != null) {
            pdfDocument.printTextLine(ReportHelper.getFixedWidthString("Driver 2: ", 20, ReportHelper.TextAlignment.LEFT) + ReportHelper.getFixedWidthString(driver2_no, 10, ReportHelper.TextAlignment.LEFT) + driver2_name, font);
        }
       
    }


}
