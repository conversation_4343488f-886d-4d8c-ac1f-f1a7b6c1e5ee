package com.abinbev.oasis.ModelClasses;

public class DELIVERY_ITEM_PRICING {
    public static final double DEFAULT_TAX_PERCENTAGE = 14;

    //DeliveryItem Attributes
    private String delvNo_ = "";
    private int itemNo_ = 0;
    private String matNo_ = "";
    private String matDesc_ = "";
    private String hhCreated_ = "";
    private String slsUOM_ = "";
    private String slsDeal_ = "";
    private String slsDealDesc_ = "";
    private String slsDealDt_ = "";
    private String promoNo_ = "";
    private String promoDesc_ = "";
    private String promoType_ = "";
    private String promoTypeDesc_ = "";
    private double fbrQty_ = 0;
    private double bitQty_ = 0;
    private double rcrQty_ = 0;
    private double ubrQty_ = 0;
    private double qty_ = 0;
    private String fbrReason_ = "";
    private String bitReason_ = "";
    private int fbrReasonPos_ = 0;
    private String isReturn_ = "";
    private String isEmpty_ = "";

    private double unitPrice_ = 0; //Unit Price before deposit / tax / discount
    private double netValue_ = 0; //Net value
    private double unitDeposit_ = 0.00; //Unit deposit amt
    private double discount_ = 0;
    private double lineTax_ = 0; //Unit tax * Qty
    private double lineValue_ = 0; //Unit price * Qty
    private double lineDeposit_ = 0; //Unit deposit * Qty
    private double lineDepositTax_ = 0; //LineDeposit_ / (LineDeposit_ + LineValue_)) * Line_Tax

    private boolean priced_ = false;
    private double cases_ = 0;
    private double deliveredQty_ = 0;
    private boolean recalculateDisc_ = false;
    private String orderType_ = "";
    private boolean promoApplicable_ = false;
    private double ZPRORate_ = 0;
    private double ZPROQty_ = 0;

    private double ZPMA_ = 0;
    private double ZPMT_ = 0;
    private double ZPMD_ = 0;
    private double ZCWO_ = 0;
    private double ZULL_ = 0;
    private double ZDST_ = 0;
    private double ZPMM_ = 0;
    private double ZPRO_ = 0;

    public static double getDefaultTaxPercentage() {
        return DEFAULT_TAX_PERCENTAGE;
    }

    public String getDelvNo_() {
        return delvNo_;
    }

    public void setDelvNo_(String delvNo_) {
        this.delvNo_ = delvNo_;
    }

    public int getItemNo_() {
        return itemNo_;
    }

    public void setItemNo_(int itemNo_) {
        this.itemNo_ = itemNo_;
    }

    public String getMatNo_() {
        return matNo_;
    }

    public void setMatNo_(String matNo_) {
        this.matNo_ = matNo_;
    }

    public String getMatDesc_() {
        return matDesc_;
    }

    public void setMatDesc_(String matDesc_) {
        this.matDesc_ = matDesc_;
    }

    public String getHhCreated_() {
        return hhCreated_;
    }

    public void setHhCreated_(String hhCreated_) {
        this.hhCreated_ = hhCreated_;
    }

    public String getSlsUOM_() {
        return slsUOM_;
    }

    public void setSlsUOM_(String slsUOM_) {
        this.slsUOM_ = slsUOM_;
    }

    public String getSlsDeal_() {
        return slsDeal_;
    }

    public void setSlsDeal_(String slsDeal_) {
        this.slsDeal_ = slsDeal_;
    }

    public String getSlsDealDesc_() {
        return slsDealDesc_;
    }

    public void setSlsDealDesc_(String slsDealDesc_) {
        this.slsDealDesc_ = slsDealDesc_;
    }

    public String getSlsDealDt_() {
        return slsDealDt_;
    }

    public void setSlsDealDt_(String slsDealDt_) {
        this.slsDealDt_ = slsDealDt_;
    }

    public String getPromoNo_() {
        return promoNo_;
    }

    public void setPromoNo_(String promoNo_) {
        this.promoNo_ = promoNo_;
    }

    public String getPromoDesc_() {
        return promoDesc_;
    }

    public void setPromoDesc_(String promoDesc_) {
        this.promoDesc_ = promoDesc_;
    }

    public String getPromoType_() {
        return promoType_;
    }

    public void setPromoType_(String promoType_) {
        this.promoType_ = promoType_;
    }

    public String getPromoTypeDesc_() {
        return promoTypeDesc_;
    }

    public void setPromoTypeDesc_(String promoTypeDesc_) {
        this.promoTypeDesc_ = promoTypeDesc_;
    }

    public double getFbrQty_() {
        return fbrQty_;
    }

    public void setFbrQty_(double fbrQty_) {
        this.fbrQty_ = fbrQty_;
    }

    public double getBitQty_() {
        return bitQty_;
    }

    public void setBitQty_(double bitQty_) {
        this.bitQty_ = bitQty_;
    }

    public double getRcrQty_() {
        return rcrQty_;
    }

    public void setRcrQty_(double rcrQty_) {
        this.rcrQty_ = rcrQty_;
    }

    public double getUbrQty_() {
        return ubrQty_;
    }

    public void setUbrQty_(double ubrQty_) {
        this.ubrQty_ = ubrQty_;
    }

    public double getQty_() {
        return qty_;
    }

    public void setQty_(double qty_) {
        this.qty_ = qty_;
    }

    public String getFbrReason_() {
        return fbrReason_;
    }

    public void setFbrReason_(String fbrReason_) {
        this.fbrReason_ = fbrReason_;
    }

    public String getBitReason_() {
        return bitReason_;
    }

    public void setBitReason_(String bitReason_) {
        this.bitReason_ = bitReason_;
    }

    public int getFbrReasonPos_() {
        return fbrReasonPos_;
    }

    public void setFbrReasonPos_(int fbrReasonPos_) {
        this.fbrReasonPos_ = fbrReasonPos_;
    }

    public String getIsReturn_() {
        return isReturn_;
    }

    public void setIsReturn_(String isReturn_) {
        this.isReturn_ = isReturn_;
    }

    public String getIsEmpty_() {
        return isEmpty_;
    }

    public void setIsEmpty_(String isEmpty_) {
        this.isEmpty_ = isEmpty_;
    }

    public double getUnitPrice_() {
        return unitPrice_;
    }

    public void setUnitPrice_(double unitPrice_) {
        this.unitPrice_ = unitPrice_;
    }

    public double getNetValue_() {
        return netValue_;
    }

    public void setNetValue_(double netValue_) {
        this.netValue_ = netValue_;
    }

    public double getUnitDeposit_() {
        return unitDeposit_;
    }

    public void setUnitDeposit_(double unitDeposit_) {
        this.unitDeposit_ = unitDeposit_;
    }

    public double getDiscount_() {
        return discount_;
    }

    public void setDiscount_(double discount_) {
        this.discount_ = discount_;
    }

    public double getLineTax_() {
        return lineTax_;
    }

    public void setLineTax_(double lineTax_) {
        this.lineTax_ = lineTax_;
    }

    public double getLineValue_() {
        return lineValue_;
    }

    public void setLineValue_(double lineValue_) {
        this.lineValue_ = lineValue_;
    }

    public double getLineDeposit_() {
        return lineDeposit_;
    }

    public void setLineDeposit_(double lineDeposit_) {
        this.lineDeposit_ = lineDeposit_;
    }

    public double getLineDepositTax_() {
        return lineDepositTax_;
    }

    public void setLineDepositTax_(double lineDepositTax_) {
        this.lineDepositTax_ = lineDepositTax_;
    }

    public boolean isPriced_() {
        return priced_;
    }

    public void setPriced_(boolean priced_) {
        this.priced_ = priced_;
    }

    public double getCases_() {
        return cases_;
    }

    public void setCases_(double cases_) {
        this.cases_ = cases_;
    }

    public double getDeliveredQty_() {
        return deliveredQty_;
    }

    public void setDeliveredQty_(double deliveredQty_) {
        this.deliveredQty_ = deliveredQty_;
    }

    public boolean isRecalculateDisc_() {
        return recalculateDisc_;
    }

    public void setRecalculateDisc_(boolean recalculateDisc_) {
        this.recalculateDisc_ = recalculateDisc_;
    }

    public String getOrderType_() {
        return orderType_;
    }

    public void setOrderType_(String orderType_) {
        this.orderType_ = orderType_;
    }

    public boolean isPromoApplicable_() {
        return promoApplicable_;
    }

    public void setPromoApplicable_(boolean promoApplicable_) {
        this.promoApplicable_ = promoApplicable_;
    }

    public double getZPRORate_() {
        return ZPRORate_;
    }

    public void setZPRORate_(double ZPRORate_) {
        this.ZPRORate_ = ZPRORate_;
    }

    public double getZPROQty_() {
        return ZPROQty_;
    }

    public void setZPROQty_(double ZPROQty_) {
        this.ZPROQty_ = ZPROQty_;
    }

    public double getZPMA_() {
        return ZPMA_;
    }

    public void setZPMA_(double ZPMA_) {
        this.ZPMA_ = ZPMA_;
    }

    public double getZPMT_() {
        return ZPMT_;
    }

    public void setZPMT_(double ZPMT_) {
        this.ZPMT_ = ZPMT_;
    }

    public double getZPMD_() {
        return ZPMD_;
    }

    public void setZPMD_(double ZPMD_) {
        this.ZPMD_ = ZPMD_;
    }

    public double getZCWO_() {
        return ZCWO_;
    }

    public void setZCWO_(double ZCWO_) {
        this.ZCWO_ = ZCWO_;
    }

    public double getZULL_() {
        return ZULL_;
    }

    public void setZULL_(double ZULL_) {
        this.ZULL_ = ZULL_;
    }

    public double getZDST_() {
        return ZDST_;
    }

    public void setZDST_(double ZDST_) {
        this.ZDST_ = ZDST_;
    }

    public double getZPMM_() {
        return ZPMM_;
    }

    public void setZPMM_(double ZPMM_) {
        this.ZPMM_ = ZPMM_;
    }

    public double getZPRO_() {
        return ZPRO_;
    }

    public void setZPRO_(double ZPRO_) {
        this.ZPRO_ = ZPRO_;
    }


}
