package com.abinbev.oasis.ModelClasses;

import android.os.Parcel;
import android.os.Parcelable;

import com.abinbev.oasis.util.Controllers.DBHelper;

public class TRIP_SUMMARY implements Parcelable {

    private int STAT,SEQ_NO;
    private String NAME,CUST_NO,ADDRESS,ACC_MGR,ACC_MGR_TEL,TIME_WIN_FROM,TIME_WIN_TO;
    private float MILE;
    private int VISIT_NO,IS_NAT_CUST;

    public TRIP_SUMMARY(int STAT, int SEQ_NO, String NAME, String CUST_NO, String ADDRESS, String ACC_MGR, String ACC_MGR_TEL, String TIME_WIN_FROM, String TIME_WIN_TO, float MILE, int VISIT_NO, int IS_NAT_CUST) {
        this.STAT = STAT;
        this.SEQ_NO = SEQ_NO;
        this.NAME = NAME;
        this.CUST_NO = CUST_NO;
        this.ADDRESS = ADDRESS;
        this.ACC_MGR = ACC_MGR;
        this.ACC_MGR_TEL = ACC_MGR_TEL;
        this.TIME_WIN_FROM = TIME_WIN_FROM;
        this.TIME_WIN_TO = TIME_WIN_TO;
        this.MILE = MILE;
        this.VISIT_NO = VISIT_NO;
        this.IS_NAT_CUST = IS_NAT_CUST;
    }

    protected TRIP_SUMMARY(Parcel in) {
        STAT = in.readInt();
        SEQ_NO = in.readInt();
        NAME = in.readString();
        CUST_NO = in.readString();
        ADDRESS = in.readString();
        ACC_MGR = in.readString();
        ACC_MGR_TEL = in.readString();
        TIME_WIN_FROM = in.readString();
        TIME_WIN_TO = in.readString();
        MILE = in.readFloat();
        VISIT_NO = in.readInt();
        IS_NAT_CUST = in.readInt();
    }


    public static final Creator<TRIP_SUMMARY> CREATOR = new Creator<TRIP_SUMMARY>() {
        @Override
        public TRIP_SUMMARY createFromParcel(Parcel in) {
            return new TRIP_SUMMARY(in);
        }

        @Override
        public TRIP_SUMMARY[] newArray(int size) {
            return new TRIP_SUMMARY[size];
        }
    };

    public TRIP_SUMMARY() {

    }

    public int getSTAT() {
        return STAT;
    }

    public void setSTAT(int STAT) {
        this.STAT = STAT;
    }

    public int getSEQ_NO() {
        return SEQ_NO;
    }

    public void setSEQ_NO(int SEQ_NO) {
        this.SEQ_NO = SEQ_NO;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public String getCUST_NO() {
        return CUST_NO;
    }

    public void setCUST_NO(String CUST_NO) {
        this.CUST_NO = CUST_NO;
    }

    public String getADDRESS() {
        return ADDRESS;
    }

    public void setADDRESS(String ADDRESS) {
        this.ADDRESS = ADDRESS;
    }

    public String getACC_MGR() {
        return ACC_MGR;
    }

    public void setACC_MGR(String ACC_MGR) {
        this.ACC_MGR = ACC_MGR;
    }

    public String getACC_MGR_TEL() {
        return ACC_MGR_TEL;
    }

    public void setACC_MGR_TEL(String ACC_MGR_TEL) {
        this.ACC_MGR_TEL = ACC_MGR_TEL;
    }

    public String getTIME_WIN_FROM() {
        return TIME_WIN_FROM;
    }

    public void setTIME_WIN_FROM(String TIME_WIN_FROM) {
        this.TIME_WIN_FROM = TIME_WIN_FROM;
    }

    public String getTIME_WIN_TO() {
        return TIME_WIN_TO;
    }

    public void setTIME_WIN_TO(String TIME_WIN_TO) {
        this.TIME_WIN_TO = TIME_WIN_TO;
    }

    public float getMILE() {
        return MILE;
    }

    public void setMILE(float MILE) {
        this.MILE = MILE;
    }

    public int getVISIT_NO() {
        return VISIT_NO;
    }

    public void setVISIT_NO(int VISIT_NO) {
        this.VISIT_NO = VISIT_NO;
    }

    public int getIS_NAT_CUST() {
        return IS_NAT_CUST;
    }

    public void setIS_NAT_CUST(int IS_NAT_CUST) {
        this.IS_NAT_CUST = IS_NAT_CUST;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(STAT);
        dest.writeInt(SEQ_NO);
        dest.writeString(NAME);
        dest.writeString(CUST_NO);
        dest.writeString(ADDRESS);
        dest.writeString(ACC_MGR);
        dest.writeString(ACC_MGR_TEL);
        dest.writeString(TIME_WIN_FROM);
        dest.writeString(TIME_WIN_TO);
        dest.writeFloat(MILE);
        dest.writeInt(VISIT_NO);
        dest.writeInt(IS_NAT_CUST);
    }
}
