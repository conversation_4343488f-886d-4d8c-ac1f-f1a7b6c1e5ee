package com.abinbev.oasis.ModelClasses;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.DELIVERY_ITM_COND;
import com.abinbev.oasis.be.PRICE_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.OasisCache;
import com.abinbev.oasis.util.PricingUtil;
import com.abinbev.oasis.util.Utils;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by kunchok on 28/01/2021
 */
public class DeliveryItemPricing {
    public Double DEFAULT_TAX_PERCENTAGE =14d;

    //DeliveryItem Attributes
    private String delvNo_ = "";
    private int itemNo_ = 0;
    private String matNo_ = "";
    private String matDesc_ = "";
    private String hhCreated_ = "";
    private String slsUOM_ = "";
    private String slsDeal_ = "";
    private String slsDealDesc_ = "";
    private String slsDealDt_ = "";
    private String promoNo_ = "";
    private String promoDesc_ = "";
    private String promoType_ = "";
    private String promoTypeDesc_ = "";
    private Double fbrQty_ = 0d;
    private Double bitQty_ = 0d;
    private Double rcrQty_ = 0d;
    private Double ubrQty_ = 0d;
    private Double qty_ = 0d;
    private String fbrReason_ = "";
    private String bitReason_ = "";
    private int fbrReasonPos_ = 0;
    private String isReturn_ = "";
    private String isEmpty_ = "";

    private Double unitPrice_ = 0d; //Unit Price before deposit / tax / discount
    private Double netValue_ = 0d; //Net value
    private Double unitDeposit_ = Double.valueOf(0.00); //Unit deposit amt
    private Double discount_ = 0d;
    private Double lineTax_ = 0d; //Unit tax * Qty
    private Double lineValue_ = 0d; //Unit price * Qty
    private Double lineDeposit_ = 0d; //Unit deposit * Qty
    private Double lineDepositTax_ = 0d; //LineDeposit_ / (LineDeposit_ + LineValue_)) * Line_Tax

    private boolean priced_ = false;
    private Double cases_ = 0d;
    private Double deliveredQty_ = 0d;
    private boolean recalculateDisc_ = false;
    private String orderType_ = "";
    private boolean promoApplicable_ = false;
    private Double ZPRORate_ = 0d;
    private Double ZPROQty_ = 0d;

    private Double ZPMA_ = 0d;
    private Double ZPMT_ = 0d;
    private Double ZPMD_ = 0d;
    private Double ZCWO_ = 0d;
    private Double ZULL_ = 0d;
    private Double ZDST_ = 0d;
    private Double ZPMM_ = 0d;
    private Double ZPRO_ = 0d;

    public Double getDEFAULT_TAX_PERCENTAGE() {
        return DEFAULT_TAX_PERCENTAGE;
    }

    public void setDEFAULT_TAX_PERCENTAGE(Double DEFAULT_TAX_PERCENTAGE) {
        this.DEFAULT_TAX_PERCENTAGE = DEFAULT_TAX_PERCENTAGE;
    }

    public String getDelvNo_() {
        return delvNo_;
    }

    public void setDelvNo_(String delvNo_) {
        this.delvNo_ = delvNo_;
    }

    public int getItemNo_() {
        return itemNo_;
    }

    public void setItemNo_(int itemNo_) {
        this.itemNo_ = itemNo_;
    }

    public String getMatNo_() {
        return matNo_;
    }

    public void setMatNo_(String matNo_) {
        this.matNo_ = matNo_;
    }

    public String getMatDesc_() {
        return matDesc_;
    }

    public void setMatDesc_(String matDesc_) {
        this.matDesc_ = matDesc_;
    }

    public String getHhCreated_() {
        return hhCreated_;
    }

    public void setHhCreated_(String hhCreated_) {
        this.hhCreated_ = hhCreated_;
    }

    public String getSlsUOM_() {
        return slsUOM_;
    }

    public void setSlsUOM_(String slsUOM_) {
        this.slsUOM_ = slsUOM_;
    }

    public String getSlsDeal_() {
        return slsDeal_;
    }

    public void setSlsDeal_(String slsDeal_) {
        this.slsDeal_ = slsDeal_;
    }

    public String getSlsDealDesc_() {
        return slsDealDesc_;
    }

    public void setSlsDealDesc_(String slsDealDesc_) {
        this.slsDealDesc_ = slsDealDesc_;
    }

    public String getSlsDealDt_() {
        return slsDealDt_;
    }

    public void setSlsDealDt_(String slsDealDt_) {
        this.slsDealDt_ = slsDealDt_;
    }

    public String getPromoNo_() {
        return promoNo_;
    }

    public void setPromoNo_(String promoNo_) {
        this.promoNo_ = promoNo_;
    }

    public String getPromoDesc_() {
        return promoDesc_;
    }

    public void setPromoDesc_(String promoDesc_) {
        this.promoDesc_ = promoDesc_;
    }

    public String getPromoType_() {
        return promoType_;
    }

    public void setPromoType_(String promoType_) {
        this.promoType_ = promoType_;
    }

    public String getPromoTypeDesc_() {
        return promoTypeDesc_;
    }

    public void setPromoTypeDesc_(String promoTypeDesc_) {
        this.promoTypeDesc_ = promoTypeDesc_;
    }

    public Double getFbrQty_() {
        return fbrQty_;
    }

    public void setFbrQty_(Double fbrQty_) {
        this.fbrQty_ = fbrQty_;
    }

    public Double getBitQty_() {
        return bitQty_;
    }

    public void setBitQty_(Double bitQty_) {
        this.bitQty_ = bitQty_;
    }

    public Double getRcrQty_() {
        return rcrQty_;
    }

    public void setRcrQty_(Double rcrQty_) {
        this.rcrQty_ = rcrQty_;
    }

    public Double getUbrQty_() {
        return ubrQty_;
    }

    public void setUbrQty_(Double ubrQty_) {
        this.ubrQty_ = ubrQty_;
    }

    public Double getQty_() {
        return qty_;
    }

    public void setQty_(Double qty_) {
        this.qty_ = qty_;
    }

    public String getFbrReason_() {
        return fbrReason_;
    }

    public void setFbrReason_(String fbrReason_) {
        this.fbrReason_ = fbrReason_;
    }

    public String getBitReason_() {
        return bitReason_;
    }

    public void setBitReason_(String bitReason_) {
        this.bitReason_ = bitReason_;
    }

    public int getFbrReasonPos_() {
        return fbrReasonPos_;
    }

    public void setFbrReasonPos_(int fbrReasonPos_) {
        this.fbrReasonPos_ = fbrReasonPos_;
    }

    public String getIsReturn_() {
        return isReturn_;
    }

    public void setIsReturn_(String isReturn_) {
        this.isReturn_ = isReturn_;
    }

    public String getIsEmpty_() {
        return isEmpty_;
    }

    public void setIsEmpty_(String isEmpty_) {
        this.isEmpty_ = isEmpty_;
    }

    public Double getUnitPrice_() {
        return unitPrice_;
    }

    public void setUnitPrice_(Double unitPrice_) {
        this.unitPrice_ = unitPrice_;
    }

    public Double getNetValue_() {
        return netValue_;
    }

    public void setNetValue_(Double netValue_) {
        this.netValue_ = netValue_;
    }

    public Double getUnitDeposit_() {
        return unitDeposit_;
    }

    public void setUnitDeposit_(Double unitDeposit_) {
        this.unitDeposit_ = unitDeposit_;
    }

    public Double getDiscount_() {
        return discount_;
    }

    public void setDiscount_(Double discount_) {
        this.discount_ = discount_;
    }

    public Double getLineTax_() {
        return lineTax_;
    }

    public void setLineTax_(Double lineTax_) {
        this.lineTax_ = lineTax_;
    }

    public Double getLineValue_() {
        return lineValue_;
    }

    public void setLineValue_(Double lineValue_) {
        this.lineValue_ = lineValue_;
    }

    public Double getLineDeposit_() {
        return lineDeposit_;
    }

    public void setLineDeposit_(Double lineDeposit_) {
        this.lineDeposit_ = lineDeposit_;
    }

    public Double getLineDepositTax_() {
        return lineDepositTax_;
    }

    public void setLineDepositTax_(Double lineDepositTax_) {
        this.lineDepositTax_ = lineDepositTax_;
    }

    public boolean isPriced_() {
        return priced_;
    }

    public void setPriced_(boolean priced_) {
        this.priced_ = priced_;
    }

    public Double getCases_() {
        return cases_;
    }

    public void setCases_(Double cases_) {
        this.cases_ = cases_;
    }

    public Double getDeliveredQty_() {
        return deliveredQty_;
    }

    public void setDeliveredQty_(Double deliveredQty_) {
        this.deliveredQty_ = deliveredQty_;
    }

    public boolean isRecalculateDisc_() {
        return recalculateDisc_;
    }

    public void setRecalculateDisc_(boolean recalculateDisc_) {
        this.recalculateDisc_ = recalculateDisc_;
    }

    public String getOrderType_() {
        return orderType_;
    }

    public void setOrderType_(String orderType_) {
        this.orderType_ = orderType_;
    }

    public boolean isPromoApplicable_() {
        return promoApplicable_;
    }

    public void setPromoApplicable_(boolean promoApplicable_) {
        this.promoApplicable_ = promoApplicable_;
    }

    public Double getZPRORate_() {
        return ZPRORate_;
    }

    public void setZPRORate_(Double ZPRORate_) {
        this.ZPRORate_ = ZPRORate_;
    }

    public Double getZPROQty_() {
        return ZPROQty_;
    }

    public void setZPROQty_(Double ZPROQty_) {
        this.ZPROQty_ = ZPROQty_;
    }

    public Double getZPMA_() {
        return ZPMA_;
    }

    public void setZPMA_(Double ZPMA_) {
        this.ZPMA_ = ZPMA_;
    }

    public Double getZPMT_() {
        return ZPMT_;
    }

    public void setZPMT_(Double ZPMT_) {
        this.ZPMT_ = ZPMT_;
    }

    public Double getZPMD_() {
        return ZPMD_;
    }

    public void setZPMD_(Double ZPMD_) {
        this.ZPMD_ = ZPMD_;
    }

    public Double getZCWO_() {
        return ZCWO_;
    }

    public void setZCWO_(Double ZCWO_) {
        this.ZCWO_ = ZCWO_;
    }

    public Double getZULL_() {
        return ZULL_;
    }

    public void setZULL_(Double ZULL_) {
        this.ZULL_ = ZULL_;
    }

    public Double getZDST_() {
        return ZDST_;
    }

    public void setZDST_(Double ZDST_) {
        this.ZDST_ = ZDST_;
    }

    public Double getZPMM_() {
        return ZPMM_;
    }

    public void setZPMM_(Double ZPMM_) {
        this.ZPMM_ = ZPMM_;
    }

    public Double getZPRO_() {
        return ZPRO_;
    }

    public void setZPRO_(Double ZPRO_) {
        this.ZPRO_ = ZPRO_;
    }

    public void initialize(String orderType, DELIVERY_ITEM deliveryItem)
    {
        itemNo_ = deliveryItem.getITM_NO().intValue();
        matNo_ = Utils.isNullOrEmpty(deliveryItem.getMAT_NO()) ? "" : deliveryItem.getMAT_NO();
        matDesc_ = Utils.isNullOrEmpty(deliveryItem.getMAT_DESC())  ? "" : deliveryItem.getMAT_DESC();
        delvNo_ = deliveryItem.getDELV_NO();
        hhCreated_ = Utils.isNullOrEmpty(deliveryItem.getHH_CREATED())  ? "" : deliveryItem.getHH_CREATED();
        isEmpty_ = Utils.isNullOrEmpty(deliveryItem.getIS_EMPTY())  ? "" : deliveryItem.getIS_EMPTY();
        isReturn_ = Utils.isNullOrEmpty(deliveryItem.getIS_RETURN())  ? "" : deliveryItem.getIS_RETURN();
        promoDesc_ = Utils.isNullOrEmpty(deliveryItem.getPROMO_DESC())  ? "" : deliveryItem.getPROMO_DESC();
        promoNo_ = Utils.isNullOrEmpty(deliveryItem.getPROMO_NO())  ? "" : deliveryItem.getPROMO_NO();
        promoType_ = Utils.isNullOrEmpty(deliveryItem.getPROMO_TYPE())  ? "" : deliveryItem.getPROMO_TYPE();
        promoTypeDesc_ = Utils.isNullOrEmpty(deliveryItem.getPROMO_TYPE_DESC())  ? "" : deliveryItem.getPROMO_TYPE_DESC();

        slsDeal_ = Utils.isNullOrEmpty(deliveryItem.getSLS_DEAL())  ? "" : deliveryItem.getSLS_DEAL();
        slsDealDesc_ = Utils.isNullOrEmpty(deliveryItem.getSLS_DEAL_DESC())  ? "" : deliveryItem.getSLS_DEAL_DESC();
        slsDealDt_ = Utils.isNullOrEmpty(deliveryItem.getSLS_DEAL_DT())  ? "" : deliveryItem.getSLS_DEAL_DT();
        slsUOM_ = Utils.isNullOrEmpty(deliveryItem.getSLS_UOM())  ? "" : deliveryItem.getSLS_UOM();

        orderType_ = orderType;
        qty_ = (Utils.isNullOrEmpty(deliveryItem.getQTY().toString())  ? 0.00d : deliveryItem.getQTY());

        if (!Utils.isNullOrEmpty(isEmpty_) && !Utils.isNullOrEmpty(isReturn_) && (rcrQty_ > 0))
        {
            recalculateDisc_ = (!rcrQty_.equals(deliveredQty_));
            deliveredQty_ = rcrQty_;
        }
        else
        {
            Double newQuantity = (Utils.isNullOrEmpty(String.valueOf(deliveryItem.getQTY())) ? 0.00d : deliveryItem.getQTY());
            recalculateDisc_ = (!newQuantity.equals(deliveredQty_));
            deliveredQty_ = newQuantity;
        }

        CalculatePricing();
    }

    private void CalculatePricing()
    {
        //If already priced then no need to read delivery conditions and recalculate, instead only do linear changes with qty.
        if (!recalculateDisc_ && (priced_ || (orderType_ != null && orderType_.equals(Constants.ORDER_TYPE.ZFID.toString()))))
        {
            lineValue_ = (unitPrice_*(deliveredQty_));
            lineDeposit_ = unitDeposit_*(deliveredQty_);
            //If Not (Me.LineValue_ + Me.LineDeposit_) = 0 AndAlso Me.lineDepositTax__ = 0 Then
            //    Me.lineDepositTax__ = Common.DecimalRound((Me.LineDeposit_ / (Me.LineValue_ + Me.LineDeposit_)) * Me.LineTax_, 2)
            //End If
            //if ((lineValue_ + lineDeposit_) != 0)
            //{
            //    lineDepositTax_ = PricingUtil.DecimalRound((lineDeposit_ / (lineValue_ + lineDeposit_)) * (float)lineTax_, 2);
            //}
            //else if ((lineValue_ + lineDeposit_) == 0 && lineDepositTax_ != 0)
            //{
            //    lineDepositTax_ = 0;
            //}

            netValue_ = lineValue_+(lineDeposit_) ;

            return;
        }

        Double CPP = 0.00d;
        Double unitValue = 0.00d;
        Double local;
        recalculateDisc_ = false;

        if (OasisCache.CasesPerPallet != null && OasisCache.CasesPerPallet.size() > 0 && OasisCache.CasesPerPallet.containsKey(matNo_))
        {
            CPP = Double.valueOf((OasisCache.CasesPerPallet.get(matNo_)));
        }

        List<DELIVERY_ITM_COND> ItemConditions = DeliveryController.getInstance().getDeliveryItmConditions(delvNo_);

        List<DELIVERY_ITM_COND> deliveryItmCond = new ArrayList<>();
        if (ItemConditions != null && ItemConditions.size() > 0)
        {
            for(DELIVERY_ITM_COND cond: ItemConditions){
                if(cond.getITM_NO().intValue()==(itemNo_)){
                    deliveryItmCond.add(cond);
                }
            }
        }

        if (Utils.isNullOrEmpty(hhCreated_))
        {
            if (slsUOM_.equals("PAL"))
            {
                cases_ = deliveredQty_*(CPP);
            }
            else
            {
                cases_ = deliveredQty_;
            }

            if (deliveryItmCond != null && deliveryItmCond.size() > 0)
            {
                List<DELIVERY_ITM_COND> zbasConditionRow = new ArrayList<>();
                for(DELIVERY_ITM_COND cond:deliveryItmCond) {
                    if(Constants.ZBAS.equals(cond.getCOND_TYPE_ID())){
                        zbasConditionRow.add(cond);
                    }
                }


                //Calculate the unit price first and then proceed to calculate other discounts
                if (null != zbasConditionRow && zbasConditionRow.size()>0)
                {
                   DELIVERY_ITM_COND ZBASItemCondition = zbasConditionRow.get(0);

                    if (qty_ == 0)
                    {
                        unitValue = (Utils.isNullOrEmpty(String.valueOf(ZBASItemCondition.getVAL())) ? 0 : ZBASItemCondition.getVAL());
                        if (deliveredQty_ > 0)
                            unitPrice_ =Utils.round(unitValue/(deliveredQty_),2) ;
                    }
                    else
                    {
                        if (qty_ > 0)
                            unitPrice_ = Utils.round(Utils.isNullOrEmpty(String.valueOf(ZBASItemCondition.getVAL())) ?0d: (ZBASItemCondition.getVAL())/(qty_),2);
                            unitValue = Utils.round(unitPrice_/(deliveredQty_),2);
                    }
                    local = 0d;
                }

                for (DELIVERY_ITM_COND itemCondition : deliveryItmCond)
                {
                    if (itemCondition.getCOND_TYPE_ID() == null)
                        continue;

                    switch (itemCondition.getCOND_TYPE_ID())
                    {
                        //case OASISConstants.ZBAS:
                        //    if (qty_ == 0)
                        //    {
                        //        unitValue = itemCondition.VAL;
                        //        if (deliveredQty_ > 0)
                        //            unitPrice_ = unitValue / deliveredQty_;
                        //    }
                        //    else
                        //    {
                        //        if (qty_ > 0)
                        //            unitPrice_ = itemCondition.VAL / qty_;
                        //        unitValue = unitPrice_ * deliveredQty_;
                        //    }
                        //    local = 0;
                        //    break;

                        case Constants.ZPMA:
                            ZPMA_ = Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_)), 2);
                            break;
                        case Constants.ZPMT:
                            ZPMT_ =Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_)), 2);
                            break;
                        case Constants.ZPMD:
                            ZPMD_ = Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_)), 2);
                            break;
                        case Constants.ZCWO:
                            ZCWO_ =Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_)), 2);
                            break;
                        case Constants.ZPMM:
                            ZPMM_ = Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_)), 2);
                            break;
                        case Constants.ZPRO:
                            if (Math.abs(itemCondition.getRATE()) > 0)
                            {
                                ZPROQty_ =(itemCondition.getVAL() / itemCondition.getRATE());

                                if (deliveredQty_ != 0 && ZPROQty_> getQty_())
                                {
                                    ZPROQty_= getQty_();

                                    //ZPROQty_ = QTY;
                                    //setQty_(ZPROQty_);
                                }
                            }
                            else
                            {
                                ZPROQty_ =0d;
                            }
                            ZPRO_ = Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), false, CPP)*(ZPROQty_)), 2);
                            promoApplicable_ = true;
                            ZPRORate_ =(itemCondition.getRATE());
                            break;
                        case Constants.ZULL:
                            ZULL_ = Utils.round((PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), true, CPP)*(deliveredQty_) ), 2);
                            break;
                        case Constants.ZDST:
                            if (itemCondition.getCOND_UNIT().equals("PAL") || itemCondition.getCOND_UNIT().equals("ZP2"))
                                ZDST_ =  Utils.round((Utils.round(deliveredQty_ /(CPP), 3)*((itemCondition.getRATE()))), 2);
                            else
                                ZDST_ =  Utils.round((itemCondition.getRATE())*( deliveredQty_), 2);
                            break;
                        case Constants.ZDEP:
                            local = PricingUtil.calcConditionValue(unitPrice_, itemCondition.getCOND_UNIT(),(itemCondition.getRATE()), false, CPP);
                            unitDeposit_ =  Utils.round(local, 2);
                            break;
                    }
                }

                if (!Utils.isNullOrEmpty(orderType_) && orderType_.equals(Constants.ORDER_TYPE.ZUBR.toString()))
                {
                    // UBR deliveries, the discounts are positive
                    ZULL_ = (double) (Math.abs(ZULL_));
                    ZDST_ = (double) (Math.abs(ZDST_));
                    unitDeposit_ = (double) (Math.abs(unitDeposit_) * -1);
                }
                else
                {
                    // All other deliveries, the discounts are negative
                    ZULL_ = (double) (Math.abs(ZULL_) * -1);
                    ZDST_ = (double) (Math.abs(ZDST_) * -1);
                }
                discount_ = ZPMA_+(ZPMT_)+(ZPMD_)+( ZPMM_)+(ZULL_)+(ZDST_)+( ZCWO_)+(ZPRO_);
            }
            if (deliveryItmCond != null && deliveryItmCond.size() > 0)
            {
                List<DELIVERY_ITM_COND> mwstRows = new ArrayList<>();
                for(DELIVERY_ITM_COND cond:deliveryItmCond) {
                    if(cond.getCOND_TYPE_ID().equals(Constants.MWST)){
                        mwstRows.add(cond);
                    }
                }
                if (mwstRows != null && mwstRows.size() > 0 && mwstRows.get(0) != null)
                {
                    local = PricingUtil.calcConditionValue((unitPrice_+(unitDeposit_))*(deliveredQty_)+(discount_), slsUOM_,(mwstRows.get(0).getRATE()), true, CPP);
                }
                else
                {
                    local = PricingUtil.calcConditionValue((unitPrice_+(unitDeposit_))*(deliveredQty_)+(discount_), slsUOM_, DEFAULT_TAX_PERCENTAGE, true, CPP);
                }
            }
            else
            {
                local = PricingUtil.calcConditionValue((unitPrice_+(unitDeposit_))*(deliveredQty_)+(discount_), slsUOM_, DEFAULT_TAX_PERCENTAGE, true, CPP);
            }
            lineTax_ = Utils.round(local, 2);
            //The unitprice is now in CASES, convert to original UOM
            if (slsUOM_.equals("PAL"))
            {
                unitPrice_ = unitPrice_*(CPP) ;
                unitDeposit_ = unitDeposit_*(CPP);
            }
        }
        else
        {
            //Empty Returns
            Double otherDeliveryItemsTaxValue = 0d;
            if (deliveryItmCond != null && deliveryItmCond.size() > 0)
            {
                //try to find corresponding ItemCondition
                List<DELIVERY_ITM_COND> ItemConditionRows = new ArrayList<>();
                for(DELIVERY_ITM_COND cond:deliveryItmCond) {
                    if(cond.getCOND_TYPE_ID().equals(Constants.MWST) && cond.getRATE()!=null && cond.getRATE()>0){
                        ItemConditionRows.add(cond);
                    }
                }
                if (ItemConditionRows != null && ItemConditionRows.size() > 0 && ItemConditionRows.get(0) != null)
                {
                    otherDeliveryItemsTaxValue = (ItemConditionRows.get(0).getRATE());
                }
            }
            if (otherDeliveryItemsTaxValue == 0)
            {
                otherDeliveryItemsTaxValue = DeliveryController.getMWSTDeliveryItemConditionRate();
            }
            if (otherDeliveryItemsTaxValue == 0)
            {
                otherDeliveryItemsTaxValue = DEFAULT_TAX_PERCENTAGE;
            }
            List<PRICE_HEADER> priceHeaderDataTable = PricingUtil.getPrices();
            PRICE_HEADER priceHeader = null;
            if (priceHeaderDataTable != null && priceHeaderDataTable.size() > 0)
            {
                List<PRICE_HEADER> priceHeaders = new ArrayList<>();
                for(PRICE_HEADER price_header:priceHeaderDataTable) {
                    if(price_header.getMAT_NO().equals(matNo_)){
                        priceHeaders.add(price_header);
                    }
                }
                if (priceHeaders != null && priceHeaders.size() > 0)
                {
                    priceHeader = priceHeaders.get(0);
                }
            }

            if (Constants.GlassSplit)
            {
                //this._MaterialIsSplit = this.CalculateSplitValues(gMaterial, otherDeliveryItemsTaxValue);
                lineTax_ = lineDeposit_;
                if (unitDeposit_ == 0)
                {
                    if (priceHeader != null)
                    {
                        // Unit Price is only the beer value (no beer value on leers) not the deposit value
                        //unitPrice__ = DecimalRound(gPrice.Price, 2) ' This is to combat banker's rounding (accomodate SAP) as found in Round(gPrice.Price, 2)
                        unitDeposit_ = Utils.round((priceHeader.getPRICE()), 2);
                        // This is to combat banker's rounding (accomodate SAP) as found in Round(gPrice.Price, 2)
                        lineTax_ = Utils.round((PricingUtil.calcConditionValue(unitDeposit_, priceHeader.getUOM(), otherDeliveryItemsTaxValue, true, CPP)*(deliveredQty_)), 2);
                    }
                }
            }
            else
            {
                for (PRICE_HEADER price : priceHeaderDataTable)
                {
                    if (priceHeader != null)
                    {
                        // Unit Price is only the beer value (no beer value on leers) not the deposit value
                        //unitPrice__ = DecimalRound(gPrice.Price, 2) ' This is to combat banker's rounding (accomodate SAP) as found in Round(gPrice.Price, 2)
                        unitDeposit_ = Utils.round((priceHeader.getPRICE()), 2);
                        // This is to combat banker's rounding (accomodate SAP) as found in Round(gPrice.Price, 2)
                        //this._EmptyCrateDepositValue = (float)unitDeposit * deliveryItem.QTY;
                        lineTax_ = Utils.round((PricingUtil.calcConditionValue(unitDeposit_, priceHeader.getUOM(), otherDeliveryItemsTaxValue, true, CPP)*(deliveredQty_)), 2);
                        //this._EmptyCrateDepositTax = lineTax;
                    }
                }
            }
        }

        priced_ = true;
    }

    public void setActualQty(DELIVERY_ITEM deliveryItem) {
        //UBR quantity change
        slsDeal_ = Utils.isNullOrEmpty( deliveryItem.getSLS_DEAL()) ? "" : deliveryItem.getSLS_DEAL();

        ubrQty_ =(deliveryItem.getUBR_QTY()==null ? 0 : deliveryItem.getUBR_QTY());
        rcrQty_ =(deliveryItem.getRCR_QTY()==null ? 0 : deliveryItem.getRCR_QTY());
        qty_ =(deliveryItem.getQTY()==null ? 0 : deliveryItem.getQTY());
        fbrQty_ =(deliveryItem.getFBR_QTY()==null ? 0 : (deliveryItem.getFBR_QTY()));
        fbrReason_ = Utils.isNullOrEmpty(deliveryItem.getFBR_RSN()) ? "" : deliveryItem.getFBR_RSN();
        fbrReasonPos_ = deliveryItem.getFBR_RSN_POS()==null ? 0 : deliveryItem.getFBR_RSN_POS().intValue();
        bitQty_ =(deliveryItem.getBIT_QTY()==null ? 0 : (deliveryItem.getBIT_QTY()));
        bitReason_ = Utils.isNullOrEmpty(deliveryItem.getBIT_RSN()) ? "" : deliveryItem.getBIT_RSN();

        if (!Utils.isNullOrEmpty(isReturn_) && !Utils.isNullOrEmpty(orderType_) && orderType_.equals(Constants.ORDER_TYPE.ZUBR.toString()))
        {
            recalculateDisc_ = (!ubrQty_.equals(deliveredQty_));
            deliveredQty_ = ubrQty_;
        }
        else
        {
            if (!Utils.isNullOrEmpty(isEmpty_))
            {
                if (rcrQty_ > 0)
                {
                    recalculateDisc_ = (!rcrQty_.equals(deliveredQty_));
                    deliveredQty_ = rcrQty_;
                }
                else
                {
                    //pallet
                    recalculateDisc_ = (!qty_.equals(deliveredQty_));
                    deliveredQty_ = qty_;
                }
            }
            else
            {
                Double newQuantity = qty_-(fbrQty_+(bitQty_));
                recalculateDisc_ = (!newQuantity.equals(deliveredQty_));
                deliveredQty_ = newQuantity;
            }
        }
        CalculatePricing();
    }
}
