package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.IMAGE_HEADER;
import com.abinbev.oasis.be.MATERIAL_HEADER;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.ImageUtil;
import com.bumptech.glide.Glide;
import com.abinbev.oasis.R;

import java.util.List;

public class MaterialDroDownAdapter extends ArrayAdapter<MATERIAL_HEADER> {
    private Context mContext;
    private List<MATERIAL_HEADER> materialHeaderList;
    private boolean showPlaceholder;
    private boolean showImage;
    public MaterialDroDownAdapter(@NonNull Context context, int resource, @NonNull List<MATERIAL_HEADER> objects,boolean showPlaceholder,boolean showImage) {
        super(context, resource, objects);
        this.materialHeaderList=objects;
        this.mContext=context;
        this.showPlaceholder=showPlaceholder;
        this.showImage=showImage;
    }

    @Override
    public int getCount()
    {
        if(showPlaceholder){
            return materialHeaderList.size()+1;
        }else {
            return materialHeaderList.size();
        }
    }

    @Nullable
    @Override
    public MATERIAL_HEADER getItem(int position) {
        if(showPlaceholder){
            if(position==0){
                return null;
             }else {
                return materialHeaderList.get(position-1);

            }
        }else {
            return materialHeaderList.get(position);
        }

    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
            if(!showPlaceholder) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
                TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
                TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
                ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
                View mDiv = convertView.findViewById(R.id.xProductDropDownDivider);
                RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
                convertView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                mDiv.setVisibility(View.GONE);
                mRootView.setBackgroundResource(R.color.question_bg_color);
                mMatDes.setTextColor(mContext.getResources().getColor(R.color.white));
                mMatNo.setTextColor(mContext.getResources().getColor(R.color.white));
                if(showImage) {
                    IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(materialHeaderList.get(position).getMAT_NO());
                    if (imageHeader != null) {
                        Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                    } else {
                        Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                    }
                }else {
                    mImg.setVisibility(View.GONE);
                }
                mMatNo.setText(materialHeaderList.get(position).getMAT_NO());
                mMatDes.setText(materialHeaderList.get(position).getMAT_DESC());

                return convertView;
            }else {
                if(position==0){
                    convertView=LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                    TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                    label.setTextColor(Color.BLACK);
                    View mDiv = convertView.findViewById(R.id.xReasonDropDownDiv);
                    mDiv.setVisibility(View.GONE);

                    return convertView;
                }else {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
                    TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
                    TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
                    ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
                    View mDiv = convertView.findViewById(R.id.xProductDropDownDivider);
                    RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
                    convertView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                    mDiv.setVisibility(View.GONE);
                    mRootView.setBackgroundResource(R.color.question_bg_color);
                    mMatDes.setTextColor(mContext.getResources().getColor(R.color.white));
                    mMatNo.setTextColor(mContext.getResources().getColor(R.color.white));
                    if(showImage) {
                        IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(materialHeaderList.get(position-1).getMAT_NO());
                        if (imageHeader != null) {
                            Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                        } else {
                            Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                        }
                    }else {
                        mImg.setVisibility(View.GONE);
                    }
                    mMatNo.setText(materialHeaderList.get(position-1).getMAT_NO());
                    mMatDes.setText(materialHeaderList.get(position-1).getMAT_DESC());

                    return convertView;
                }
            }

    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        if(!showPlaceholder) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
            TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
            TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
            ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
            RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
            if(showImage) {
                IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(materialHeaderList.get(position).getMAT_NO());
                if (imageHeader != null) {
                    Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                } else {
                    Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                }
            }else {
                mImg.setVisibility(View.GONE);
            }
            mMatNo.setText(materialHeaderList.get(position).getMAT_NO());
            mMatDes.setText(materialHeaderList.get(position).getMAT_DESC());
            if (position == mSelectedIndex) {
                mRootView.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            } else {
                mRootView.setBackgroundResource(R.color.white);
            }
            return convertView;
        }else {
            if(position==0){
                convertView=LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setText("Select product");
                label.setTypeface(null, Typeface.BOLD);
                label.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);
                label.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                return convertView;
            }else {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
                TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
                TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
                ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
                RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
                if(showImage) {
                    IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(materialHeaderList.get(position-1).getMAT_NO());
                    if (imageHeader != null) {
                        Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                    } else {
                        Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                    }
                }else {
                    mImg.setVisibility(View.GONE);
                }
                mMatNo.setText(materialHeaderList.get(position-1).getMAT_NO());
                mMatDes.setText(materialHeaderList.get(position-1).getMAT_DESC());
                if (position == mSelectedIndex) {
                    mRootView.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootView.setBackgroundResource(R.color.white);
                }
                return convertView;
            }
        }
    }

    private int mSelectedIndex = -1;
    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }
}
