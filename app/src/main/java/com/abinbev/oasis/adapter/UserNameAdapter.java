package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.TwoLineListItem;

import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.R;

import java.util.List;

public class UserNameAdapter extends ArrayAdapter<AUTH_HEADER> {

    // Your sent context
    private Context context;
    // Your custom values for the spinner (User)
    private List<AUTH_HEADER> values;
    private int mSelectedIndex = -1;

    public UserNameAdapter(Context context, int textViewResourceId,
                       List<AUTH_HEADER> values) {
        super(context, textViewResourceId, values);
        this.context = context;
        this.values = values;
    }

    @Override
    public int getCount(){
        return values.size();
    }

    @Override
    public AUTH_HEADER getItem(int position){
        return values.get(position);
    }

    @Override
    public long getItemId(int position){
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        LinearLayout linearLayout;

        if (convertView == null) {
            LayoutInflater inflater = (LayoutInflater) context
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            linearLayout = (LinearLayout) inflater.inflate(
                    R.layout.username_spinner_dropdown_item, null);
        } else {
            linearLayout = (LinearLayout) convertView;
        }
        TextView text1 = linearLayout.findViewById(R.id.auth_name);
        TextView text2 = linearLayout.findViewById(R.id.auth_id);

        text1.setText(values.get(position).getNAME());
        text2.setText(values.get(position).getNAME());
        text2.setVisibility(View.GONE);

        return linearLayout;
    }

    // And here is when the "chooser" is popped up
    // Normally is the same view, but you can customize it if you want
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {

        LinearLayout linearLayout;

        if (convertView == null) {
            LayoutInflater inflater = (LayoutInflater) context
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            linearLayout = (LinearLayout) inflater.inflate(
                    R.layout.username_spinner_dropdown_item, null);
        } else {
            linearLayout = (LinearLayout) convertView;
        }
        TextView text1 = linearLayout.findViewById(R.id.auth_name);
        TextView text2 = linearLayout.findViewById(R.id.auth_id);

        text1.setText(values.get(position).getNAME());
        text2.setText(values.get(position).getUSER_ID());
        text1.setTextColor(Color.WHITE);
        text2.setTextColor(Color.WHITE);

        if (position == mSelectedIndex) {
            linearLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
        } else {
            linearLayout.setBackgroundResource(R.color.depot_list_item_background);
        }

        return linearLayout;
    }

    @Override
    public boolean isEnabled(int position) {
        if (position == 0) {
            // Disable the first item from Spinner
            // First item will be use for hint
            return false;
        } else {
            return true;
        }
    }

    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }

}