package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.activities.ScanCustomerPodClaimActivity;
import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.bumptech.glide.Glide;
import com.abinbev.oasis.R;

import java.util.List;

public class PODClaimSummaryAdapter extends RecyclerView.Adapter<PODClaimSummaryAdapter.ViewHolder> {
    Activity mContext;
    List<DELIVERY> deliveryItemList;
    private boolean[] itemDoneStatus;

    public PODClaimSummaryAdapter(Activity mContext, List<DELIVERY> deliveryItemList) {
        this.mContext = mContext;
        this.deliveryItemList = deliveryItemList;
        this.itemDoneStatus = new boolean[deliveryItemList.size()];
    }

    @NonNull
    @Override
    public PODClaimSummaryAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.podclaim_summary_single_item, parent, false);
        return new PODClaimSummaryAdapter.ViewHolder(v);
    }

    public boolean areAllItemsDone() {
        for (boolean status : itemDoneStatus) {
            if (!status) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void onBindViewHolder(@NonNull PODClaimSummaryAdapter.ViewHolder holder, int position) {
        final DELIVERY delivery = deliveryItemList.get(position);
        holder.mDeliveryNo.setText(delivery.getDELV_NO());
        holder.mSapNumber.setText(delivery.getORD_NO()==null?"":delivery.getORD_NO());
        itemDoneStatus[position] = (("X").equalsIgnoreCase(delivery.getBLANK_TYPE()) || ("X").equalsIgnoreCase(delivery.getIS_FBR()));
        if (("X").equalsIgnoreCase(delivery.getBLANK_TYPE())) {
            holder.mRootView.setCardBackgroundColor(mContext.getResources().getColor(R.color.green));
            Glide.with(mContext).load(R.drawable.task_completed).into(holder.mImg);
        } else {
            holder.mRootView.setCardBackgroundColor(mContext.getResources().getColor(R.color.red));
            Glide.with(mContext).load(R.drawable.ic_white_arrow).into(holder.mImg);
        }
            holder.mRelativeLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    DeliveryController.getInstance().setDeliveryNoFromAdapter(delivery.getDELV_NO());
                    Intent intent = new Intent(mContext, ScanCustomerPodClaimActivity.class);
                    intent.putExtra("DeliveryNo", delivery.getDELV_NO());
                    intent.putExtra("DeliverySize", deliveryItemList.size());
                    mContext.startActivity(intent);
                }
            });

    }

    @Override
    public int getItemCount() {
        return deliveryItemList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView mDeliveryNo, mSapNumber;
        ImageView mImg;
        CardView mRootView;
        RelativeLayout mRelativeLayout;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mDeliveryNo = itemView.findViewById(R.id.xPODClaimSummaryDeliveryNo);
            mRelativeLayout = itemView.findViewById(R.id.xPODClaimSummarySingleItemBg);
            mSapNumber = itemView.findViewById(R.id.xPODClaimSummarySapNumber);
            mRootView = itemView.findViewById(R.id.xPODClaimSingleItemRootLayout);
            mImg = itemView.findViewById(R.id.xPodClaimSingleItemGoImg);

        }
    }

    private void navigateToScanCustomerPODClaim() {
        mContext.startActivity(new Intent(mContext, ScanCustomerPodClaimActivity.class));
    }

}
