package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.unvired.database.DBException;
import com.unvired.oasis.R;

import java.util.List;

public class DeliveryDropDownAdapter extends ArrayAdapter<DELIVERY> {
    private Context mContext;
    private List<DELIVERY> deliveries;
    private boolean showPlaceHolder;
    public DeliveryDropDownAdapter(@NonNull Context context, int resource, @NonNull List<DELIVERY> objects,boolean showPlaceHolder) {
        super(context, resource, objects);
        this.deliveries=objects;
        this.mContext=context;
        this.showPlaceHolder=showPlaceHolder;
    }

    @Override
    public int getCount() {
        if (deliveries != null && deliveries.size() >= 1) {
            if (showPlaceHolder) {
                return deliveries.size() + 1;
            } else {
                return deliveries.size();
            }
        } else {
            return 0;
        }
    }


    @Nullable
    @Override
    public DELIVERY getItem(int position) {
        if(showPlaceHolder){
            if(position==0){
                return null;
            }else {
                return deliveries.get(position-1);

            }
        }else {
            return deliveries.get(position);
        }

    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        if(!showPlaceHolder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
            mRootLayout.setBackgroundResource(R.color.table_bg_color);
            mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            label.setTextColor(Color.WHITE);
            label.setGravity(Gravity.CENTER_VERTICAL);
            label.setText(deliveries.get(position).getDELV_NO());
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
               // TextView label = (TextView) super.getView(position, convertView, parent);
                label.setBackgroundResource(R.color.white);
                RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
                params.setMargins(2,2,2,2);
                label.setLayoutParams(params);
                label.setTextColor(Color.WHITE);
                label.setText("");
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                mRootLayout.setBackgroundResource(R.color.table_bg_color);
                label.setGravity(Gravity.CENTER_VERTICAL);
                mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                label.setTextColor(Color.WHITE);
                label.setText(deliveries.get(position-1).getDELV_NO());
                return convertView;
            }
        }
    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        if(!showPlaceHolder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
            label.setTextColor(Color.BLACK);
            label.setText(deliveries.get(position).getDELV_NO());
            if (position == mSelectedIndex) {
                mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            } else {
                mRootLayout.setBackgroundResource(R.color.white);
            }
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setText("Select delivery");
                label.setTypeface(null, Typeface.BOLD);
                label.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);
                label.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                label.setText(deliveries.get(position-1).getDELV_NO());
                if (position == mSelectedIndex) {
                    mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootLayout.setBackgroundResource(R.color.white);
                }
                return convertView;
            }
        }
    }

    private int mSelectedIndex = -1;
    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }

}
