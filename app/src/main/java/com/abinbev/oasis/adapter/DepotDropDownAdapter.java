package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.AUTH_HEADER;
import com.abinbev.oasis.be.DEPOT_HEADER;
import com.unvired.oasis.R;

import java.util.List;

public class DepotDropDownAdapter extends ArrayAdapter<DEPOT_HEADER> {
    private Context mContext;
    private List<DEPOT_HEADER> depotHeaderList;

    public DepotDropDownAdapter(@NonNull Context context, int resource, @NonNull List<DEPOT_HEADER> depotHeaderList) {
        super(context, resource, depotHeaderList);
        this.depotHeaderList=depotHeaderList;
        this.mContext=context;
    }

    @Override
    public int getCount() {
        return depotHeaderList.size();
    }

    @Nullable
    @Override
    public DEPOT_HEADER getItem(int position) {

        return depotHeaderList.get(position);


    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        TextView label = (TextView) super.getView(position, convertView, parent);
        label.setTextColor(Color.BLACK);
        label.setText(depotHeaderList.get(position).getNAME());
        return label;
    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        TextView label = (TextView) super.getDropDownView(position, convertView, parent);
        label.setTextColor(Color.BLACK);
        label.setText(depotHeaderList.get(position).getNAME());
        if (position == mSelectedIndex) {
            label.setBackgroundResource(R.color.depot_list_item_background_heighlight);
        } else {
            label.setBackgroundResource(R.color.white);
        }
        return label;
    }

    private int mSelectedIndex = -1;
    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }
}
