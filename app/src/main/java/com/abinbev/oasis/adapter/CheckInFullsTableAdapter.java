package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.STOCK;
import com.abinbev.oasis.util.Constants;
import com.unvired.oasis.R;

import java.util.List;

public class CheckInFullsTableAdapter extends RecyclerView.Adapter<CheckInFullsTableAdapter.ViewHolder> {
    private Activity mContext;
    private List<STOCK> stockList;
    public int selectedPosition=-1;
    private TableClickListner tableClickListner;
    public CheckInFullsTableAdapter(Activity mContext, List<STOCK> stockList, TableClickListner tableClickListner) {
        this.mContext = mContext;
        this.stockList = stockList;
        this.tableClickListner=tableClickListner;
    }
    @NonNull
    @Override
    public CheckInFullsTableAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.fbr_bit_recyclerview_single_item,parent,false);
        return new CheckInFullsTableAdapter.ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull CheckInFullsTableAdapter.ViewHolder holder, final int position) {
        final STOCK stock = stockList.get(position);
        holder.mMaterialName.setText(stock.getMAT_DESC());
        holder.mQty.setText(String.valueOf((int)stock.getCHCK_IN_QTY().intValue()));
        if(stock.getTYPE().equals(Constants.FBR_TYPE)){
            holder.mBitQty.setText("F");
        }else if(stock.getTYPE().equals(Constants.BIT_TYPE)){
            holder.mBitQty.setText("B");
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectedPosition = position;
                notifyDataSetChanged();
                tableClickListner.onTableClick(position,stock);
            }
        });

        if(selectedPosition == position) {
            holder.mMaterialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.mQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.mBitQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));

        } else {
            holder.mMaterialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.mQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.mBitQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));

        }
    }

    public void resetSelectedPos(){
        selectedPosition=-1;
        notifyDataSetChanged();
    }

    public void setSelectedPos(STOCK stock){
        for (int i=0;i<stockList.size();i++){
            if(stockList.get(i).getMAT_NO().equals(stock.getMAT_NO())){
                selectedPosition=i;
                notifyDataSetChanged();
                break;
            }
        }

    }
    public interface TableClickListner{
        public void onTableClick(int position,STOCK stock);
    }

    @Override
    public int getItemCount() {
        return stockList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView mMaterialName,mQty,mBitQty;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mMaterialName=itemView.findViewById(R.id.xFbrBitSingleItemMaterialName);
            mQty=itemView.findViewById(R.id.xFbrBitSingleItemFbrQty);
            mBitQty=itemView.findViewById(R.id.xFbrBitSingleItemBitQty);
        }
    }
}
