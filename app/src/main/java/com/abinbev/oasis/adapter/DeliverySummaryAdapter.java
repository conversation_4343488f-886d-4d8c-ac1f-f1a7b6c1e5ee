package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.ModelClasses.DeliveryItemPricing;
import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.R;

import java.util.HashMap;
import java.util.List;

public class DeliverySummaryAdapter extends RecyclerView.Adapter<DeliverySummaryAdapter.ViewHolder>{
    Activity mContext;
    List<DeliveryItemPricing> deliveryItemPricingList;
    public int selectedPosition=-1;

    public DeliverySummaryAdapter(Activity mContext, List<DeliveryItemPricing> deliveryItemPricingList) {
        this.mContext = mContext;
        this.deliveryItemPricingList = deliveryItemPricingList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.delivery_summary_single_item,parent,false);
        return new DeliverySummaryAdapter.ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, final int position) {
        DeliveryItemPricing deliveryItemPricing = deliveryItemPricingList.get(position);

        holder.mDelvName.setText(deliveryItemPricing.getMatDesc_());

        if(deliveryItemPricing.getIsReturn_().equals("X")){

            if(deliveryItemPricing.getOrderType_().equals(Constants.ORDER_TYPE.ZUBR.toString())){
                holder.mPlannedQty.setText(String.valueOf(deliveryItemPricing.getQty_().intValue()));
                holder.mDeliveredTxt.setText("Returned");
                holder.mDelvQty.setText(String.valueOf(deliveryItemPricing.getUbrQty_().intValue()));

            }else if(deliveryItemPricing.getRcrQty_().intValue()>0){
                holder.mPlannedQty.setText(String.valueOf(deliveryItemPricing.getQty_().intValue()));
                holder.mDeliveredTxt.setText("Returned");
                holder.mDelvQty.setText(String.valueOf(deliveryItemPricing.getRcrQty_().intValue()));
             }else if(deliveryItemPricing.getQty_().intValue()>0 && deliveryItemPricing.getRcrQty_().intValue()<=0){
                holder.mPlannedQty.setText("0");
                holder.mDeliveredTxt.setText("Returned");
                holder.mDelvQty.setText(String.valueOf(deliveryItemPricing.getQty_().intValue()));
             }
        }else {
            holder.mPlannedQty.setText(String.valueOf(deliveryItemPricing.getQty_().intValue()));
            holder.mDeliveredTxt.setText("Delivered");
            holder.mDelvQty.setText(String.valueOf(deliveryItemPricing.getQty_().intValue()-(deliveryItemPricing.getBitQty_().intValue()+deliveryItemPricing.getFbrQty_().intValue())));
            holder.mFbrBitQty.setText(String.valueOf(deliveryItemPricing.getBitQty_().intValue()+deliveryItemPricing.getFbrQty_().intValue()));
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(selectedPosition == position){
                    selectedPosition=-1;
                }else {
                    selectedPosition = position;
                }
                notifyDataSetChanged();

            }
        });

        if(selectedPosition == position) {
            holder.mRootLayout.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
        } else {
            if((position%2)!=0){
                changeLayoutColor(holder);
            }else {
                changeLayoutColor1(holder);
            }
        }
    }

    private void changeLayoutColor(ViewHolder holder) {
        holder.mRootLayout.setBackgroundResource(R.color.table_data_bg_color);
        holder.mDelvName.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mPlannedQty.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mDelvQty.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mFbrBitQty.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mPlannedTxt.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mDeliveredTxt.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mFbrBitTxt.setTextColor(mContext.getResources().getColor(R.color.black));
        holder.mDivider.setBackgroundResource(R.color.black);
    }

    private void changeLayoutColor1(ViewHolder holder) {
        holder.mRootLayout.setBackgroundResource(R.color.question_bg_color);
        holder.mDelvName.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mPlannedQty.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mDelvQty.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mFbrBitQty.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mPlannedTxt.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mDeliveredTxt.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mFbrBitTxt.setTextColor(mContext.getResources().getColor(R.color.white));
        holder.mDivider.setBackgroundResource(R.color.white);
    }

    @Override
    public int getItemCount() {
        return deliveryItemPricingList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView mDelvName,mPlannedQty,mDelvQty,mFbrBitQty,mPlannedTxt,mDeliveredTxt,mFbrBitTxt;
        View mDivider;
        RelativeLayout mRootLayout;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mDelvName=itemView.findViewById(R.id.xDeliverySummarySingleItemName);
            mPlannedQty=itemView.findViewById(R.id.xDeliverySummarySingleItemPlannedQty);
            mDelvQty=itemView.findViewById(R.id.xDeliverySummarySingleItemDeliveredQty);
            mFbrBitQty=itemView.findViewById(R.id.xDeliverySummarySingleItemFbrBitQty);
            mPlannedTxt=itemView.findViewById(R.id.xDeliverySummarySingleItemPlannedTxt);
            mDeliveredTxt=itemView.findViewById(R.id.xDeliverySummarySingleItemDeliveredTxt);
            mFbrBitTxt=itemView.findViewById(R.id.xDeliverySummarySingleItemFbrBitTxt);
            mDivider=itemView.findViewById(R.id.xDeliverySummarySingleItemDivider);
            mRootLayout=itemView.findViewById(R.id.xDeliverySummarySingleItemRootLayout);
        }
    }
}
