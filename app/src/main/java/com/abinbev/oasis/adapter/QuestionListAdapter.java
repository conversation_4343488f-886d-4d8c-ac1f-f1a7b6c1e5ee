package com.abinbev.oasis.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import com.abinbev.oasis.activities.PreTripInspectionVarianceActivity;
import com.abinbev.oasis.be.ANSWER_OPTION_HEADER;
import com.abinbev.oasis.be.QUESTION_HEADER;
import com.abinbev.oasis.be.TRIP_INSP;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.Utils;
import com.abinbev.oasis.R;

import java.util.List;

public class QuestionListAdapter extends RecyclerView.Adapter<QuestionListAdapter.ViewHolder> {
    Context mContext;
    public List<TRIP_INSP> tripInspHeaderList;
    private List<ANSWER_OPTION_HEADER> answersList;
    private QUESTION_HEADER selectedQuestion;
    public ANSWER_OPTION_HEADER selectedAnswerOption;
    public boolean validateAnswer = false;
    public int count = 0;
    public int validateAfter = 0;
    public TRIP_INSP selectedTripInspItem;
    Constants.SCREEN_NAVIGATION_MODE currentScreensNavigationMode;

    public QuestionListAdapter(Context mContext, List<TRIP_INSP> triPInspHeader,Constants.SCREEN_NAVIGATION_MODE screensNavigationMode)
    {
        this.mContext = mContext;
        tripInspHeaderList = triPInspHeader;
        if(tripInspHeaderList.size()!=0){
            validateAfter = tripInspHeaderList.size() - 1;
        }

        currentScreensNavigationMode = screensNavigationMode;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View v = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.question_single_item,viewGroup,false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, final int position) {

            final TRIP_INSP tripInspHeader = tripInspHeaderList.get(position);
            setupSpinner(viewHolder, tripInspHeader.getQ_ID(), tripInspHeader);
            QUESTION_HEADER questionHeader = DBHelper.getInstance().getQuestion(tripInspHeader.getQ_ID());
            this.selectedQuestion = questionHeader;
            viewHolder.mQuestion.setText(questionHeader.getQUES());
            if(tripInspHeader.getPRIO() == Constants.PRE_TRIP_PRIO_CRITICAL) {
                viewHolder.mQuestionBg.setCardBackgroundColor(mContext.getResources().getColor(R.color.red));
            } else {
                viewHolder.mQuestionBg.setCardBackgroundColor(mContext.getResources().getColor(R.color.yellow));
            }
    }

    private void setupSpinner(ViewHolder holder, Long questionId, TRIP_INSP tripInspHeader)
    {
        List <ANSWER_OPTION_HEADER> answers = null;
        final String[] selectedAnswer = {""};
        answers = DBHelper.getInstance().getAnswerOptions(questionId);
        final ArrayAdapter<ANSWER_OPTION_HEADER> adapter = new SpinAdapter(mContext,
                android.R.layout.simple_spinner_item, answers);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        holder.mSpinnerAns.setAdapter(adapter);
        for(int i = 0; i < answers.size(); i++) {
            if(tripInspHeader.getANS_TEXT() != null && answers.get(i).getANS_TEXT().equalsIgnoreCase(tripInspHeader.getANS_TEXT())) {
                holder.mSpinnerAns.setSelection(i);
                if(!tripInspHeader.getANS_TEXT().isEmpty()) {
                    validateAfter++;
                }
                ((SpinAdapter) adapter).setSelectionPosition(i);
            }
        }

        holder.mSpinnerAns.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parentView, View selectedItemView, int position, long id) {
                count++;
                if (((ANSWER_OPTION_HEADER)parentView.getItemAtPosition(position)).getANS_TEXT() != "" && validateAnswer == true) {
                    ((SpinAdapter)parentView.getAdapter()).setSelectionPosition(position);
                    validateAnswer((ANSWER_OPTION_HEADER)parentView.getItemAtPosition(position));
                }
                //Gets called 2 times during initialization due to setting color in the dropdown dialog.
//                if(count > validateAfter) {
                    validateAnswer = true;
//                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parentView) {

            }
        });
    }



    public void validateAnswer(ANSWER_OPTION_HEADER ansObj) {

            selectedQuestion = DBHelper.getInstance().getQuestion(ansObj.getQ_ID());
            selectedTripInspItem = DBHelper.getInstance().getSelectedTripInsp(tripInspHeaderList.get(0).getINSP_TYPE(), ansObj.getQ_ID());
            answersList = DBHelper.getInstance().getAnswerOptions(selectedQuestion.getQ_ID());
            for (int i = 0; i < answersList.size(); i++) {
                if (ansObj.getANS_TEXT().equalsIgnoreCase(answersList.get(i).getANS_TEXT())) {
                    if (("X").equalsIgnoreCase(answersList.get(i).getIS_AUTH_REQD())) {
                        if(Utils.isNullOrEmpty(selectedTripInspItem.getAUTH_BY()) && selectedTripInspItem.getREASON() <= 0)
                        navigateToPreTripVarient(ansObj);
//                        updateTripInapectionHeader(ansObj);
                    } else {
                        updateTripInapectionHeader(ansObj);
                    }
                }
            }

    }



    public void updateTripInapectionHeader(ANSWER_OPTION_HEADER ansObj) {
        TRIP_INSP updateRow = null;
        for(int i = 0; i < this.tripInspHeaderList.size(); i++) {
            if(ansObj.getQ_ID() == this.tripInspHeaderList.get(i).getQ_ID()) {
                this.tripInspHeaderList.get(i).setANS_TEXT(ansObj.getANS_TEXT());
                this.tripInspHeaderList.get(i).setANS_ID(ansObj.getANS_ID());
                this.tripInspHeaderList.get(i).setREASON((long) 0);
                this.tripInspHeaderList.get(i).setAUTH_BY("");
                updateRow = this.tripInspHeaderList.get(i);
            }
        }
        if(updateRow != null) {
            DBHelper.getInstance().updateTripInspHeader(updateRow);
        }
    }

    public void navigateToPreTripVarient(ANSWER_OPTION_HEADER ansObj) {
        TRIP_INSP updateRow = null;
        for(int i = 0; i < this.tripInspHeaderList.size(); i++) {
            if(ansObj.getQ_ID() == this.tripInspHeaderList.get(i).getQ_ID()) {
                this.tripInspHeaderList.get(i).setANS_TEXT(ansObj.getANS_TEXT());
                this.tripInspHeaderList.get(i).setANS_ID(ansObj.getANS_ID());
                updateRow = this.tripInspHeaderList.get(i);
            }
        }
        if(updateRow != null) {
            Intent intent = new Intent(mContext, PreTripInspectionVarianceActivity.class);
            intent.putExtra(Constants.SCREEN_NAVIGATION,currentScreensNavigationMode);
            DataHelper.getInstance().setTripInspectionToUpdate(updateRow);
            mContext.startActivity(intent);
        }
    }

    @Override
    public int getItemCount() {
        if (tripInspHeaderList != null && tripInspHeaderList.size() > 0) {
            return tripInspHeaderList.size();
        } else
            return 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        CardView mQuestionBg;
        TextView mQuestion;
        Spinner mSpinnerAns;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mQuestionBg=itemView.findViewById(R.id.xQuestionBg);
            mQuestion=itemView.findViewById(R.id.xQuestionText);
            mSpinnerAns=itemView.findViewById(R.id.xQuestionSpinner);
        }
    }
}
