package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.R;

import java.util.List;

public class UbrTableAdapter extends RecyclerView.Adapter<UbrTableAdapter.ViewHolder>{
    private Activity mContext;
    private List<DELIVERY_ITEM> delivery_itemList;
    public int selectedPosition=-1;
    private RcrTableAdapter.TableClickListner tableClickListner;
    public UbrTableAdapter(Activity mContext, List<DELIVERY_ITEM> delivery_itemList, RcrTableAdapter.TableClickListner tableClickListner) {
        this.mContext = mContext;
        this.delivery_itemList = delivery_itemList;
        this.tableClickListner=tableClickListner;
    }

    @NonNull
    @Override
    public UbrTableAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.rcr_recyclerview_single_item,parent,false);
        return new UbrTableAdapter.ViewHolder(v);
    }

    public interface TableClickListner{
        public void onTableClick(int position,DELIVERY_ITEM delivery_item);
    }
    @Override
    public void onBindViewHolder(@NonNull UbrTableAdapter.ViewHolder holder, final int position) {
        final DELIVERY_ITEM delivery_item = delivery_itemList.get(position);
        holder.materialName.setText(delivery_item.getMAT_DESC());
        holder.totalCases.setText(String.valueOf(delivery_item.getUBR_QTY().intValue()));
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectedPosition = position;
                notifyDataSetChanged();
                tableClickListner.onTableClick(position,delivery_item);
            }
        });

        if(selectedPosition == position) {
            holder.materialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.totalCases.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));

        } else {
            holder.materialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.totalCases.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));

        }
    }

    @Override
    public int getItemCount() {
        return delivery_itemList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView materialName,totalCases;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            materialName = itemView.findViewById(R.id.xRcrSingleItemMaterialName);
            totalCases = itemView.findViewById(R.id.xRcrSingleItemTotalCases);
        }
    }
}
