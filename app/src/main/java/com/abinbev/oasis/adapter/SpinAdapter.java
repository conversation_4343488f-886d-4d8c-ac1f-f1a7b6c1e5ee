package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import com.abinbev.oasis.be.ANSWER_OPTION_HEADER;
import com.abinbev.oasis.R;

import java.util.List;

public class SpinAdapter extends ArrayAdapter<ANSWER_OPTION_HEADER> {

    // Your sent context
    private Context context;
    // Your custom values for the spinner (User)
    private List<ANSWER_OPTION_HEADER> values;
    private int mSelectedIndex = -1;

    public SpinAdapter(Context context, int textViewResourceId,
                       List<ANSWER_OPTION_HEADER> values) {
        super(context, textViewResourceId, values);
        this.context = context;
        this.values = values;
    }

    @Override
    public int getCount(){
        return values.size();
    }

    @Override
    public ANSWER_OPTION_HEADER getItem(int position){
        return values.get(position);
    }

    @Override
    public long getItemId(int position){
        return position;
    }


    // And the "magic" goes here
    // This is for the "passive" state of the spinner
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        // I created a dynamic TextView here, but you can reference your own  custom layout for each spinner item
        TextView label = (TextView) super.getView(position, convertView, parent);
        label.setTextColor(Color.BLACK);
        // Then you can get the current item using the values array (Users array) and the current position
        // You can NOW reference each method you has created in your bean object (User class)
        label.setText(values.get(position).getANS_TEXT());

        // And finally return your dynamic (or custom) view for each spinner item
        return label;
    }

    // And here is when the "chooser" is popped up
    // Normally is the same view, but you can customize it if you want
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        TextView label = (TextView) super.getDropDownView(position, convertView, parent);
        View itemView =  super.getDropDownView(position, convertView, parent);
        label.setText(values.get(position).getANS_TEXT());
        if (position == 0) {
            label.setBackgroundColor(Color.WHITE);
            label.setTextColor(Color.BLACK);
        } else if (position == mSelectedIndex) {
            label.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            label.setTextColor(Color.WHITE);
        } else {
                label.setBackgroundColor(Color.TRANSPARENT);
                label.setTextColor(Color.BLACK);
        }

        return label;
    }

    @Override
    public boolean isEnabled(int position) {
        if (position == 0) {
            // Disable the first item from Spinner
            // First item will be use for hint
            return false;
        } else {
            return true;
        }
    }

    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }

}