package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.STOCK;
import com.unvired.oasis.R;

import java.util.List;

public class CheckInEmptiesTableAdapter extends RecyclerView.Adapter<CheckInEmptiesTableAdapter.ViewHolder>{
    private Activity mContext;
    private List<STOCK> stockList;
    public int selectedPosition=-1;
    private CheckInFullsTableAdapter.TableClickListner tableClickListner;
    public CheckInEmptiesTableAdapter(Activity mContext, List<STOCK> stockList, CheckInFullsTableAdapter.TableClickListner tableClickListner) {
        this.mContext = mContext;
        this.stockList = stockList;
        this.tableClickListner=tableClickListner;
    }
    @NonNull
    @Override
    public CheckInEmptiesTableAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.rcr_recyclerview_single_item,parent,false);
        return new CheckInEmptiesTableAdapter.ViewHolder(v);
    }

    public void resetSelectedPos(){
        selectedPosition=-1;
        notifyDataSetChanged();
    }

    public void setSelectedPos(STOCK stock){
        for (int i=0;i<stockList.size();i++){
            if(stockList.get(i).getMAT_NO().equals(stock.getMAT_NO())){
                selectedPosition=i;
                notifyDataSetChanged();
                break;
            }
         }

    }

    @Override
    public void onBindViewHolder(@NonNull CheckInEmptiesTableAdapter.ViewHolder holder, final int position) {
        final STOCK stock = stockList.get(position);
        holder.materialName.setText(stock.getMAT_DESC());
        holder.totalCases.setText(String.valueOf(stock.getCHCK_IN_QTY().intValue()));
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectedPosition = position;
                notifyDataSetChanged();
                tableClickListner.onTableClick(position,stock);
            }
        });

        if(selectedPosition == position) {
            holder.materialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.totalCases.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));

        } else {
            holder.materialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.totalCases.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));

        }
    }

    @Override
    public int getItemCount() {
        return stockList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView materialName,totalCases;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            materialName = itemView.findViewById(R.id.xRcrSingleItemMaterialName);
            totalCases = itemView.findViewById(R.id.xRcrSingleItemTotalCases);
        }
    }
}
