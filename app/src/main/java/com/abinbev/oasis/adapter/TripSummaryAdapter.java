package com.abinbev.oasis.adapter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.abinbev.oasis.ModelClasses.TRIP_SUMMARY;
import com.abinbev.oasis.activities.DeliveryMenuActivity;
import com.abinbev.oasis.activities.EndOfVisitMenuActivity;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.ImageUtil;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.bumptech.glide.Glide;
import com.google.android.material.textfield.TextInputEditText;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.util.HashMap;
import java.util.List;

public class TripSummaryAdapter extends RecyclerView.Adapter<TripSummaryAdapter.ViewHolder> {
    Activity mContext;
    List<TRIP_SUMMARY> trip_summaryList;
    HashMap<Integer, Integer> NO_OF_DELIVERIES;
    public int selectedPosition = -1;

    public TripSummaryAdapter(Activity mContext, List<TRIP_SUMMARY> trip_summaryList, HashMap<Integer, Integer> NO_OF_DELIVERIES) {
        this.mContext = mContext;
        this.trip_summaryList = trip_summaryList;
        this.NO_OF_DELIVERIES = NO_OF_DELIVERIES;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View v = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.trip_summary_single_item, viewGroup, false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, @SuppressLint("RecyclerView") final int i) {
        final TRIP_SUMMARY trip_summary = trip_summaryList.get(i);

        viewHolder.mSno.setText(String.valueOf(i + 1));
        viewHolder.mName.setText(trip_summary.getNAME());
        viewHolder.mOutlet.setText(trip_summary.getCUST_NO());
        viewHolder.mContact.setText(trip_summary.getACC_MGR());
        viewHolder.mTelNo.setText(trip_summary.getACC_MGR_TEL());
        viewHolder.mAddress.setText(trip_summary.getADDRESS());
        viewHolder.mDeliveries.setText((NO_OF_DELIVERIES.get(trip_summary.getVISIT_NO()) != null) ? NO_OF_DELIVERIES.get(trip_summary.getVISIT_NO()).toString() : "0");
        viewHolder.mVisitWindow.setText(trip_summary.getTIME_WIN_FROM() + " " + trip_summary.getTIME_WIN_TO());

        if (trip_summary.getSTAT() == Constants.VISIT_STATUS_END) {
            viewHolder.mRootView.setCardBackgroundColor(mContext.getResources().getColor(R.color.green));
            Glide.with(mContext).load(R.drawable.task_completed).into(viewHolder.mImg);
        } else {
            viewHolder.mRootView.setCardBackgroundColor(mContext.getResources().getColor(R.color.red));
            Glide.with(mContext).load(R.drawable.ic_arrow_right).into(viewHolder.mImg);
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (visitSelection(trip_summary)) {
                        navigateToEndOfVisitMenuForm();
                    } else {
                        showOdometerReadingDialog(trip_summary);
                        selectedPosition = i;
                        notifyDataSetChanged();
                    }
                }
            });
        }
        if (selectedPosition == i) {
            viewHolder.mBackground.setBackgroundColor(Color.parseColor("#" + Integer.toHexString(ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
        } else {
            viewHolder.mBackground.setBackgroundColor(Color.parseColor("#" + Integer.toHexString(ContextCompat.getColor(mContext, R.color.depot_list_item_background))));
        }
    }

    private boolean visitSelection(TRIP_SUMMARY trip_summary) {
        try {
            TripSummaryController.getInstance().setVisitRow(trip_summary.getVISIT_NO());
            TripSummaryController.getInstance().setNAT_CUSTOMER_TYPE(String.valueOf(trip_summary.getIS_NAT_CUST()));
        } catch (Exception e) {
            return false;
        }
        int s = (int) trip_summary.getSTAT();
        if (s != Constants.VISIT_STATUS_END) {
            float visitMile = trip_summary.getMILE();
            if (visitMile != 0 && s == Constants.VISIT_STATUS_END_REACHED) {
                return true;
            }

        }
        return false;
    }

    private void navigateToEndOfVisitMenuForm() {
        Intent intent = new Intent(mContext, EndOfVisitMenuActivity.class);
        mContext.startActivity(intent);
    }

    private void showOdometerReadingDialog(final TRIP_SUMMARY trip_summary) {

        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        final View dialogView = LayoutInflater.from(mContext).inflate(R.layout.odometer_reading_dialog_trip_summary, null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xOdometerReadingDialogClose);
        Button dialogProceed = dialogView.findViewById(R.id.xOdometerReadingDialogProceed);
        TextView mMessage = dialogView.findViewById(R.id.xOdometerReadingDialogMessage);
        TextInputEditText mReadingEt = dialogView.findViewById(R.id.xOdometerReadingDialogReadingEt);

        mMessage.setText("Odometer reading at " + trip_summary.getNAME());
        dialogProceed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                float currentReading = 0;
                if (mReadingEt.getText().toString().isEmpty()) {
                    SnackBarToast.showMessage(v, "Please enter truck KM reading");
                }
                try {
                    currentReading = Float.parseFloat(mReadingEt.getText().toString());
                } catch (Exception e) {
                    SnackBarToast.showMessage(v, "Please enter a valid KM reading");
                }
                SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
                double previousReading = shipment_header.getEND_MILE() == null ? 0 : shipment_header.getEND_MILE();
                if (currentReading < previousReading) {
                    SnackBarToast.showMessage(v, "Entered KM reading less than truck odometer.");
                } else {
                    resetSelection();
                    alertDialog.dismiss();
                    trip_summary.setMILE(currentReading);
                    VISIT currentVisitRow = TripSummaryController.getInstance().getCurrentVisitRow();
                    currentVisitRow.setMILE((double) currentReading);
                    TripSummaryController.getInstance().insertOrUpdateVisit(currentVisitRow);
                    shipment_header.setEND_MILE((double) currentReading);
                    DBHelper.getInstance().updateShipmentStatus(shipment_header);
                    DBHelper.getInstance().createNewTime(currentVisitRow.getVISIT_NO().toString(), Constants.TIME_TYPE_VISIT_START);
                    alertDialog.dismiss();
                    Logger.i("User in TripSummaryActivity. navigateToDeliveryMenu VISIT_NO: "+TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO());
                    navigateToDeliveryMenu();
                }
            }
        });
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetSelection();
                alertDialog.dismiss();
            }
        });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mReadingEt.requestFocus();
        alertDialog.show();

    }


    private void resetSelection() {
        selectedPosition = -1;
        notifyDataSetChanged();
    }

    private void navigateToDeliveryMenu() {
        mContext.startActivity(new Intent(mContext, DeliveryMenuActivity.class));
    }

    @Override
    public int getItemCount() {
        return trip_summaryList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        RelativeLayout mNameLayout, mBackground;
        TextView mSno, mName, mOutlet, mAddress, mContact, mTelNo, mDeliveries, mVisitWindow;
        CardView mRootView;
        ImageView mImg;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mNameLayout = itemView.findViewById(R.id.xTripSummaryNameLayout);
            mSno = itemView.findViewById(R.id.xTripSummarySingleItemIndex);
            mName = itemView.findViewById(R.id.xTripSummarySingleItemCustomerName);
            mOutlet = itemView.findViewById(R.id.xTripSummarySingleItemOutletNo);
            mAddress = itemView.findViewById(R.id.xTripSummarySingleItemAddress);
            mContact = itemView.findViewById(R.id.xTripSummarySingleItemContact);
            mTelNo = itemView.findViewById(R.id.xTripSummarySingleItemTelNo);
            mDeliveries = itemView.findViewById(R.id.xTripSummarySingleItemDeliveries);
            mVisitWindow = itemView.findViewById(R.id.xTripSummarySingleItemVisitWindow);
            mBackground = itemView.findViewById(R.id.xTripSummarySingleItemBg);
            mRootView = itemView.findViewById(R.id.xTripSummarySingleItemRootLayout);
            mImg = itemView.findViewById(R.id.xTripSummarySingleItemGoImg);
        }
    }


    public interface OdometerReadingListner {
        public void onReadingProvided();
    }
}
