package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.be.IMAGE_HEADER;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.ImageUtil;
import com.bumptech.glide.Glide;
import com.abinbev.oasis.R;

import java.util.List;

public class DeliveryItemDropDownAdapter extends ArrayAdapter<DELIVERY_ITEM> {
    private Context mContext;
    private List<DELIVERY_ITEM> deliveryItemList;
    private boolean showPlaceholder;
    private boolean showImage;
    private int mSelectedIndex = -1;
    public DeliveryItemDropDownAdapter(@NonNull Context context, int resource, @NonNull List<DELIVERY_ITEM> deliveryItemList,boolean showPlaceholder,boolean showImage) {
        super(context, resource, deliveryItemList);
        this.deliveryItemList=deliveryItemList;
        this.mContext=context;
        this.showPlaceholder=showPlaceholder;
        this.showImage = showImage;
    }


    @Override
    public int getCount() {
        if(showPlaceholder){
            return deliveryItemList.size()+1;
        }else {
            return deliveryItemList.size();
        }
    }

    @Nullable
    @Override
    public DELIVERY_ITEM getItem(int position) {
        if(showPlaceholder){
            if(position==0){
                return null;
            }else {
                return deliveryItemList.get(position-1);

            }
        }else {
            return deliveryItemList.get(position);
        }

    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        if(!showPlaceholder) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
            TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
            TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
            ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
            RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
            View mDiv = convertView.findViewById(R.id.xProductDropDownDivider);
            mRootView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            mDiv.setVisibility(View.GONE);
            mRootView.setBackgroundResource(R.color.question_bg_color);
            mMatDes.setTextColor(mContext.getResources().getColor(R.color.white));
            mMatNo.setTextColor(mContext.getResources().getColor(R.color.white));
            if(showImage) {
                IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(deliveryItemList.get(position).getMAT_NO());
                if (imageHeader != null) {
                    Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                } else {
                    Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                }
            }else {
                mImg.setVisibility(View.GONE);
            }
            mMatNo.setText(deliveryItemList.get(position).getMAT_NO());
            mMatDes.setText(deliveryItemList.get(position).getMAT_DESC());

            return convertView;
        }else {
            if(position==0){
                convertView=LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                View mDiv = convertView.findViewById(R.id.xReasonDropDownDiv);
                mDiv.setVisibility(View.GONE);
                label.setTextColor(Color.BLACK);
                label.setText("");
                return convertView;
            }else {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
                TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
                TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
                ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
                RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
                View mDiv = convertView.findViewById(R.id.xProductDropDownDivider);
                mRootView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                mRootView.setBackgroundResource(R.color.table_bg_color);
                mMatDes.setTextColor(mContext.getResources().getColor(R.color.white));
                mMatNo.setTextColor(mContext.getResources().getColor(R.color.white));
                mDiv.setVisibility(View.GONE);
                if(showImage){
                    IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(deliveryItemList.get(position-1).getMAT_NO());
                    if (imageHeader != null) {
                        Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                    } else {
                        Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                    }
                }else {
                    mImg.setVisibility(View.GONE);
                }
                mMatNo.setText(deliveryItemList.get(position-1).getMAT_NO());
                mMatDes.setText(deliveryItemList.get(position-1).getMAT_DESC());

                return convertView;
            }
        }

    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        if(!showPlaceholder) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
            TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
            TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
            ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
            RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);

            if(showImage){
            IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(deliveryItemList.get(position).getMAT_NO());
            if (imageHeader != null) {
                Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
            } else {
                Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
            }
            }else {
                mImg.setVisibility(View.GONE);
            }
            mMatNo.setText(deliveryItemList.get(position).getMAT_NO());
            mMatDes.setText(deliveryItemList.get(position).getMAT_DESC());
            if (position == mSelectedIndex) {
                mRootView.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            } else {
                mRootView.setBackgroundResource(R.color.white);
            }
            return convertView;
        }else {
            if(position==0){
                convertView=LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setText("Select product");
                label.setTypeface(null, Typeface.BOLD);
                label.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);
                label.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                return convertView;
            }else {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.product_list_dropdown_layout, null);
                TextView mMatNo = convertView.findViewById(R.id.xProductDropDownMatNo);
                TextView mMatDes = convertView.findViewById(R.id.xProductDropDownMatDesc);
                ImageView mImg = convertView.findViewById(R.id.xProductDropDownImg);
                 RelativeLayout mRootView = convertView.findViewById(R.id.xProductListDropDownRoot);
                if(showImage){
                IMAGE_HEADER imageHeader = DBHelper.getInstance().getImageForMaterial(deliveryItemList.get(position-1).getMAT_NO());
                if (imageHeader != null) {
                    Glide.with(mContext).load(ImageUtil.getBitmapFromBase64(imageHeader.getIMG())).placeholder(R.drawable.image_placeholder).into(mImg);
                } else {
                    Glide.with(mContext).load(R.drawable.image_placeholder).into(mImg);
                }
                }else {
                    mImg.setVisibility(View.GONE);
                }
                mMatNo.setText(deliveryItemList.get(position-1).getMAT_NO());
                mMatDes.setText(deliveryItemList.get(position-1).getMAT_DESC());
                if (position == mSelectedIndex) {
                    mRootView.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootView.setBackgroundResource(R.color.white);
                }
                return convertView;
            }
        }
    }

    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }

}
