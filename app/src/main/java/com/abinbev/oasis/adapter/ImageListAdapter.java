package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.util.Loader.ImageRowMoveCallback;
import com.bumptech.glide.Glide;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;

public class ImageListAdapter extends RecyclerView.Adapter<ImageListAdapter.ViewHolder> implements ImageRowMoveCallback.RecyclerViewRowTouchHelperContract {

    private ArrayList<Uri> imagesList;
    Context context;

    public ImageListAdapter(ArrayList<Uri> list, Context context) {
        this.imagesList = list;
        this.context = context;

    }

    @NonNull
    @Override
    public ImageListAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater=LayoutInflater.from(parent.getContext());
        View view =inflater.inflate(R.layout.custom_image_list,parent,false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Uri imageUri = imagesList.get(position);
        Uri contentUri = null;
//        if(imageUri.toString().startsWith("content://")){
             contentUri =  Uri.parse(String.valueOf(imageUri));
//        }else {
//            contentUri = FileProvider.getUriForFile(context, "com.abinbev.oasis.activities.ScanCustomerClaimPod.provider", new File(imageUri.getPath()));
//        }
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(contentUri);
            if (inputStream != null) {
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeStream(inputStream, null, options);
                int imageWidth = options.outWidth;
                int imageHeight = options.outHeight;

                    holder.imageView.setRotation(0);
                    holder.imageView.setImageURI(imagesList.get(position));
                inputStream.close();
            } else {
                Log.e("Inputstream is empty", inputStream.toString());
            }
        } catch (IOException e) {
            Logger.e("", e);
        }

        holder.imageView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                        holder.imageView.setBackgroundColor(Color.GRAY);
                        holder.crossButton.setVisibility(View.VISIBLE);
                        holder.deleteButton.setVisibility(View.VISIBLE);

                        holder.deleteButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            imagesList.remove(position);
                            notifyItemRemoved(position);
                            notifyItemRangeChanged(position, imagesList.size());
                        }
                    });

                    holder.crossButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            holder.imageView.setBackgroundColor(Color.TRANSPARENT);
                            holder.crossButton.setVisibility(View.GONE);
                            holder.deleteButton.setVisibility(View.GONE);
                        }
                    });
                return false;

            }

        });

    }

    @Override
    public int getItemCount() {
        return imagesList.size();
    }

    @Override
    public void onRowMoved(int from, int to) {
        if(from < to)
        {
            for(int i=from; i<to; i++)
            {
                Collections.swap(imagesList,i,i+1);
            }
        }
        else
        {
            for(int i=from; i>to; i--)
            {
                Collections.swap(imagesList,i,i-1);
            }
        }
        notifyItemMoved(from,to);
    }

    @Override
    public void onRowSelected(ViewHolder myViewHolder) {

    }

    @Override
    public void onRowClear(ViewHolder myViewHolder) {

    }

    public class ViewHolder extends RecyclerView.ViewHolder {
            ImageView imageView;
            ImageView crossButton;
            ImageView deleteButton;
        public ViewHolder(@NonNull View itemView) {
                super(itemView);
                imageView=itemView.findViewById(R.id.customerClaimImage);
                crossButton=itemView.findViewById(R.id.crossButton);
                deleteButton=itemView.findViewById(R.id.deleteButton);
            }
        }
    }
