package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.REASON_HEADER;
import com.unvired.oasis.R;

import java.util.List;

public class ReasonDropDownAdapter extends ArrayAdapter<REASON_HEADER> {
    private Context mContext;
    private List<REASON_HEADER> reason_headerList;
    private boolean showPlaceholder;
    public ReasonDropDownAdapter(@NonNull Context context, int resource, @NonNull List<REASON_HEADER> objects,boolean showPlaceholder) {
        super(context, resource, objects);
        this.mContext=context;
        this.reason_headerList=objects;
        this.showPlaceholder=showPlaceholder;
    }
    @Override
    public int getCount() {
        if(showPlaceholder){
            return reason_headerList.size()+1;
        }else {
            return reason_headerList.size();
        }
    }

    @Nullable
    @Override
    public REASON_HEADER getItem(int position) {

        if(showPlaceholder){
            if(position==0){
                return null;
            }else {
                return reason_headerList.get(position-1);

            }
        }else {
            return reason_headerList.get(position);
        }


    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {

        if(!showPlaceholder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
            divider.setVisibility(View.GONE);
            mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            mRootLayout.setBackgroundResource(R.color.table_bg_color);
            label.setGravity(Gravity.CENTER_VERTICAL);
            label.setTextColor(Color.WHITE);
            label.setText(reason_headerList.get(position).getRSN_DESC());
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                label.setTextColor(Color.BLACK);
                label.setText("");
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                mRootLayout.setBackgroundResource(R.color.table_bg_color);
                label.setGravity(Gravity.CENTER_VERTICAL);
                label.setTextColor(Color.WHITE);
                label.setText(reason_headerList.get(position-1).getRSN_DESC());
                return convertView;
            }
        }
    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        if(!showPlaceholder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);

            label.setTextColor(Color.BLACK);
            label.setText(reason_headerList.get(position).getRSN_DESC());
            if (position == mSelectedIndex) {
                mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            } else {
                mRootLayout.setBackgroundResource(R.color.white);
            }
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setGravity(Gravity.CENTER_HORIZONTAL);
                label.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                label.setTypeface(null, Typeface.BOLD);
                label.setText("Select Reason");
                if (position == mSelectedIndex) {
                    mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootLayout.setBackgroundResource(R.color.white);
                }
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                label.setTextColor(Color.BLACK);
                label.setText(reason_headerList.get(position-1).getRSN_DESC());
                if (position == mSelectedIndex) {
                    mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootLayout.setBackgroundResource(R.color.white);
                }
                return convertView;
            }
        }
    }


    private int mSelectedIndex = -1;
    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }
}
