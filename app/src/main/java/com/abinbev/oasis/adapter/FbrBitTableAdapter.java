package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.be.DELIVERY_ITEM;
import com.abinbev.oasis.R;

import java.util.List;

public class FbrBitTableAdapter extends RecyclerView.Adapter<FbrBitTableAdapter.ViewHolder> {
    private Activity mContext;
    private List<DELIVERY_ITEM> delivery_itemList;
    public int selectedPosition=-1;
    private RcrTableAdapter.TableClickListner tableClickListner;
    public FbrBitTableAdapter(Activity mContext, List<DELIVERY_ITEM> delivery_itemList, RcrTableAdapter.TableClickListner tableClickListner) {
        this.mContext = mContext;
        this.delivery_itemList = delivery_itemList;
        this.tableClickListner=tableClickListner;
    }
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.fbr_bit_recyclerview_single_item,parent,false);
        return new FbrBitTableAdapter.ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, final int position) {
        final DELIVERY_ITEM delivery_item = delivery_itemList.get(position);
        holder.mMaterialName.setText(delivery_item.getMAT_DESC());
        holder.mFbrQty.setText(String.valueOf((int)delivery_item.getFBR_QTY().intValue()));
        holder.mBitQty.setText(String.valueOf((int)delivery_item.getBIT_QTY().intValue()));
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectedPosition = position;
                notifyDataSetChanged();
                tableClickListner.onTableClick(position,delivery_item);
            }
        });

        if(selectedPosition == position) {
            holder.mMaterialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.mFbrQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
            holder.mBitQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));

        } else {
            holder.mMaterialName.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.mFbrQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));
            holder.mBitQty.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.table_data_bg_color))));

        }
    }

    @Override
    public int getItemCount() {
        return delivery_itemList.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView mMaterialName,mFbrQty,mBitQty;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mMaterialName=itemView.findViewById(R.id.xFbrBitSingleItemMaterialName);
            mFbrQty=itemView.findViewById(R.id.xFbrBitSingleItemFbrQty);
            mBitQty=itemView.findViewById(R.id.xFbrBitSingleItemBitQty);
        }
    }
}
