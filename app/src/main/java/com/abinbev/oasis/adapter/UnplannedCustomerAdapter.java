package com.abinbev.oasis.adapter;

import android.app.Activity;
import android.app.AlertDialog;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.abinbev.oasis.activities.DeliveryMenuActivity;
import com.abinbev.oasis.activities.EndOfVisitMenuActivity;
import com.abinbev.oasis.be.CUSTOMER_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.be.VISIT;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Controllers.DeliveryController;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Controllers.TripSummaryController;
import com.bumptech.glide.Glide;
import com.google.android.material.textfield.TextInputEditText;
import com.unvired.database.DBException;
import com.unvired.logger.Logger;
import com.unvired.oasis.R;

import java.util.ArrayList;
import java.util.List;

public class UnplannedCustomerAdapter extends RecyclerView.Adapter<UnplannedCustomerAdapter.ViewHolder> {

    Activity mContext;
    List<CUSTOMER_HEADER> customerHeaders;
    List<CUSTOMER_HEADER> filteredCustomerHeaders;
    public int selectedPosition=-1;
    public UnplannedCustomerAdapter(Activity mContext,List<CUSTOMER_HEADER> customerHeaders)
    {
        this.mContext = mContext;
        this.customerHeaders=customerHeaders;
        this.filteredCustomerHeaders=customerHeaders;
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View v = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.unplanned_customer_single_item,viewGroup,false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, final int i) {
        final CUSTOMER_HEADER customer_header = filteredCustomerHeaders.get(i);
        viewHolder.mSno.setText(String.valueOf(i+1));
        viewHolder.mName.setText(customer_header.getNAME());
        viewHolder.mOutlet.setText(customer_header.getCUST_NO());
        viewHolder.mContact.setText(customer_header.getACC_MGR());
        viewHolder.mTelNo.setText(customer_header.getACC_MGR_TEL());
        viewHolder.mAddress.setText(customer_header.getADDRESS());
        viewHolder.mVisitWindow.setText(customer_header.getTIME_WIN_FROM() + " " + customer_header.getTIME_WIN_TO());
        Glide.with(mContext).asGif().load(R.raw.map_giff).into(viewHolder.mMap);
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v)
            {
                checkCustomerVisitStatus(customer_header,v);
                selectedPosition=i;
                notifyDataSetChanged();
            }
        });
        if(selectedPosition == i) {
            viewHolder.mBackground.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background_heighlight))));
        } else {
            viewHolder.mBackground.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(mContext, R.color.depot_list_item_background))));
        }
    }

    private void checkCustomerVisitStatus(CUSTOMER_HEADER customer_header, View v) {
        if(visitExists(customer_header,v)){
            VISIT visit = TripSummaryController.getInstance().getCurrentVisitRow();
            TripSummaryController.getInstance().setNAT_CUSTOMER_TYPE(customer_header.getIS_NAT_CUST());
            if(visit.getSTAT()==Constants.VISIT_STATUS_END_REACHED){
                navigateToEndOfVisitMenuForm();
            }else {
                DBHelper.getInstance().setUnplannedCustomer(true);
                DBHelper.getInstance().setPalletPickPrompted(false);
                navigateToDeliveryMenu();
            }
        }
    }

    private void navigateToEndOfVisitMenuForm() {
        mContext.startActivity(new Intent(mContext, EndOfVisitMenuActivity.class));
    }

    public boolean visitExists(CUSTOMER_HEADER customer_header,View view)
    {
        VISIT visit =TripSummaryController.getInstance().getVisitForCustomer(customer_header.getCUST_NO());
        if(visit==null){
            showOdometerReadingDialog(customer_header);
            return false;
        }else if(visit.getSTAT()==Constants.VISIT_STATUS_END){
            SnackBarToast.showMessage(view,"This customer has already been visited");
            return false;
        }else {
            TripSummaryController.getInstance().setCurrentVisitRow(visit);
            return true;
        }
    }

    private void showOdometerReadingDialog(final CUSTOMER_HEADER customer_header) {
        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        final View dialogView = LayoutInflater.from(mContext).inflate(R.layout.odometer_reading_dialog_trip_summary,  null);
        builder.setView(dialogView);
        final AlertDialog alertDialog = builder.create();
        ImageView dialogClose = dialogView.findViewById(R.id.xOdometerReadingDialogClose);
        Button dialogProceed = dialogView.findViewById(R.id.xOdometerReadingDialogProceed);
        TextView mMessage = dialogView.findViewById(R.id.xOdometerReadingDialogMessage);
        final TextInputEditText mReadingEt = dialogView.findViewById(R.id.xOdometerReadingDialogReadingEt);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mReadingEt.setFocusedByDefault(true);
        }
        mMessage.setText("Odometer reading at " + customer_header.getNAME());
        dialogProceed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                float currentReading = 0;
                if(mReadingEt.getText().toString().isEmpty()){
                    SnackBarToast.showMessage(v,"Please enter truck KM reading");
                }
                try {
                    currentReading=Float.parseFloat(mReadingEt.getText().toString());
                }catch (Exception e){
                    SnackBarToast.showMessage(v,"Please enter a valid KM reading");
                }
                SHIPMENT_HEADER shipment_header = DBHelper.getInstance().getCurrentShipment();
                double previousReading = shipment_header.getEND_MILE() == null ? 0 :  shipment_header.getEND_MILE();
                if(currentReading<previousReading){
                    SnackBarToast.showMessage(v,"Entered KM reading less than truck odometer.");
                }else {
                    alertDialog.dismiss();
                    resetSelection();
                    Loader.showLoader(mContext,"Please wait...");
                    shipment_header.setEND_MILE((double) currentReading);
                    DBHelper.getInstance().updateShipmentStatus(shipment_header);
                    createVisitAndUpdateMile(customer_header,currentReading);
                    DBHelper.getInstance().createNewTime(TripSummaryController.getInstance().getCurrentVisitRow().getVISIT_NO().toString(),Constants.TIME_TYPE_VISIT_START);
                    DeliveryController.getInstance().createAndSaveDeliveryRow();
                    DBHelper.getInstance().setUnplannedCustomer(true);
                    TripSummaryController.getInstance().setNAT_CUSTOMER_TYPE(customer_header.getIS_NAT_CUST());
                    Loader.hideLoader();
                    navigateToDeliveryMenu();
                }
            }
        });
        dialogClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetSelection();
                alertDialog.dismiss();
            }
        });
        alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE  | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
        alertDialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        mReadingEt.requestFocus();
        alertDialog.show();
    }

    private void resetSelection(){
        selectedPosition=-1;
        notifyDataSetChanged();
    }
    private void navigateToDeliveryMenu() {
        mContext.startActivity(new Intent(mContext, DeliveryMenuActivity.class));
    }


    private void createVisitAndUpdateMile(CUSTOMER_HEADER customer_header,float currentReading)
    {
        try {
            String ship_no = DBHelper.getInstance().getCurrentShipment().getSHIP_NO();
            //String ship_no = "11312033";
            VISIT visit = new VISIT();
            visit.setLid(visit.getLid());
            visit.setFid(DBHelper.getInstance().getCurrentShipment().getLid());
            visit.setSHIP_NO(ship_no);
            visit.setCUST_NO(customer_header.getCUST_NO());
            visit.setHHT("X");
            visit.setSEQ_NO((long) TripSummaryController.getInstance().getNextVisitNo());
            visit.setVISIT_NO(visit.getSEQ_NO());
            visit.setMILE((double) currentReading);
            visit.setSTAT((long) Constants.VISIT_STATUS_CHECKLIST_PRINTED);
            TripSummaryController.getInstance().setCurrentVisitRow(visit);
            TripSummaryController.getInstance().insertOrUpdateVisit(visit);
        } catch (DBException e) {
            Logger.e("", e);
        }
//        visitRow.LID = Util.GetGuid();
//        visitRow.FID = ShipmentController.GetShipmentRow().LID;
//        visitRow.SHIP_NO = ShipmentController.GetShipmentRow().SHIP_NO;
//        visitRow.CUST_NO = advancedListUnplnCustomerList.SelectedRow[2].ToString();
//        visitRow.HHT = "X";
//        visitRow.SEQ_NO = TripSummaryController.GetNextVisitNo();
//        visitRow.VISIT_NO = visitRow.SEQ_NO;
//        visitRow.MILE = currentReading;
//        visitRow.STAT = OASISConstants.VISIT_STATUS_CHECKLIST_PRINTED;
//
//        TripSummaryController.UpdateVisit(visitHeaderDataTable, visitRow);
    }



    @Override
    public int getItemCount() { return filteredCustomerHeaders.size(); }

    public void filter(String text) {
        if(!text.isEmpty()) {
            List<CUSTOMER_HEADER> filterdNames = new ArrayList<>();
            for (CUSTOMER_HEADER s : customerHeaders) {
                if (s.getCUST_NO().toLowerCase().contains(text.toLowerCase()) || s.getNAME().toLowerCase().contains(text.toLowerCase())) {

                    filterdNames.add(s);
                }
            }
            filterList(filterdNames);
        }else {
            filterList(customerHeaders);
        }
    }

    public void filterList(List<CUSTOMER_HEADER> filteredCustomerHeaders) {
        selectedPosition=-1;
        this.filteredCustomerHeaders = filteredCustomerHeaders;
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView mMap;
        RelativeLayout mBackground;
        TextView mSno,mName,mOutlet,mAddress,mContact,mTelNo,mVisitWindow;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mMap=itemView.findViewById(R.id.xUnplannedCustomerMapImg);
            mSno=itemView.findViewById(R.id.xUnplannedCustomerSingleItemIndex);
            mName=itemView.findViewById(R.id.xUnplannedCustomerSingleItemName);
            mOutlet=itemView.findViewById(R.id.xUnplannedCustomerSingleItemOutlet);
            mAddress=itemView.findViewById(R.id.xUnplannedCustomerSingleItemAddress);
            mContact=itemView.findViewById(R.id.xUnplannedCustomerSingleItemContact);
            mTelNo=itemView.findViewById(R.id.xUnplannedCustomerSingleItemTelNo);
            mVisitWindow=itemView.findViewById(R.id.xUnplannedCustomerSingleItemTimeWindow);
            mBackground=itemView.findViewById(R.id.xUnplannedCustomerSingleItemBg);
        }
    }
}
