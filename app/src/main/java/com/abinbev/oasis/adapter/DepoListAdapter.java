package com.abinbev.oasis.adapter;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Color;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import androidx.annotation.NonNull;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.abinbev.oasis.activities.ShipmentListActivity;
import com.abinbev.oasis.be.DEPOT_HEADER;

import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.unvired.oasis.R;
import com.unvired.utils.Context;

import java.util.List;

public class DepoListAdapter extends  RecyclerView.Adapter<DepoListAdapter.ViewHolder> {

    public class ViewHolder extends RecyclerView.ViewHolder {
        public TextView depotName;
        public TextView depot;
        public LinearLayout rootLayout;

        public ViewHolder(View itemView) {
            super(itemView);
            rootLayout = (LinearLayout) itemView.findViewById(R.id.rootLayout);
            depotName = (TextView) itemView.findViewById(R.id.depotName);
            depot = (TextView) itemView.findViewById(R.id.depot);
        }
    }

    private List<DEPOT_HEADER> depotHeaderList = null;
    private AppCompatActivity activity;
    public int selectedPosition=-1;
    private Context mContext;
    private final DialogMessage dialogMessage;
    public DepoListAdapter(AppCompatActivity activity, List<DEPOT_HEADER> depotHeaders, int mode) {
        this.depotHeaderList = depotHeaders;
        this.activity = activity;
        this.dialogMessage=new DialogMessage(activity);
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.content_depot_list, parent, false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, final int position) {
        final DEPOT_HEADER depotHeader = depotHeaderList.get(position);
        viewHolder.rootLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                selectedPosition = position;
                notifyDataSetChanged();
                dialogMessage.showDialogWithPositiveAndNegativeBtn("", "You selected Depot \n\n\"" + depotHeader.getNAME() + "\"", activity.getString(R.string.continue_label), activity.getString(R.string.cancel), new DialogClickListner() {
                    @Override
                    public void onPositiveClick(Dialog dialogMessage)
                    {
                        dialogMessage.dismiss();
                        depotHeader.setSELECTED("X");
                        DBHelper.getInstance().insertOrUpdatePerson(depotHeader);
                        Intent intent = new Intent(activity, ShipmentListActivity.class);
                        intent.putExtra("DEPOT", depotHeader.getDEPOT());
                        activity.startActivity(intent);
                    }

                    @Override
                    public void onNegativeClick(Dialog dialogMessage) {
                        dialogMessage.dismiss();
                    }

                    @Override
                    public void onNeutralClick(Dialog dialogMessage) {

                    }
                });

            }
        });

        if(selectedPosition == position) {
            viewHolder.itemView.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background_heighlight))));
        } else {
            viewHolder.itemView.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background))));
        }
        if (depotHeader != null) {
            viewHolder.depotName.setText(depotHeader.getNAME());
            viewHolder.depot.setText(depotHeader.getDEPOT());
        }
    }

    @Override
    public int getItemCount() {
        if (depotHeaderList != null && depotHeaderList.size() > 0) {
            return depotHeaderList.size();
        } else
            return 0;
    }


}
