package com.abinbev.oasis.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abinbev.oasis.be.DELIVERY;
import com.abinbev.oasis.be.TRL_CUST_MAP_HEADER;
import com.abinbev.oasis.R;

import java.util.List;

public class TrailerDropDownAdapter extends ArrayAdapter<TRL_CUST_MAP_HEADER> {

    private Context mContext;
    private List<TRL_CUST_MAP_HEADER> trl_cust_map_headerList;
    private boolean showPlaceholder;
    private int mSelectedIndex = -1;
    public TrailerDropDownAdapter(@NonNull Context context, int resource, @NonNull List<TRL_CUST_MAP_HEADER> objects,boolean showPlaceholder) {
        super(context, resource, objects);
        this.mContext=context;
        this.trl_cust_map_headerList=objects;
        this.showPlaceholder=showPlaceholder;
    }

    @Override
    public int getCount() {
        if(showPlaceholder){
            return trl_cust_map_headerList.size()+1;
        }else {
            return trl_cust_map_headerList.size();
        }
    }

    @Nullable
    @Override
    public TRL_CUST_MAP_HEADER getItem(int position) {
        if(showPlaceholder){
            if(position==0){
                return null;
            }else {
                return trl_cust_map_headerList.get(position-1);

            }
        }else {
            return trl_cust_map_headerList.get(position);
        }
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        if(!showPlaceholder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
            View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
            divider.setVisibility(View.GONE);  label.setTextColor(Color.BLACK);
            label.setText(trl_cust_map_headerList.get(position).getTRAILER_ID());
            mRootLayout.setBackgroundResource(R.color.table_bg_color);
            mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            label.setTextColor(Color.WHITE);
            label.setGravity(Gravity.CENTER_VERTICAL);
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setText("");
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);  label.setTextColor(Color.BLACK);
                label.setText(trl_cust_map_headerList.get(position-1).getTRAILER_ID());
                mRootLayout.setBackgroundResource(R.color.table_bg_color);
                mRootLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                label.setTextColor(Color.WHITE);
                label.setGravity(Gravity.CENTER_VERTICAL);
                return convertView;
            }
        }
    }
    @Override
    public View getDropDownView(int position, View convertView,
                                ViewGroup parent) {
        if(!showPlaceholder) {
            convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
            TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
            RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
            label.setText(trl_cust_map_headerList.get(position).getTRAILER_ID());
            if (position == mSelectedIndex) {
                mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
            } else {
                mRootLayout.setBackgroundResource(R.color.white);
            }
            return convertView;
        }else {
            if(position==0){
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                View divider = convertView.findViewById(R.id.xReasonDropDownDiv);
                divider.setVisibility(View.GONE);
                label.setText("Select trailer");
                label.setTypeface(null, Typeface.BOLD);
                label.setGravity(Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL);
                label.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                return convertView;
            }else {
                convertView= LayoutInflater.from(mContext).inflate(R.layout.spinner_placeholder_layout,null);
                TextView label = (TextView) convertView.findViewById(R.id.xSpinnerPlaceholder);
                RelativeLayout mRootLayout = convertView.findViewById(R.id.xReasonDropDownRootLayout);
                label.setText(trl_cust_map_headerList.get(position-1).getTRAILER_ID());
                if (position == mSelectedIndex) {
                    mRootLayout.setBackgroundResource(R.color.depot_list_item_background_heighlight);
                } else {
                    mRootLayout.setBackgroundResource(R.color.white);
                }
                return convertView;
            }
        }
    }


    public int getPositionOfObject(TRL_CUST_MAP_HEADER trl_cust_map_header){
        return trl_cust_map_headerList.indexOf(trl_cust_map_header);
    }

    public void setSelectionPosition(int position) {
        mSelectedIndex =  position;
        notifyDataSetChanged();
    }
}
