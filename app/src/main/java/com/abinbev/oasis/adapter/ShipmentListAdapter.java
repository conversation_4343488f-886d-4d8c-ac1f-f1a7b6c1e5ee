package com.abinbev.oasis.adapter;

import android.app.Dialog;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.abinbev.oasis.activities.PreTripInspectionQuestionsActivity;
import com.abinbev.oasis.activities.StartUpActivity;
import com.abinbev.oasis.be.INPUT_GET_SHIPMENT_HEADER;
import com.abinbev.oasis.be.SHIPMENT_HEADER;
import com.abinbev.oasis.util.Constants;
import com.abinbev.oasis.util.Controllers.DBHelper;
import com.abinbev.oasis.util.DataHelper;
import com.abinbev.oasis.util.Dialog.DialogClickListner;
import com.abinbev.oasis.util.Dialog.DialogMessage;
import com.abinbev.oasis.util.Loader.Loader;
import com.abinbev.oasis.util.PAHelper;
import com.abinbev.oasis.util.SnackBarToast;
import com.abinbev.oasis.util.Utils;
import com.unvired.database.DBException;
import com.unvired.database.IDataStructure;
import com.unvired.logger.Logger;
import com.unvired.model.InfoMessage;
import com.unvired.oasis.R;
import com.unvired.sync.out.ISyncAppCallback;
import com.unvired.sync.response.ISyncResponse;
import com.unvired.sync.response.SyncBEResponse;
import com.unvired.core.UserSettingsManager;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;
import java.util.Vector;

public class ShipmentListAdapter extends  RecyclerView.Adapter<ShipmentListAdapter.ViewHolder> implements DialogClickListner {

    public class ViewHolder extends RecyclerView.ViewHolder {
        public TextView shipmentNo, driverNmae, truckNo, crew, trip, trailer1, trailer2;
        public LinearLayout rootLayout;
        public CardView itemCard;

        public ViewHolder(View itemView) {
            super(itemView);
            rootLayout = (LinearLayout) itemView.findViewById(R.id.rootLayout);
            itemCard = (CardView) itemView.findViewById(R.id.itemCard);
            shipmentNo = (TextView) itemView.findViewById(R.id.shipmentNO);
            truckNo = (TextView) itemView.findViewById(R.id.truckNo);
            driverNmae = (TextView) itemView.findViewById(R.id.driverName);
            crew = (TextView) itemView.findViewById(R.id.crew);
            trip = (TextView) itemView.findViewById(R.id.trip);
            trailer1 = (TextView) itemView.findViewById(R.id.trailer1);
            trailer2 = (TextView) itemView.findViewById(R.id.trailer2);
        }
    }

    private List<SHIPMENT_HEADER> shipmentHeaderList = null;
    private List<SHIPMENT_HEADER> tempShipmentHeaderList = null;
    private AppCompatActivity activity;
    private INPUT_GET_SHIPMENT_HEADER header;
    private String depotNo;

    private String responseCode;
    private String responseText;
    private SHIPMENT_HEADER selectedShipment;
    public int selectedPosition=-1;
    private final DialogMessage dialogMessage;

    public ShipmentListAdapter(AppCompatActivity activity, List<SHIPMENT_HEADER> shipmentHeaders, String depotNo) {
        this.shipmentHeaderList = shipmentHeaders;
        this.activity = activity;
        this.depotNo = depotNo;
        this.dialogMessage = new DialogMessage(activity);



    }

    @Override
    public ShipmentListAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.content_shipment_list

                , parent, false);
        return new ShipmentListAdapter.ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(ShipmentListAdapter.ViewHolder viewHolder, final int position) {
        final SHIPMENT_HEADER shipmentHeader = shipmentHeaderList.get(position);
        viewHolder.rootLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                selectedPosition = position;
                notifyDataSetChanged();
                dialogMessage.showDialogWithPositiveAndNegativeBtn("", "Do you want to download \nshipment No - " + shipmentHeader.getSHIP_NO(), activity.getString(R.string.continue_label), activity.getString(R.string.cancel), new DialogClickListner() {
                            @Override
                            public void onPositiveClick(Dialog dialogMessage) {
                                dialogMessage.dismiss();
                                selectedShipment = shipmentHeader;
                                Loader.showLoader(activity, "Please Wait...");
                                try {
                                    header = new INPUT_GET_SHIPMENT_HEADER();
                                    header.setDEPOT_NO(depotNo);
                                    header.setSHIP_NO(shipmentHeader.getSHIP_NO());
                                    header.setMOBILE_USER(UserSettingsManager.getInstance().getUnviredUserId());
                                    header.setDNLD_MASTERDATA("X");
                                    downloadShipments(header);

                                } catch (DBException e) {
                                    Logger.e("", e);
                                }
                            }

                            @Override
                            public void onNegativeClick(Dialog dialogMessage) {
                                dialogMessage.dismiss();
                            }

                            @Override
                            public void onNeutralClick(Dialog dialogMessage) {

                            }
                        });

            }
        });
        if(selectedPosition == position) {
            viewHolder.itemView.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background_heighlight))));
            viewHolder.itemCard.setCardBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background_heighlight))));
        } else {
            viewHolder.itemView.setBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background))));
            viewHolder.itemCard.setCardBackgroundColor(Color.parseColor("#" + Integer.toHexString( ContextCompat.getColor(activity, R.color.depot_list_item_background))));
        }
        if (shipmentHeader != null) {
            viewHolder.shipmentNo.setText(shipmentHeader.getSHIP_NO());
            viewHolder.truckNo.setText(shipmentHeader.getTRUCK());
            viewHolder.driverNmae.setText(shipmentHeader.getDRV1_NAME());
            viewHolder.trailer1.setText(shipmentHeader.getTRL1());
            viewHolder.trailer2.setText(shipmentHeader.getTRL2());
            viewHolder.crew.setText(shipmentHeader.getCREW().toString());
            viewHolder.trip.setText(shipmentHeader.getTRIP().toString());

        }
    }

    @Override
    public int getItemCount() {
        if (shipmentHeaderList != null && shipmentHeaderList.size() > 0) {
            return shipmentHeaderList.size();
        } else
            return 0;
    }

    @Override
    public void onPositiveClick(Dialog dialogMessage) {
    }

    @Override
    public void onNegativeClick(Dialog dialogMessage) {
        dialogMessage.dismiss();
    }

    @Override
    public void onNeutralClick(Dialog dialogMessage) {

    }

    private void downloadShipments(final INPUT_GET_SHIPMENT_HEADER header) {

        tempShipmentHeaderList = new ArrayList<>();

        final ISyncAppCallback callback = new ISyncAppCallback() {
            @Override
            public void onResponse(ISyncResponse iSyncResponse) {

//                Loader.showLoader(context, getString(R.string.downloading_shipments_list));

                SyncBEResponse syncBEResponse;
                responseText = null;

                if (iSyncResponse == null) {
                    responseCode = Constants.RESPONSE_CODE_ERROR;
                    responseText = activity.getResources().getString(R.string.invalidResponse);
                } else {

                    switch (iSyncResponse.getResponseStatus()) {
                        case SUCCESS:

                            if (iSyncResponse instanceof SyncBEResponse) {

                                syncBEResponse = (SyncBEResponse) iSyncResponse;

                                responseCode = Constants.RESPONSE_CODE_SUCCESSFUL;
                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        responseCode = infoMessages.get(i).getCategory().equals(InfoMessage.CATEGORY_SUCCESS) ? Constants.RESPONSE_CODE_SUCCESSFUL : Constants.RESPONSE_CODE_ERROR;

                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }

                                    responseText = infoMsgs.toString();
                                }

                                if (responseCode.equals(Constants.RESPONSE_CODE_SUCCESSFUL)) {
                                    Hashtable<String, Hashtable<IDataStructure, Vector<IDataStructure>>> dataBEs = syncBEResponse.getDataBEs();
                                    Hashtable<IDataStructure, Vector<IDataStructure>> tempCollectionOfHeaderAndItems = null;

                                    if (!dataBEs.isEmpty()) {

                                        Enumeration<String> beKeys = dataBEs.keys();

                                        if (beKeys.hasMoreElements()) {
                                            String customerBEName = beKeys.nextElement();
//                                            if(customerBEName.equalsIgnoreCase(Constants.BE_SHIPMENT)) {
                                                tempCollectionOfHeaderAndItems = dataBEs.get(Constants.BE_SHIPMENT);

                                                Enumeration<IDataStructure> depotHeaderKeys = tempCollectionOfHeaderAndItems.keys();

                                                while (depotHeaderKeys.hasMoreElements()) {
                                                    SHIPMENT_HEADER shipment = (SHIPMENT_HEADER) depotHeaderKeys.nextElement();
                                                    tempShipmentHeaderList.add(shipment);

//                                                }
                                                }
                                        }
                                    }
                                    StartUpActivity.initialSettings();
                                    DBHelper.getInstance().createNewTime(selectedShipment.getSHIP_NO(),Constants.TIME_TYPE_SHIP_DNLD_START, Utils.getCurrentTimeStamp());
                                    DBHelper.getInstance().createNewTime(selectedShipment.getSHIP_NO(),Constants.TIME_TYPE_SHIP_DNLD_END);
                                }
                                //System.out.println(tempShipmentHeaderList.size() + " ---------------------- " + tempShipmentHeaderList.get(0).getSHIP_NO());
                                if (responseText == null || responseText.trim().isEmpty()) {
                                    if(tempShipmentHeaderList.size() > 0) {
                                        responseText = "You downloded \nshipment No - " + tempShipmentHeaderList.get(0).getSHIP_NO() + "\n for \ndriver " + tempShipmentHeaderList.get(0).getDRV1_NAME() + "\n and \ntruck " + tempShipmentHeaderList.get(0).getTRUCK();
                                    } else {
                                        responseText = "You downloded \nshipment No - " + selectedShipment.getSHIP_NO() + "\n for \ndriver " + selectedShipment.getDRV1_NAME() + "\n and \ntruck " + selectedShipment.getTRUCK();
                                    }
                                }

                                Logger.i(responseText);
                            }
                            break;

                        case FAILURE:
                            responseCode = Constants.RESPONSE_CODE_ERROR;

                            if (iSyncResponse instanceof SyncBEResponse) {
                                syncBEResponse = (SyncBEResponse) iSyncResponse;
                                responseText = syncBEResponse.getErrorMessage();

                                if (syncBEResponse.getErrorMessage().contains(activity.getResources().getString(R.string.invalidResponse))) {
                                    responseText = activity.getResources().getString(R.string.invalidResponse);
                                } else {
                                    responseText = syncBEResponse.getErrorMessage();
                                }

                                Vector<InfoMessage> infoMessages = syncBEResponse.getInfoMessages();

                                if (infoMessages != null && infoMessages.size() > 0) {
                                    StringBuilder infoMsgs = new StringBuilder();

                                    for (int i = 0; i < infoMessages.size(); i++) {
                                        if (infoMessages.get(i).getMessage() != null && !infoMessages.get(i).getMessage().equals("")) {
                                            infoMsgs.append(infoMessages.get(i).getMessage() + "\n");
                                        }
                                    }
                                    responseText = infoMsgs.toString();
                                }

                                if (responseText.trim().isEmpty())
                                    responseText = activity.getResources().getString(R.string.invalidResponse);

                            } else {
                                responseText = activity.getResources().getString(R.string.invalidResponse);
                            }
                            Logger.i("SHIPMENT DOWNLOAD ERROR: "   + responseText);
                            break;
                    }

                    if (responseCode != null && responseCode.equalsIgnoreCase(Constants.RESPONSE_CODE_ERROR)) {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showInfo(responseText);
//                                loadShipmentsList();
                            }
                        });
                    } else {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showInfo(responseText);
                            }
                        });
                    }
                }
            }
        };
        new Thread(new Runnable() {
            @Override
            public void run() {
                PAHelper.getShipment(header, callback);
            }
        }).start();
    }

    private void showInfo(String msg) {

        dialogMessage.showDialogWithPositive("", msg, activity.getString(R.string.continue_label), new DialogClickListner() {
            @Override
            public void onPositiveClick(Dialog dialogMessage) {
                SHIPMENT_HEADER currentShipmentHeader = DBHelper.getInstance().getCurrentShipment();
                if(currentShipmentHeader == null) {
                    SnackBarToast.showMessage(activity.getApplicationContext(), "Shipment data is not complete or incorrect");
                    dialogMessage.dismiss();
                    Loader.hideLoader();
                    return;
                }
                dialogMessage.dismiss();
                SHIPMENT_HEADER shipmentHeaderSelected = tempShipmentHeaderList.get(0);
                shipmentHeaderSelected.setSTAT((long) Constants.SHIP_STATUS_NEW);
                DataHelper.getInstance().OSB_addDeliveryItemsOnEmptyCollectionDelivery();
                DBHelper.getInstance().updateShipmentStatus(shipmentHeaderSelected);
                Loader.hideLoader();
                //DataHelper.getInstance().OSB_addDeliveryItemsOnEmptyCollectionDelivery();
                DataHelper.getInstance().setShipmentHeader(tempShipmentHeaderList.get(0));
                Intent intent = new Intent(activity, PreTripInspectionQuestionsActivity.class);
                intent.putExtra(Constants.SCREEN_NAVIGATION,Constants.SCREEN_NAVIGATION_MODE.PretripInspection);
                activity.startActivity(intent);
            }

            @Override
            public void onNegativeClick(Dialog dialogMessage) {
                dialogMessage.dismiss();
            }

            @Override
            public void onNeutralClick(Dialog dialogMessage) {

            }
        });
    }


}
