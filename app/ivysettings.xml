<?xml version="1.0" encoding="UTF-8"?>
<ivy-settings>
  <settings defaultResolver="main" />
  <!-- Authentication required for publishing (deployment). 'Artifactory Realm' is the realm used by Artifactory so don't change it.-->
  <credentials host="projectserver" realm="Artifactory Realm" username="hudson" passwd="INDhudson"/>
  <resolvers>
      <chain name="main">
        <ibiblio name="public" m2compatible="true" root="http://projectserver:8080/artifactory/${pub.repo}" />
        <url name="artifactory-publish">
            <artifact pattern=
                         "http://projectserver:8080/artifactory/${pub.repodir}/[organization]/[module]/[revision]/[artifact]-[revision].[ext]"/>
            <ivy pattern="http://projectserver:8080/artifactory/${pub.repodir}/[organization]/[module]/[revision]/ivy-[revision].xml" />            
        </url> 
    </chain>
  </resolvers>
</ivy-settings>

