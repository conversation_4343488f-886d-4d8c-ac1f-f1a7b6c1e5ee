<?xml version="1.0" encoding="UTF-8"?>
<project name="App_ABINBEV_OASIS_Android" default="prepare" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
 
	<!-- Get the release number -->
	<target name="getbuildno">
		<property environment="env" />

		<java jar="/Users/<USER>/Jenkins/BuildNo/BuildNo.jar" fork="true" failonerror="true" maxmemory="128m">
			<arg value="ABINBEV_OASIS"/>
			<arg value="-r=true"/>
			<arg value="-n=true"/>
		</java>

		<!-- Now read into the build numberfile into release.str property -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Using release number : ${release.str}"/>
	</target>


    <!-- Compiles this project's .java files into .class files. -->
    <target name="prepare">
		<!-- Release string to be written -->
		<antcall target="getbuildno"/>

		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<property environment="env" />
		<echo message="BUILD_NUMBER - BUILD_ID	: ${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}"/>
		<echo message="JAVA_HOME				: ${env.JAVA_HOME}"/>
		<echo message="BUILD_URL				: ${env.BUILD_URL}"/>
		<echo message="GIT_COMMIT				: ${env.GIT_COMMIT}"/>
		<echo message="RELEASE_NUMBER			: ${release.str}"/>
		<echo message="GIT_URL					: ${env.GIT_URL}"/>

<!--		<delete file="${env.WORKSPACE}/app/build.gradle"/>-->
<!--        <move file="${env.WORKSPACE}/app/build.gradle.jenkins" tofile="${env.WORKSPACE}/app/build.gradle"/>-->
        <copy file="${env.WORKSPACE}/keys/sablimited.keystore" overwrite="true" todir="${env.WORKSPACE}/app" />
		<replace file="${env.WORKSPACE}/app/build.gradle" token="99.99.99" value='${release.str}'/>
    </target>

</project>
