{"project_info": {"project_number": "276736475261", "firebase_url": "https://abinbev-apps.firebaseio.com", "project_id": "abinbev-apps", "storage_bucket": "abinbev-apps.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:276736475261:android:519778f02f235b6b", "android_client_info": {"package_name": "co.za.abinbev.msts"}}, "oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCKFEyo-FG17JtUijrUYmnDpbPzmUFB9FM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:276736475261:android:a81b8417dbfed75c", "android_client_info": {"package_name": "co.za.abinbev.nsts"}}, "oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCKFEyo-FG17JtUijrUYmnDpbPzmUFB9FM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:276736475261:android:1990569e4aff941a", "android_client_info": {"package_name": "co.za.sablimited.sabsalestoolkit"}}, "oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCKFEyo-FG17JtUijrUYmnDpbPzmUFB9FM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:276736475261:android:72d76f0275281965eb7114", "android_client_info": {"package_name": "com.abinbev.oasis"}}, "oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCKFEyo-FG17JtUijrUYmnDpbPzmUFB9FM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "276736475261-s8bv2mmqst7lob35uuneaisu4gijfo5u.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}