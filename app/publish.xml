<?xml version="1.0" encoding="UTF-8"?>
<project name="ABINBEV_OASIS_Android" default="publish" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">

	<target name="package">

        <!-- Release string to be written -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

    	<mkdir dir="dist"/>
        <copy file="build/outputs/apk/release/app-release.apk" overwrite="true" todir="dist" />
		<move file="dist/app-release.apk" tofile="dist/ABINBEV_OASIS_Android.apk"/>
    </target>

	<target name="publish" depends="package">
		<!-- =================================================================== -->
		<!-- Set Artifactory target depending on release build or nightly        -->
		<!-- =================================================================== -->
		<property environment="env"/>

		<property name="pub.repo" value="apps-release"/>
		<property name="pub.repodir" value="custom-apps-release"/>

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Publishing to artifactory repository: ${pub.repodir}"/>
		<ivy:resolve revision="${release.str}"/>
		<ivy:publish resolver="artifactory-publish" revision="${release.str}" update="true" publishivy="false">
			<artifacts pattern="dist/[artifact].[ext]" />
		</ivy:publish>
	</target>
</project>
