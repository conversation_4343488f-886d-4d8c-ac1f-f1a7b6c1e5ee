#!/bin/bash

# <PERSON><PERSON>t to view Android logcat logs for the OASIS app

# Kill ADB server first to ensure a clean connection
~/Library/Android/sdk/platform-tools/adb kill-server
~/Library/Android/sdk/platform-tools/adb start-server

# Clear existing logs
~/Library/Android/sdk/platform-tools/adb logcat -c

# View logs, filtering for the OASIS app
# You can modify the filter as needed
echo "Viewing logcat for com.abinbev.oasis..."
echo "Press Ctrl+C to exit"
echo ""

# Option 1: Filter by package name
~/Library/Android/sdk/platform-tools/adb logcat | grep -i -E "oasis|abinbev|unvired"

# Option 2: If the above doesn't show enough logs, comment it out and uncomment this:
# ~/Library/Android/sdk/platform-tools/adb logcat *:D | grep -i -E "oasis|abinbev|unvired"
