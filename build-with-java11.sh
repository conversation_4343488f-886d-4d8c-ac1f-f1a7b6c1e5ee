#!/bin/bash

# Save current JAVA_HOME
OLD_JAVA_HOME=$JAVA_HOME

# Set JAVA_HOME to Java 11
export JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-11.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH

echo "Using Java 11 for build:"
java -version

# Run Gradle command
./gradlew "$@"

# Restore original JAVA_HOME
export JAVA_HOME=$OLD_JAVA_HOME
export PATH=$JAVA_HOME/bin:$PATH

echo "Restored to original Java version:"
java -version
