#!/bin/bash

# Save current JAVA_HOME
OLD_JAVA_HOME=$JAVA_HOME

# Set JAVA_HOME to Java 11
export JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-11.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH

echo "Using Java 11 for build:"
java -version

# Run Gradle command with error handling
echo "Running Gradle command: ./gradlew $@"
./gradlew "$@"
GRADLE_EXIT_CODE=$?

if [ $GRADLE_EXIT_CODE -ne 0 ]; then
    echo "Gradle build failed with exit code: $GRADLE_EXIT_CODE"
    exit $GRADLE_EXIT_CODE
fi

echo "Gradle build completed successfully"

# Restore original JAVA_HOME
export JAVA_HOME=$OLD_JAVA_HOME
export PATH=$JAVA_HOME/bin:$PATH

echo "Restored to original Java version:"
java -version
