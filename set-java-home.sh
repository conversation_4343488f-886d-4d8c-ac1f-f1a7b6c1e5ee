#!/bin/bash

# This script sets JAVA_HOME to Java 11 for this project
# Run this script with 'source set-java-home.sh' before running gradle commands

# Save current JAVA_HOME if it exists
if [ -n "$JAVA_HOME" ]; then
  export OLD_JAVA_HOME=$JAVA_HOME
fi

# Set JAVA_HOME to Java 11
export JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-11.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH

echo "Java version set to:"
java -version
echo ""
echo "You can now run Gradle commands directly:"
echo "./gradlew build"
echo ""
echo "To restore your original Java version, run:"
echo "source restore-java-home.sh"
