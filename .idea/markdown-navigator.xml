<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MarkdownProjectSettings">
    <PreviewSettings splitEditorLayout="SPLIT" splitEditorPreview="PREVIEW" useGrayscaleRendering="false" zoomFactor="1.25" maxImageWidth="0" synchronizePreviewPosition="true" highlightPreviewType="LINE" highlightFadeOut="5" highlightOnTyping="true" synchronizeSourcePosition="true" verticallyAlignSourceAndPreviewSyncPosition="true" showSearchHighlightsInPreview="true" showSelectionInPreview="true" lastLayoutSetsDefault="false">
      <PanelProvider>
        <provider providerId="com.vladsch.md.nav.editor.swing.html.panel" providerName="Default - Swing" />
      </PanelProvider>
    </PreviewSettings>
    <ParserSettings gitHubSyntaxChange="false" correctedInvalidSettings="false" emojiShortcuts="1" emojiImages="0">
      <PegdownExtensions>
        <option name="ATXHEADERSPACE" value="true" />
        <option name="FENCED_CODE_BLOCKS" value="true" />
        <option name="INTELLIJ_DUMMY_IDENTIFIER" value="true" />
        <option name="RELAXEDHRULES" value="true" />
        <option name="STRIKETHROUGH" value="true" />
        <option name="TABLES" value="true" />
        <option name="TASKLISTITEMS" value="true" />
      </PegdownExtensions>
      <ParserOptions>
        <option name="COMMONMARK_LISTS" value="true" />
        <option name="EMOJI_SHORTCUTS" value="true" />
        <option name="GFM_TABLE_RENDERING" value="true" />
        <option name="PRODUCTION_SPEC_PARSER" value="true" />
        <option name="SIM_TOC_BLANK_LINE_SPACER" value="true" />
      </ParserOptions>
    </ParserSettings>
    <HtmlSettings headerTopEnabled="false" headerBottomEnabled="false" bodyTopEnabled="false" bodyBottomEnabled="false" addPageHeader="false" addAnchorLinks="false" anchorLinksWrapText="false" imageUriSerials="false" addDocTypeHtml="true" noParaTags="false" defaultUrlTitle="false" migratedPlantUml="true" migratedAnchorLinks="true" plantUmlConversion="0">
      <GeneratorProvider>
        <provider providerId="com.vladsch.md.nav.editor.text.html.generator" providerName="Unmodified HTML Generator" />
      </GeneratorProvider>
      <headerTop />
      <headerBottom />
      <bodyTop />
      <bodyBottom />
      <fencedCodeConversions>
        <option name="c4plantuml" value="NONE" />
        <option name="ditaa" value="NONE" />
        <option name="erd" value="NONE" />
        <option name="graphviz" value="NONE" />
        <option name="latex" value="KATEX" />
        <option name="math" value="KATEX" />
        <option name="mermaid" value="NONE" />
        <option name="nomnoml" value="NONE" />
        <option name="plantuml" value="NONE" />
        <option name="puml" value="NONE" />
        <option name="svgbob" value="NONE" />
        <option name="umlet" value="NONE" />
        <option name="vega" value="NONE" />
        <option name="vegalite" value="NONE" />
        <option name="wavedrom" value="NONE" />
      </fencedCodeConversions>
    </HtmlSettings>
    <CssSettings previewScheme="UI_SCHEME" cssUri="" isCssUriEnabled="false" isCssUriSerial="true" isCssTextEnabled="false" isDynamicPageWidth="true">
      <StylesheetProvider>
        <provider providerId="com.vladsch.md.nav.editor.text.html.css" providerName="No Stylesheet" />
      </StylesheetProvider>
      <ScriptProviders />
      <cssText />
      <cssUriHistory />
    </CssSettings>
  </component>
</project>