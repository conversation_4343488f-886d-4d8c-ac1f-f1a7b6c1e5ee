<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Google" />
      <option name="name" value="Google" />
      <option name="url" value="https://maven.google.com/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="BintrayJCenter" />
      <option name="name" value="BintrayJCenter" />
      <option name="url" value="https://jcenter.bintray.com/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/google/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/google/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\google\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/google/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/android/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/android/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="name" value="C:\Users\<USER>\AppData\Local\Android\Sdk\extras\android\m2repository" />
      <option name="url" value="file:/$PROJECT_DIR$/../AppData/Local/Android/Sdk/extras/android/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/android/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/google/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository" />
      <option name="name" value="$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository" />
      <option name="url" value="file:$PROJECT_DIR$/../Library/Android/sdk/extras/m2repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Google" />
      <option name="name" value="Google" />
      <option name="url" value="https://dl.google.com/dl/android/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven" />
      <option name="name" value="maven" />
      <option name="url" value="https://jitpack.io" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="MavenRepo" />
      <option name="name" value="MavenRepo" />
      <option name="url" value="https://repo.maven.apache.org/maven2/" />
    </remote-repository>
  </component>
</project>